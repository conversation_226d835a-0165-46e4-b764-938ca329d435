export interface Template {
  id: string;
  title: string;
  description: string;
  category: string;
  price: number;
  originalPrice?: number;
  imageUrl: string;
  previewUrl?: string;
  downloadUrl?: string;
  tags: string[];
  featured: boolean;
  rating?: number;
  downloads?: number;
  discount?: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface Category {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
  templateCount: number;
}

export interface Order {
  id: string;
  userId: string;
  templateId: string;
  templateTitle: string;
  amount: number;
  status: 'pending' | 'confirmed' | 'declined' | 'completed';
  createdAt: Date;
  updatedAt: Date;
  paymentMethod?: string;
  notes?: string;
}

export interface CustomRequest {
  id: string;
  userId: string;
  userEmail: string;
  title: string;
  description: string;
  category: string;
  budget?: number;
  deadline?: Date;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
  adminNotes?: string;
}

export interface User {
  uid: string;
  email: string;
  role: 'admin' | 'user';
  displayName?: string;
  fullName?: string;
  phoneNumber?: string;
  countryCode?: string;
  createdAt: Date;
  updatedAt?: Date;
  purchasedTemplates?: string[];
}

export interface ContactMessage {
  id: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  status: 'unread' | 'read' | 'replied';
  createdAt: Date;
}

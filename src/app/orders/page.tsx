'use client';

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  Eye, 
  Clock, 
  CheckCircle, 
  XCircle,
  ShoppingBag,
  Calendar,
  DollarSign
} from 'lucide-react';
import Link from 'next/link';

// Mock data for user orders
const userOrders = [
  {
    id: 'ORD-001',
    templateId: '1',
    templateName: 'Modern Dashboard Pro',
    templateImage: '/api/placeholder/100/80',
    amount: 49,
    status: 'completed',
    orderDate: '2024-01-15',
    downloadCount: 3,
    maxDownloads: 5
  },
  {
    id: 'ORD-002',
    templateId: '2',
    templateName: 'E-commerce Store Complete',
    templateImage: '/api/placeholder/100/80',
    amount: 79,
    status: 'confirmed',
    orderDate: '2024-01-10',
    downloadCount: 1,
    maxDownloads: 3
  },
  {
    id: 'ORD-003',
    templateId: '3',
    templateName: 'Landing Page Pro',
    templateImage: '/api/placeholder/100/80',
    amount: 39,
    status: 'pending',
    orderDate: '2024-01-08',
    downloadCount: 0,
    maxDownloads: 5
  },
  {
    id: 'ORD-004',
    templateId: '4',
    templateName: 'Creative Portfolio',
    templateImage: '/api/placeholder/100/80',
    amount: 29,
    status: 'declined',
    orderDate: '2024-01-05',
    downloadCount: 0,
    maxDownloads: 3
  }
];

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    case 'confirmed':
      return <CheckCircle className="h-5 w-5 text-blue-500" />;
    case 'pending':
      return <Clock className="h-5 w-5 text-yellow-500" />;
    case 'declined':
      return <XCircle className="h-5 w-5 text-red-500" />;
    default:
      return <Clock className="h-5 w-5 text-gray-500" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'confirmed':
      return 'bg-blue-100 text-blue-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'declined':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export default function OrdersPage() {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-2xl font-bold mb-4">Please sign in to view your orders</h1>
        <Button asChild>
          <Link href="/auth">Sign In</Link>
        </Button>
      </div>
    );
  }

  const completedOrders = userOrders.filter(order => order.status === 'completed').length;
  const totalSpent = userOrders
    .filter(order => order.status === 'completed' || order.status === 'confirmed')
    .reduce((sum, order) => sum + order.amount, 0);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          My Orders
        </h1>
        <p className="text-gray-600">
          Track your template purchases and downloads
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Orders</p>
                <p className="text-2xl font-bold text-gray-900">{userOrders.length}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <ShoppingBag className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">{completedOrders}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Spent</p>
                <p className="text-2xl font-bold text-gray-900">${totalSpent}</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Orders List */}
      <Card>
        <CardHeader>
          <CardTitle>Order History</CardTitle>
          <CardDescription>
            All your template purchases and their current status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {userOrders.length === 0 ? (
            <div className="text-center py-12">
              <ShoppingBag className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No orders yet</h3>
              <p className="text-gray-600 mb-6">
                Start browsing our premium templates to make your first purchase.
              </p>
              <Button asChild>
                <Link href="/templates">Browse Templates</Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {userOrders.map((order) => (
                <div key={order.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      {/* Template Image */}
                      <div className="w-20 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                        <img 
                          src={order.templateImage} 
                          alt={order.templateName}
                          className="w-full h-full object-cover"
                        />
                      </div>

                      {/* Order Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h3 className="font-semibold text-gray-900 mb-1">
                              {order.templateName}
                            </h3>
                            <p className="text-sm text-gray-600">
                              Order #{order.id}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold text-gray-900">${order.amount}</p>
                            <p className="text-sm text-gray-600 flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              {new Date(order.orderDate).toLocaleDateString()}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-2">
                              {getStatusIcon(order.status)}
                              <Badge className={getStatusColor(order.status)}>
                                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                              </Badge>
                            </div>
                            
                            {(order.status === 'completed' || order.status === 'confirmed') && (
                              <div className="text-sm text-gray-600">
                                Downloads: {order.downloadCount}/{order.maxDownloads}
                              </div>
                            )}
                          </div>

                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline" asChild>
                              <Link href={`/templates/${order.templateId}`}>
                                <Eye className="h-4 w-4 mr-1" />
                                View
                              </Link>
                            </Button>
                            
                            {order.status === 'completed' && (
                              <Button size="sm">
                                <Download className="h-4 w-4 mr-1" />
                                Download
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Help Section */}
      <Card className="mt-8">
        <CardContent className="p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Need Help with Your Order?
            </h3>
            <p className="text-gray-600 mb-4">
              If you have any questions about your orders or need assistance with downloads, we're here to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild variant="outline">
                <Link href="/contact">Contact Support</Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/templates">Browse More Templates</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

"use client"

import { useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Download, ArrowRight, Star, Gift } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"

export default function SuccessPage() {
  const router = useRouter()

  useEffect(() => {
    // Auto redirect after 10 seconds
    const timer = setTimeout(() => {
      router.push('/dashboard')
    }, 10000)

    return () => clearTimeout(timer)
  }, [router])

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full space-y-8">
        {/* Success Animation */}
        <div className="text-center">
          <div className="relative inline-block">
            <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse">
              <CheckCircle className="h-12 w-12 text-green-600" />
            </div>
            <div className="absolute -top-2 -right-2">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center animate-bounce">
                <Gift className="h-4 w-4 text-yellow-600" />
              </div>
            </div>
          </div>
          
          <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            🎉 Purchase Successful!
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            Thank you for your purchase! Your template is ready for download.
          </p>
        </div>

        {/* Purchase Details */}
        <Card className="border-0 shadow-xl">
          <CardHeader className="text-center pb-4">
            <CardTitle className="text-xl">Your Template is Ready!</CardTitle>
            <CardDescription>
              You can now download and start using your premium template
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Template Info */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900">Creative Portfolio Template</h3>
                  <p className="text-sm text-gray-600">Professional portfolio design</p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-green-600">₹1,999</div>
                  <div className="flex items-center gap-1 text-yellow-500">
                    <Star className="h-4 w-4 fill-current" />
                    <span className="text-sm">4.9</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Features Included */}
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">What's Included:</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Source Files</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Documentation</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Lifetime Updates</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Premium Support</span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white h-12">
                <Download className="h-5 w-5 mr-2" />
                Download Template
              </Button>
              
              <div className="grid grid-cols-2 gap-3">
                <Button variant="outline" asChild>
                  <Link href="/dashboard">
                    View Dashboard
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/templates">
                    Browse More
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Link>
                </Button>
              </div>
            </div>

            {/* Next Steps */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Next Steps:</h4>
              <ol className="text-sm text-blue-800 space-y-1">
                <li>1. Download your template files</li>
                <li>2. Read the documentation</li>
                <li>3. Customize to your needs</li>
                <li>4. Launch your project!</li>
              </ol>
            </div>

            {/* Support */}
            <div className="text-center text-sm text-gray-600">
              <p>Need help? <Link href="/contact" className="text-blue-600 hover:underline">Contact our support team</Link></p>
              <p className="mt-2">Redirecting to dashboard in 10 seconds...</p>
            </div>
          </CardContent>
        </Card>

        {/* Special Offer */}
        <Card className="border-2 border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50">
          <CardContent className="p-6 text-center">
            <Badge className="mb-3 bg-yellow-500">
              <Gift className="h-4 w-4 mr-1" />
              Special Offer
            </Badge>
            <h3 className="font-semibold text-gray-900 mb-2">Get 20% off your next purchase!</h3>
            <p className="text-sm text-gray-600 mb-4">
              Use code <code className="bg-yellow-200 px-2 py-1 rounded font-mono">WELCOME20</code> on your next template purchase
            </p>
            <Button variant="outline" size="sm" asChild>
              <Link href="/templates">
                Shop More Templates
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

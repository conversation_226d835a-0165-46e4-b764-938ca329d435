'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  FileText,
  ShoppingCart,
  DollarSign,
  Plus,
  Eye,
  Settings,
  BarChart3,
  Database,
  MessageSquare,
  Palette,
  Download,
  Phone
} from 'lucide-react';
import Link from 'next/link';
import {
  getDashboardStats,
  getCustomRequests,
  getAllUsers,
  updateCustomRequestStatus,
  subscribeToCustomRequests
} from '@/lib/firebaseServices';
import { CustomRequest, User } from '@/types';

interface DashboardStats {
  totalUsers: number;
  totalTemplates: number;
  totalRequests: number;
  pendingRequests: number;
  totalSales: number;
  customizations: number;
}

export default function AdminDashboard() {
  const { user, userData } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalTemplates: 0,
    totalRequests: 0,
    pendingRequests: 0,
    totalSales: 0,
    customizations: 0
  });
  const [customRequests, setCustomRequests] = useState<CustomRequest[]>([]);
  const [recentUsers, setRecentUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('templates');

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch dashboard stats
        const dashboardStats = await getDashboardStats();
        setStats(dashboardStats);

        // Fetch custom requests
        const requests = await getCustomRequests();
        setCustomRequests(requests.slice(0, 5)); // Show only recent 5

        // Fetch recent users
        const users = await getAllUsers();
        setRecentUsers(users.slice(0, 5)); // Show only recent 5

      } catch (error: any) {
        console.error('Error fetching admin data:', error);
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    if (user && userData?.role === 'admin') {
      fetchData();

      // Set up real-time listener for custom requests
      const unsubscribe = subscribeToCustomRequests((requests) => {
        setCustomRequests(requests.slice(0, 5));
      });

      return () => unsubscribe();
    }
  }, [user, userData]);

  const handleUpdateRequestStatus = async (requestId: string, status: CustomRequest['status']) => {
    try {
      await updateCustomRequestStatus(requestId, status);
      // The real-time listener will update the UI automatically
    } catch (error: any) {
      console.error('Error updating request status:', error);
      setError('Failed to update request status');
    }
  };

  if (!user || userData?.role !== 'admin') {
    return (
      <div className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-4">You need admin privileges to access this page.</p>
        <Button asChild>
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Admin Dashboard
              </h1>
              <p className="text-gray-600">
                Manage your marketplace and monitor performance
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Button asChild>
                <Link href="/admin/add-template">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Template
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/admin/setup">
                  <Download className="mr-2 h-4 w-4" />
                  Setup Sample Data
                </Link>
              </Button>
            </div>
          </div>
        </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading dashboard data...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800">{error}</p>
        </div>
      )}

        {/* Stats Cards */}
        {!loading && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            {/* Templates Card */}
            <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm font-medium mb-1">Templates</p>
                    <p className="text-3xl font-bold">{stats.totalTemplates}</p>
                    <p className="text-blue-100 text-xs">Available templates</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <FileText className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Requests Card */}
            <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm font-medium mb-1">Contact Requests</p>
                    <p className="text-3xl font-bold">{stats.totalRequests}</p>
                    <p className="text-green-100 text-xs">Customer inquiries</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <MessageSquare className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Total Sales Card */}
            <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm font-medium mb-1">Total Sales</p>
                    <p className="text-3xl font-bold">{stats.pendingRequests}</p>
                    <p className="text-purple-100 text-xs">No revenue</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <DollarSign className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Customizations Card */}
            <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-100 text-sm font-medium mb-1">Customizations</p>
                    <p className="text-3xl font-bold">{stats.customizations}</p>
                    <p className="text-orange-100 text-xs">Total customizations</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <Palette className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Users Card */}
            <Card className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-cyan-100 text-sm font-medium mb-1">Users</p>
                    <p className="text-3xl font-bold">{stats.totalUsers}</p>
                    <p className="text-cyan-100 text-xs">Site visitors</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Navigation Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('templates')}
                className={`border-b-2 py-2 px-1 text-sm font-medium ${
                  activeTab === 'templates'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Templates Management
              </button>
              <button
                onClick={() => setActiveTab('requests')}
                className={`border-b-2 py-2 px-1 text-sm font-medium ${
                  activeTab === 'requests'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Contact Requests
              </button>
              <button
                onClick={() => setActiveTab('purchases')}
                className={`border-b-2 py-2 px-1 text-sm font-medium ${
                  activeTab === 'purchases'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Purchases
              </button>
              <button
                onClick={() => setActiveTab('customizations')}
                className={`border-b-2 py-2 px-1 text-sm font-medium ${
                  activeTab === 'customizations'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Customizations
              </button>
            </nav>
          </div>
        </div>

        {!loading && (
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Content based on active tab */}
            <div className="lg:col-span-2">
              {activeTab === 'templates' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      Templates Management
                      <Button asChild size="sm">
                        <Link href="/admin/add-template">
                          <Plus className="mr-2 h-4 w-4" />
                          Add Template
                        </Link>
                      </Button>
                    </CardTitle>
                    <CardDescription>
                      Manage your template collection
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Sample template items */}
                      <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                        <div className="flex items-center space-x-4">
                          <div className="w-16 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg"></div>
                          <div>
                            <h4 className="font-medium text-gray-900">SaaS Dashboard Pro</h4>
                            <p className="text-sm text-gray-600">Technology</p>
                            <p className="text-sm text-green-600 font-medium">₹2499</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button size="sm" variant="outline">
                            <Eye className="mr-2 h-4 w-4" />
                            Preview
                          </Button>
                          <Button size="sm" variant="destructive">
                            Delete
                          </Button>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                        <div className="flex items-center space-x-4">
                          <div className="w-16 h-12 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg"></div>
                          <div>
                            <h4 className="font-medium text-gray-900">Free Startup Landing</h4>
                            <p className="text-sm text-gray-600">Business</p>
                            <p className="text-sm text-green-600 font-medium">Free</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button size="sm" variant="outline">
                            <Eye className="mr-2 h-4 w-4" />
                            Preview
                          </Button>
                          <Button size="sm" variant="destructive">
                            Delete
                          </Button>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                        <div className="flex items-center space-x-4">
                          <div className="w-16 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg"></div>
                          <div>
                            <h4 className="font-medium text-gray-900">Education Platform</h4>
                            <p className="text-sm text-gray-600">Education</p>
                            <p className="text-sm text-green-600 font-medium">₹3499</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button size="sm" variant="outline">
                            <Eye className="mr-2 h-4 w-4" />
                            Preview
                          </Button>
                          <Button size="sm" variant="destructive">
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                    <div className="mt-6">
                      <Button asChild variant="outline" className="w-full">
                        <Link href="/templates">View All Templates</Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'requests' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Contact Requests</CardTitle>
                    <CardDescription>
                      Manage customer inquiries and requests
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="p-4 border rounded-lg">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium text-gray-900">John Doe</h4>
                            <p className="text-sm text-gray-600"><EMAIL></p>
                            <p className="text-sm text-gray-500 mt-2">Need a custom e-commerce template for my business...</p>
                          </div>
                          <Badge variant="outline">New</Badge>
                        </div>
                      </div>
                      <div className="p-4 border rounded-lg">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium text-gray-900">Jane Smith</h4>
                            <p className="text-sm text-gray-600"><EMAIL></p>
                            <p className="text-sm text-gray-500 mt-2">Looking for a portfolio template with dark theme...</p>
                          </div>
                          <Badge variant="secondary">In Progress</Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'purchases' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Purchases</CardTitle>
                    <CardDescription>
                      Track template sales and downloads
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium text-gray-900">SaaS Dashboard Pro</h4>
                            <p className="text-sm text-gray-600">Purchased by: <EMAIL></p>
                            <p className="text-sm text-gray-500">2 hours ago</p>
                          </div>
                          <span className="text-green-600 font-medium">₹2499</span>
                        </div>
                      </div>
                      <div className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium text-gray-900">Education Platform</h4>
                            <p className="text-sm text-gray-600">Purchased by: <EMAIL></p>
                            <p className="text-sm text-gray-500">1 day ago</p>
                          </div>
                          <span className="text-green-600 font-medium">₹3499</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'customizations' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Customization Requests</CardTitle>
                    <CardDescription>
                      Manage custom template requests
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="p-4 border rounded-lg">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium text-gray-900">Custom Dashboard</h4>
                            <p className="text-sm text-gray-600">Client: <EMAIL></p>
                            <p className="text-sm text-gray-500 mt-2">Need a custom admin dashboard with specific features...</p>
                          </div>
                          <Badge variant="outline">Pending</Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Quick Actions */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>
                    Common admin tasks
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button
                    onClick={() => setActiveTab('templates')}
                    variant={activeTab === 'templates' ? 'default' : 'outline'}
                    className="w-full justify-start"
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Templates
                  </Button>

                  <Button
                    onClick={() => setActiveTab('requests')}
                    variant={activeTab === 'requests' ? 'default' : 'outline'}
                    className="w-full justify-start"
                  >
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Contact Requests
                  </Button>

                  <Button asChild variant="outline" className="w-full justify-start">
                    <Link href="/templates">
                      <Eye className="mr-2 h-4 w-4" />
                      View Templates
                    </Link>
                  </Button>

                  <Button asChild variant="outline" className="w-full justify-start">
                    <Link href="/contact">
                      <Phone className="mr-2 h-4 w-4" />
                      Contact Page
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              {/* System Status */}
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>System Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Database className="w-4 h-4 mr-2 text-green-600" />
                        <span className="text-sm text-gray-600">Firebase</span>
                      </div>
                      <Badge variant="default" className="bg-green-500">Connected</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Database className="w-4 h-4 mr-2 text-green-600" />
                        <span className="text-sm text-gray-600">Firestore</span>
                      </div>
                      <Badge variant="default" className="bg-green-500">Active</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-2 text-green-600" />
                        <span className="text-sm text-gray-600">Authentication</span>
                      </div>
                      <Badge variant="default" className="bg-green-500">Working</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <FileText className="w-4 h-4 mr-2 text-gray-600" />
                        <span className="text-sm text-gray-600">Total Templates</span>
                      </div>
                      <span className="text-sm text-gray-900">{stats.totalTemplates}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

import { collection, addDoc, doc, setDoc } from 'firebase/firestore';
import { db } from './firebase';
import { CustomRequest, User, ContactMessage } from '@/types';

// Sample templates data
export const sampleTemplates = [
  {
    title: 'Modern Dashboard',
    description: 'Clean and modern dashboard template with analytics and data visualization',
    category: 'Dashboard',
    price: 49,
    imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop',
    previewUrl: '#',
    downloadUrl: '#',
    tags: ['React', 'TypeScript', 'Charts', 'Analytics'],
    featured: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    title: 'E-commerce Store',
    description: 'Complete e-commerce solution with shopping cart and payment integration',
    category: 'E-commerce',
    price: 79,
    imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop',
    previewUrl: '#',
    downloadUrl: '#',
    tags: ['Next.js', 'Stripe', 'Shopping Cart', 'Responsive'],
    featured: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    title: 'Landing Page Pro',
    description: 'High-converting landing page template for SaaS and startups',
    category: 'Landing Page',
    price: 39,
    imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
    previewUrl: '#',
    downloadUrl: '#',
    tags: ['HTML', 'CSS', 'JavaScript', 'Conversion'],
    featured: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    title: 'Portfolio Showcase',
    description: 'Creative portfolio template for designers and developers',
    category: 'Portfolio',
    price: 29,
    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
    previewUrl: '#',
    downloadUrl: '#',
    tags: ['Vue.js', 'GSAP', 'Animation', 'Creative'],
    featured: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    title: 'Corporate Website',
    description: 'Professional corporate website template with multiple pages',
    category: 'Corporate',
    price: 59,
    imageUrl: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop',
    previewUrl: '#',
    downloadUrl: '#',
    tags: ['WordPress', 'PHP', 'Corporate', 'Professional'],
    featured: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  },
  {
    title: 'Mobile App UI',
    description: 'Complete mobile app UI kit with 50+ screens',
    category: 'Mobile App',
    price: 69,
    imageUrl: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop',
    previewUrl: '#',
    downloadUrl: '#',
    tags: ['React Native', 'Flutter', 'Mobile', 'UI Kit'],
    featured: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin'
  }
];

// Sample categories data
export const sampleCategories = [
  {
    name: 'Dashboard',
    description: 'Admin panels and data visualization templates',
    imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop',
    templateCount: 0
  },
  {
    name: 'E-commerce',
    description: 'Online stores and shopping cart templates',
    imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop',
    templateCount: 0
  },
  {
    name: 'Landing Page',
    description: 'High-converting marketing pages',
    imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
    templateCount: 0
  },
  {
    name: 'Portfolio',
    description: 'Creative showcases for professionals',
    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
    templateCount: 0
  },
  {
    name: 'Corporate',
    description: 'Business and company websites',
    imageUrl: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop',
    templateCount: 0
  },
  {
    name: 'Mobile App',
    description: 'Mobile application UI templates',
    imageUrl: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop',
    templateCount: 0
  }
];

// Function to add sample data to Firestore
export const addSampleData = async () => {
  try {
    console.log('Adding sample templates...');

    // Add templates
    for (const template of sampleTemplates) {
      await addDoc(collection(db, 'templates'), template);
    }

    console.log('Adding sample categories...');

    // Add categories
    for (const category of sampleCategories) {
      await addDoc(collection(db, 'categories'), category);
    }

    console.log('Adding sample custom requests...');

    // Add custom requests
    for (const request of sampleCustomRequests) {
      await addDoc(collection(db, 'customRequests'), request);
    }

    console.log('Adding sample users...');

    // Add sample users (with generated IDs)
    for (const userData of sampleUsers) {
      const userRef = doc(collection(db, 'users'));
      await setDoc(userRef, { ...userData, uid: userRef.id });
    }

    console.log('Adding sample contact messages...');

    // Add contact messages
    for (const message of sampleContactMessages) {
      await addDoc(collection(db, 'contactMessages'), message);
    }

    console.log('All sample data added successfully!');
  } catch (error) {
    console.error('Error adding sample data:', error);
    throw error;
  }
};

// Sample custom requests data
export const sampleCustomRequests: Omit<CustomRequest, 'id'>[] = [
  {
    userId: 'user1',
    userEmail: '<EMAIL>',
    title: 'Modern SaaS Dashboard',
    description: 'I need a modern dashboard for my SaaS application with analytics, user management, and billing features.',
    category: 'Dashboard',
    budget: 800,
    deadline: new Date('2024-02-15'),
    status: 'pending',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10')
  },
  {
    userId: 'user2',
    userEmail: '<EMAIL>',
    title: 'E-commerce Mobile App',
    description: 'Looking for a mobile-first e-commerce template with product catalog, shopping cart, and payment integration.',
    category: 'E-commerce',
    budget: 1200,
    deadline: new Date('2024-02-20'),
    status: 'in-progress',
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-12'),
    adminNotes: 'Started working on wireframes and design mockups.'
  },
  {
    userId: 'user3',
    userEmail: '<EMAIL>',
    title: 'Portfolio Website',
    description: 'Creative portfolio website for a photographer with gallery, blog, and contact features.',
    category: 'Portfolio',
    budget: 500,
    status: 'completed',
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-15'),
    adminNotes: 'Completed and delivered. Client very satisfied.'
  },
  {
    userId: 'user4',
    userEmail: '<EMAIL>',
    title: 'Corporate Landing Page',
    description: 'Professional landing page for a consulting firm with services showcase and lead generation forms.',
    category: 'Landing Page',
    budget: 600,
    deadline: new Date('2024-02-10'),
    status: 'pending',
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-12')
  },
  {
    userId: 'user5',
    userEmail: '<EMAIL>',
    title: 'Restaurant Website',
    description: 'Website for a restaurant with menu display, online ordering, and reservation system.',
    category: 'Other',
    budget: 900,
    status: 'cancelled',
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-07'),
    adminNotes: 'Client cancelled due to budget constraints.'
  }
];

// Sample users data
export const sampleUsers: Omit<User, 'uid'>[] = [
  {
    email: '<EMAIL>',
    role: 'user',
    fullName: 'John Doe',
    displayName: 'John',
    phoneNumber: '1234567890',
    countryCode: '+1',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-10')
  },
  {
    email: '<EMAIL>',
    role: 'user',
    fullName: 'Sarah Smith',
    displayName: 'Sarah',
    phoneNumber: '9876543210',
    countryCode: '+1',
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-08')
  },
  {
    email: '<EMAIL>',
    role: 'user',
    fullName: 'Mike Johnson',
    displayName: 'Mike',
    phoneNumber: '5555555555',
    countryCode: '+44',
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-05')
  },
  {
    email: '<EMAIL>',
    role: 'user',
    fullName: 'Lisa Brown',
    displayName: 'Lisa',
    phoneNumber: '7777777777',
    countryCode: '+91',
    createdAt: new Date('2024-01-04'),
    updatedAt: new Date('2024-01-12')
  },
  {
    email: '<EMAIL>',
    role: 'user',
    fullName: 'David Wilson',
    displayName: 'David',
    phoneNumber: '3333333333',
    countryCode: '+61',
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-07')
  }
];

// Sample contact messages
export const sampleContactMessages: Omit<ContactMessage, 'id'>[] = [
  {
    name: 'Alex Thompson',
    email: '<EMAIL>',
    subject: 'Question about custom development',
    message: 'Hi, I\'m interested in getting a custom template developed. Can you provide more information about your process and pricing?',
    status: 'unread',
    createdAt: new Date('2024-01-14')
  },
  {
    name: 'Emma Davis',
    email: '<EMAIL>',
    subject: 'Template customization request',
    message: 'I purchased the Modern Dashboard template and would like to customize the color scheme. Is this service available?',
    status: 'read',
    createdAt: new Date('2024-01-13')
  },
  {
    name: 'Ryan Miller',
    email: '<EMAIL>',
    subject: 'Technical support needed',
    message: 'I\'m having trouble setting up the e-commerce template. The payment integration is not working as expected.',
    status: 'replied',
    createdAt: new Date('2024-01-12')
  }
];

// Function to create an admin user (call this after signing up)
export const makeUserAdmin = async (userId: string) => {
  try {
    await setDoc(doc(db, 'users', userId), {
      role: 'admin'
    }, { merge: true });
    console.log('User made admin successfully!');
  } catch (error) {
    console.error('Error making user admin:', error);
  }
};

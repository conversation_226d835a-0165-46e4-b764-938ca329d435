'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Download, Edit, Trash2, ExternalLink, Search, Filter } from 'lucide-react';
import { Template } from '@/types';
import { collection, getDocs, query, orderBy, deleteDoc, doc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { toast } from '@/lib/toast';
import TemplateDialog from './TemplateDialog';

interface TemplatesTabProps {
  onRefresh?: () => void;
}

export default function TemplatesTab({ onRefresh }: TemplatesTabProps) {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState<'title' | 'price' | 'category' | 'createdAt'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const templatesQuery = query(collection(db, 'templates'), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(templatesQuery);
      const templatesData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date(),
      })) as Template[];

      setTemplates(templatesData);
    } catch (error) {
      console.error('Error fetching templates:', error);
      // Fallback to sample data
      setTemplates([
        {
          id: '1',
          title: 'SaaS Dashboard Pro',
          description: 'Professional dashboard template for SaaS applications',
          category: 'Technology',
          price: 2499,
          imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop',
          previewUrl: '#',
          tags: ['React', 'Dashboard', 'SaaS'],
          featured: true,
          rating: 4.9,
          downloads: 1234,
          createdAt: new Date('2023-06-01'),
          updatedAt: new Date(),
          createdBy: 'admin'
        },
        {
          id: '2',
          title: 'Free Startup Landing',
          description: 'Clean and minimal landing page for startups',
          category: 'Business',
          price: 0,
          imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
          previewUrl: '#',
          tags: ['Landing Page', 'Startup'],
          featured: false,
          rating: 4.7,
          downloads: 856,
          createdAt: new Date('2023-06-02'),
          updatedAt: new Date(),
          createdBy: 'admin'
        },
        {
          id: '3',
          title: 'Education Platform',
          description: 'Complete education platform template with course management',
          category: 'Education',
          price: 3499,
          imageUrl: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop',
          previewUrl: '#',
          tags: ['Education', 'LMS'],
          featured: true,
          rating: 4.8,
          downloads: 567,
          createdAt: new Date('2023-06-03'),
          updatedAt: new Date(),
          createdBy: 'admin'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const filteredAndSortedTemplates = templates
    .filter(template => {
      const matchesSearch = template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           template.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = filterCategory === 'all' || template.category === filterCategory;
      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      const aVal = a[sortBy];
      const bVal = b[sortBy];

      if (sortBy === 'price') {
        return sortOrder === 'asc' ? aVal - bVal : bVal - aVal;
      }

      if (sortBy === 'createdAt') {
        return sortOrder === 'asc' 
          ? new Date(aVal).getTime() - new Date(bVal).getTime()
          : new Date(bVal).getTime() - new Date(aVal).getTime();
      }

      const comparison = String(aVal).localeCompare(String(bVal));
      return sortOrder === 'asc' ? comparison : -comparison;
    });

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this template?')) return;

    try {
      await deleteDoc(doc(db, 'templates', id));
      toast.success('Template deleted successfully');
      await fetchTemplates();
      onRefresh?.();
    } catch (error) {
      console.error('Error deleting template:', error);
      toast.error('Failed to delete template');
    }
  };

  const exportToCSV = () => {
    const headers = ['Title', 'Description', 'Price', 'Category', 'Preview URL', 'Created At'];
    const csvData = [
      headers,
      ...filteredAndSortedTemplates.map(template => [
        template.title,
        template.description,
        template.price.toString(),
        template.category,
        template.previewUrl || '',
        new Date(template.createdAt).toLocaleDateString()
      ])
    ];

    const csvContent = csvData.map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `templates-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const categories = ['all', ...new Set(templates.map(t => t.category))];

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Templates Management</CardTitle>
            <CardDescription>
              Manage your template collection
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button onClick={() => setShowAddDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Template
            </Button>
            <Button variant="outline" onClick={exportToCSV}>
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Select value={filterCategory} onValueChange={setFilterCategory}>
            <SelectTrigger className="w-[180px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map(category => (
                <SelectItem key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
            const [field, order] = value.split('-');
            setSortBy(field as any);
            setSortOrder(order as 'asc' | 'desc');
          }}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="createdAt-desc">Newest First</SelectItem>
              <SelectItem value="createdAt-asc">Oldest First</SelectItem>
              <SelectItem value="title-asc">Title A-Z</SelectItem>
              <SelectItem value="title-desc">Title Z-A</SelectItem>
              <SelectItem value="price-asc">Price Low-High</SelectItem>
              <SelectItem value="price-desc">Price High-Low</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Templates List */}
        <div className="space-y-4">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading templates...</p>
            </div>
          ) : filteredAndSortedTemplates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No templates found
            </div>
          ) : (
            filteredAndSortedTemplates.map((template) => (
              <div key={template.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold text-lg">{template.title}</h4>
                      <Badge variant="secondary">{template.category}</Badge>
                      {template.featured && <Badge variant="default">Featured</Badge>}
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {template.description}
                    </p>
                    <div className="flex items-center gap-4 text-sm">
                      <span className="font-medium text-green-600">
                        {template.price === 0 ? 'Free' : `₹${template.price}`}
                      </span>
                      {template.previewUrl && (
                        <a
                          href={template.previewUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 flex items-center gap-1"
                        >
                          <ExternalLink className="h-3 w-3" />
                          Preview Link
                        </a>
                      )}
                      <span className="text-muted-foreground">
                        Created: {new Date(template.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingTemplate(template)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDelete(template.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>

      {/* Add/Edit Template Dialog */}
      <TemplateDialog
        open={showAddDialog || !!editingTemplate}
        onClose={() => {
          setShowAddDialog(false);
          setEditingTemplate(null);
        }}
        template={editingTemplate}
        onSuccess={() => {
          setShowAddDialog(false);
          setEditingTemplate(null);
          fetchTemplates();
          onRefresh?.();
        }}
      />
    </Card>
  );
}

'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Star, Eye, Download, ArrowRight } from 'lucide-react';

// Mock data for featured templates
const featuredTemplates = [
  {
    id: '1',
    title: 'Modern Dashboard',
    description: 'Clean and modern dashboard template with dark mode support',
    category: 'Dashboard',
    price: 49,
    imageUrl: '/api/placeholder/400/300',
    rating: 4.9,
    downloads: 1234,
    featured: true,
    tags: ['React', 'TypeScript', 'Tailwind']
  },
  {
    id: '2',
    title: 'E-commerce Store',
    description: 'Complete e-commerce solution with shopping cart and checkout',
    category: 'E-commerce',
    price: 79,
    imageUrl: '/api/placeholder/400/300',
    rating: 4.8,
    downloads: 856,
    featured: true,
    tags: ['Next.js', 'Stripe', 'Responsive']
  },
  {
    id: '3',
    title: 'Landing Page Pro',
    description: 'High-converting landing page template for SaaS products',
    category: 'Landing Page',
    price: 39,
    imageUrl: '/api/placeholder/400/300',
    rating: 4.9,
    downloads: 2341,
    featured: true,
    tags: ['HTML', 'CSS', 'JavaScript']
  },
  {
    id: '4',
    title: 'Portfolio Showcase',
    description: 'Creative portfolio template for designers and developers',
    category: 'Portfolio',
    price: 29,
    imageUrl: '/api/placeholder/400/300',
    rating: 4.7,
    downloads: 1567,
    featured: true,
    tags: ['Vue.js', 'GSAP', 'Responsive']
  }
];

export const FeaturedTemplates = () => {
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Popular Templates
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Professional templates trusted by thousands of businesses
          </p>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {featuredTemplates.map((template) => (
            <Card key={template.id} className="group hover:shadow-lg transition-shadow duration-200">
              <div className="relative overflow-hidden rounded-t-lg">
                <Image
                  src={template.imageUrl}
                  alt={template.title}
                  width={400}
                  height={300}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <Button size="sm" variant="secondary">
                      <Eye className="h-4 w-4 mr-1" />
                      Preview
                    </Button>
                  </div>
                </div>
              </div>
              
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="outline">{template.category}</Badge>
                  <div className="flex items-center text-sm text-gray-600">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                    {template.rating}
                  </div>
                </div>
                
                <h3 className="font-semibold text-lg text-gray-900 mb-2">
                  {template.title}
                </h3>
                
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {template.description}
                </p>
                
                <div className="flex flex-wrap gap-1 mb-4">
                  {template.tags.slice(0, 2).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {template.tags.length > 2 && (
                    <Badge variant="secondary" className="text-xs">
                      +{template.tags.length - 2}
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-sm text-gray-600">
                    <Download className="h-4 w-4 mr-1" />
                    {template.downloads.toLocaleString()}
                  </div>
                  <div className="text-2xl font-bold text-gray-900">
                    ${template.price}
                  </div>
                </div>
              </CardContent>
              
              <CardFooter className="p-6 pt-0">
                <Button asChild className="w-full">
                  <Link href={`/templates/${template.id}`}>
                    View Details
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Button asChild size="lg" variant="outline">
            <Link href="/templates">
              View All Templates
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Monitor, 
  ShoppingCart, 
  Briefcase, 
  Users, 
  FileText, 
  Smartphone,
  ArrowRight 
} from 'lucide-react';

// Mock data for categories
const categories = [
  {
    id: '1',
    name: 'Dashboard',
    description: 'Admin panels and data visualization templates',
    icon: Monitor,
    templateCount: 45,
    color: 'bg-blue-500'
  },
  {
    id: '2',
    name: 'E-commerce',
    description: 'Online store and shopping cart templates',
    icon: ShoppingCart,
    templateCount: 32,
    color: 'bg-green-500'
  },
  {
    id: '3',
    name: 'Portfolio',
    description: 'Creative showcases for professionals',
    icon: Briefcase,
    templateCount: 28,
    color: 'bg-purple-500'
  },
  {
    id: '4',
    name: 'Landing Page',
    description: 'High-converting marketing pages',
    icon: FileText,
    templateCount: 56,
    color: 'bg-orange-500'
  },
  {
    id: '5',
    name: 'Corporate',
    description: 'Business and company websites',
    icon: Users,
    templateCount: 23,
    color: 'bg-indigo-500'
  },
  {
    id: '6',
    name: 'Mobile App',
    description: 'Mobile application UI templates',
    icon: Smartphone,
    templateCount: 19,
    color: 'bg-pink-500'
  }
];

export const CategoriesSection = () => {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Template Categories
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Choose from our organized collection of professional templates
          </p>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {categories.map((category) => {
            const IconComponent = category.icon;
            return (
              <Link key={category.id} href={`/templates?category=${category.name}`}>
                <Card className="group hover:shadow-lg transition-shadow duration-200 h-full cursor-pointer">
                  <CardContent className="p-6 text-center">
                    <div className={`inline-flex items-center justify-center w-12 h-12 ${category.color} rounded-lg mb-4`}>
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>

                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {category.name}
                    </h3>

                    <p className="text-gray-600 text-sm mb-3">
                      {category.description}
                    </p>

                    <div className="text-sm text-gray-500">
                      <span>{category.templateCount} templates</span>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>

        {/* View All Categories Button */}
        <div className="text-center">
          <Button asChild size="lg" variant="outline">
            <Link href="/categories">
              View All Categories
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

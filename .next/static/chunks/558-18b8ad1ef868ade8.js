(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[558],{381:(e,r,n)=>{"use strict";n.d(r,{A:()=>t});let t=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1007:(e,r,n)=>{"use strict";n.d(r,{A:()=>t});let t=(0,n(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1414:(e,r,n)=>{"use strict";e.exports=n(2436)},2436:(e,r,n)=>{"use strict";var t=n(2115),o="function"==typeof Object.is?Object.is:function(e,r){return e===r&&(0!==e||1/e==1/r)||e!=e&&r!=r},a=t.useState,l=t.useEffect,i=t.useLayoutEffect,u=t.useDebugValue;function s(e){var r=e.getSnapshot;e=e.value;try{var n=r();return!o(e,n)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,r){return r()}:function(e,r){var n=r(),t=a({inst:{value:n,getSnapshot:r}}),o=t[0].inst,d=t[1];return i(function(){o.value=n,o.getSnapshot=r,s(o)&&d({inst:o})},[e,n,r]),l(function(){return s(o)&&d({inst:o}),e(function(){s(o)&&d({inst:o})})},[e]),u(n),n};r.useSyncExternalStore=void 0!==t.useSyncExternalStore?t.useSyncExternalStore:d},4011:(e,r,n)=>{"use strict";n.d(r,{H4:()=>C,bL:()=>b});var t=n(2115),o=n(6081),a=n(9033),l=n(2712),i=n(3655),u=n(1414);function s(){return()=>{}}var d=n(5155),c="Avatar",[p,f]=(0,o.A)(c),[v,m]=p(c),g=t.forwardRef((e,r)=>{let{__scopeAvatar:n,...o}=e,[a,l]=t.useState("idle");return(0,d.jsx)(v,{scope:n,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,d.jsx)(i.sG.span,{...o,ref:r})})});g.displayName=c;var h="AvatarImage";t.forwardRef((e,r)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:c=()=>{},...p}=e,f=m(h,n),v=function(e,r){let{referrerPolicy:n,crossOrigin:o}=r,a=(0,u.useSyncExternalStore)(s,()=>!0,()=>!1),i=t.useRef(null),d=a?(i.current||(i.current=new window.Image),i.current):null,[c,p]=t.useState(()=>x(d,e));return(0,l.N)(()=>{p(x(d,e))},[d,e]),(0,l.N)(()=>{let e=e=>()=>{p(e)};if(!d)return;let r=e("loaded"),t=e("error");return d.addEventListener("load",r),d.addEventListener("error",t),n&&(d.referrerPolicy=n),"string"==typeof o&&(d.crossOrigin=o),()=>{d.removeEventListener("load",r),d.removeEventListener("error",t)}},[d,o,n]),c}(o,p),g=(0,a.c)(e=>{c(e),f.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==v&&g(v)},[v,g]),"loaded"===v?(0,d.jsx)(i.sG.img,{...p,ref:r,src:o}):null}).displayName=h;var w="AvatarFallback",y=t.forwardRef((e,r)=>{let{__scopeAvatar:n,delayMs:o,...a}=e,l=m(w,n),[u,s]=t.useState(void 0===o);return t.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==l.imageLoadingStatus?(0,d.jsx)(i.sG.span,{...a,ref:r}):null});function x(e,r){return e?r?(e.src!==r&&(e.src=r),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}y.displayName=w;var b=g,C=y},4147:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},4416:(e,r,n)=>{"use strict";n.d(r,{A:()=>t});let t=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,r,n)=>{"use strict";n.d(r,{A:()=>t});let t=(0,n(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},4835:(e,r,n)=>{"use strict";n.d(r,{A:()=>t});let t=(0,n(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5452:(e,r,n)=>{"use strict";n.d(r,{UC:()=>ee,ZL:()=>Q,bL:()=>Y,bm:()=>er,hJ:()=>$,l9:()=>J});var t=n(2115),o=n(5185),a=n(6101),l=n(6081),i=n(1285),u=n(5845),s=n(9178),d=n(7900),c=n(4378),p=n(8905),f=n(3655),v=n(2293),m=n(1114),g=n(8168),h=n(9708),w=n(5155),y="Dialog",[x,b]=(0,l.A)(y),[C,j]=x(y),R=e=>{let{__scopeDialog:r,children:n,open:o,defaultOpen:a,onOpenChange:l,modal:s=!0}=e,d=t.useRef(null),c=t.useRef(null),[p,f]=(0,u.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:y});return(0,w.jsx)(C,{scope:r,triggerRef:d,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:s,children:n})};R.displayName=y;var M="DialogTrigger",D=t.forwardRef((e,r)=>{let{__scopeDialog:n,...t}=e,l=j(M,n),i=(0,a.s)(r,l.triggerRef);return(0,w.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":q(l.open),...t,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});D.displayName=M;var _="DialogPortal",[k,E]=x(_,{forceMount:void 0}),I=e=>{let{__scopeDialog:r,forceMount:n,children:o,container:a}=e,l=j(_,r);return(0,w.jsx)(k,{scope:r,forceMount:n,children:t.Children.map(o,e=>(0,w.jsx)(p.C,{present:n||l.open,children:(0,w.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};I.displayName=_;var N="DialogOverlay",S=t.forwardRef((e,r)=>{let n=E(N,e.__scopeDialog),{forceMount:t=n.forceMount,...o}=e,a=j(N,e.__scopeDialog);return a.modal?(0,w.jsx)(p.C,{present:t||a.open,children:(0,w.jsx)(P,{...o,ref:r})}):null});S.displayName=N;var O=(0,h.TL)("DialogOverlay.RemoveScroll"),P=t.forwardRef((e,r)=>{let{__scopeDialog:n,...t}=e,o=j(N,n);return(0,w.jsx)(m.A,{as:O,allowPinchZoom:!0,shards:[o.contentRef],children:(0,w.jsx)(f.sG.div,{"data-state":q(o.open),...t,ref:r,style:{pointerEvents:"auto",...t.style}})})}),A="DialogContent",L=t.forwardRef((e,r)=>{let n=E(A,e.__scopeDialog),{forceMount:t=n.forceMount,...o}=e,a=j(A,e.__scopeDialog);return(0,w.jsx)(p.C,{present:t||a.open,children:a.modal?(0,w.jsx)(T,{...o,ref:r}):(0,w.jsx)(F,{...o,ref:r})})});L.displayName=A;var T=t.forwardRef((e,r)=>{let n=j(A,e.__scopeDialog),l=t.useRef(null),i=(0,a.s)(r,n.contentRef,l);return t.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,w.jsx)(G,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;e.preventDefault(),null==(r=n.triggerRef.current)||r.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey;(2===r.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=t.forwardRef((e,r)=>{let n=j(A,e.__scopeDialog),o=t.useRef(!1),a=t.useRef(!1);return(0,w.jsx)(G,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{var t,l;null==(t=e.onCloseAutoFocus)||t.call(e,r),r.defaultPrevented||(o.current||null==(l=n.triggerRef.current)||l.focus(),r.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:r=>{var t,l;null==(t=e.onInteractOutside)||t.call(e,r),r.defaultPrevented||(o.current=!0,"pointerdown"===r.detail.originalEvent.type&&(a.current=!0));let i=r.target;(null==(l=n.triggerRef.current)?void 0:l.contains(i))&&r.preventDefault(),"focusin"===r.detail.originalEvent.type&&a.current&&r.preventDefault()}})}),G=t.forwardRef((e,r)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...u}=e,c=j(A,n),p=t.useRef(null),f=(0,a.s)(r,p);return(0,v.Oh)(),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,w.jsx)(s.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":q(c.open),...u,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(X,{titleId:c.titleId}),(0,w.jsx)(z,{contentRef:p,descriptionId:c.descriptionId})]})]})}),K="DialogTitle";t.forwardRef((e,r)=>{let{__scopeDialog:n,...t}=e,o=j(K,n);return(0,w.jsx)(f.sG.h2,{id:o.titleId,...t,ref:r})}).displayName=K;var B="DialogDescription";t.forwardRef((e,r)=>{let{__scopeDialog:n,...t}=e,o=j(B,n);return(0,w.jsx)(f.sG.p,{id:o.descriptionId,...t,ref:r})}).displayName=B;var V="DialogClose",U=t.forwardRef((e,r)=>{let{__scopeDialog:n,...t}=e,a=j(V,n);return(0,w.jsx)(f.sG.button,{type:"button",...t,ref:r,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function q(e){return e?"open":"closed"}U.displayName=V;var H="DialogTitleWarning",[W,Z]=(0,l.q)(H,{contentName:A,titleName:K,docsSlug:"dialog"}),X=e=>{let{titleId:r}=e,n=Z(H),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return t.useEffect(()=>{r&&(document.getElementById(r)||console.error(o))},[o,r]),null},z=e=>{let{contentRef:r,descriptionId:n}=e,o=Z("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return t.useEffect(()=>{var e;let t=null==(e=r.current)?void 0:e.getAttribute("aria-describedby");n&&t&&(document.getElementById(n)||console.warn(a))},[a,r,n]),null},Y=R,J=D,Q=I,$=S,ee=L,er=U},6151:(e,r,n)=>{"use strict";n.d(r,{A:()=>t});let t=(0,n(9946).A)("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},8489:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},8698:(e,r,n)=>{"use strict";n.d(r,{UC:()=>eZ,q7:()=>eX,ZL:()=>eW,bL:()=>eq,wv:()=>ez,l9:()=>eH});var t=n(2115),o=n(5185),a=n(6101),l=n(6081),i=n(5845),u=n(3655),s=n(7328),d=n(4315),c=n(9178),p=n(2293),f=n(7900),v=n(1285),m=n(8795),g=n(4378),h=n(8905),w=n(9196),y=n(9708),x=n(9033),b=n(8168),C=n(1114),j=n(5155),R=["Enter"," "],M=["ArrowUp","PageDown","End"],D=["ArrowDown","PageUp","Home",...M],_={ltr:[...R,"ArrowRight"],rtl:[...R,"ArrowLeft"]},k={ltr:["ArrowLeft"],rtl:["ArrowRight"]},E="Menu",[I,N,S]=(0,s.N)(E),[O,P]=(0,l.A)(E,[S,m.Bk,w.RG]),A=(0,m.Bk)(),L=(0,w.RG)(),[T,F]=O(E),[G,K]=O(E),B=e=>{let{__scopeMenu:r,open:n=!1,children:o,dir:a,onOpenChange:l,modal:i=!0}=e,u=A(r),[s,c]=t.useState(null),p=t.useRef(!1),f=(0,x.c)(l),v=(0,d.jH)(a);return t.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,j.jsx)(m.bL,{...u,children:(0,j.jsx)(T,{scope:r,open:n,onOpenChange:f,content:s,onContentChange:c,children:(0,j.jsx)(G,{scope:r,onClose:t.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:v,modal:i,children:o})})})};B.displayName=E;var V=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=A(n);return(0,j.jsx)(m.Mz,{...o,...t,ref:r})});V.displayName="MenuAnchor";var U="MenuPortal",[q,H]=O(U,{forceMount:void 0}),W=e=>{let{__scopeMenu:r,forceMount:n,children:t,container:o}=e,a=F(U,r);return(0,j.jsx)(q,{scope:r,forceMount:n,children:(0,j.jsx)(h.C,{present:n||a.open,children:(0,j.jsx)(g.Z,{asChild:!0,container:o,children:t})})})};W.displayName=U;var Z="MenuContent",[X,z]=O(Z),Y=t.forwardRef((e,r)=>{let n=H(Z,e.__scopeMenu),{forceMount:t=n.forceMount,...o}=e,a=F(Z,e.__scopeMenu),l=K(Z,e.__scopeMenu);return(0,j.jsx)(I.Provider,{scope:e.__scopeMenu,children:(0,j.jsx)(h.C,{present:t||a.open,children:(0,j.jsx)(I.Slot,{scope:e.__scopeMenu,children:l.modal?(0,j.jsx)(J,{...o,ref:r}):(0,j.jsx)(Q,{...o,ref:r})})})})}),J=t.forwardRef((e,r)=>{let n=F(Z,e.__scopeMenu),l=t.useRef(null),i=(0,a.s)(r,l);return t.useEffect(()=>{let e=l.current;if(e)return(0,b.Eq)(e)},[]),(0,j.jsx)(ee,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=t.forwardRef((e,r)=>{let n=F(Z,e.__scopeMenu);return(0,j.jsx)(ee,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),$=(0,y.TL)("MenuContent.ScrollLock"),ee=t.forwardRef((e,r)=>{let{__scopeMenu:n,loop:l=!1,trapFocus:i,onOpenAutoFocus:u,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:v,onEscapeKeyDown:g,onPointerDownOutside:h,onFocusOutside:y,onInteractOutside:x,onDismiss:b,disableOutsideScroll:R,..._}=e,k=F(Z,n),E=K(Z,n),I=A(n),S=L(n),O=N(n),[P,T]=t.useState(null),G=t.useRef(null),B=(0,a.s)(r,G,k.onContentChange),V=t.useRef(0),U=t.useRef(""),q=t.useRef(0),H=t.useRef(null),W=t.useRef("right"),z=t.useRef(0),Y=R?C.A:t.Fragment,J=e=>{var r,n;let t=U.current+e,o=O().filter(e=>!e.disabled),a=document.activeElement,l=null==(r=o.find(e=>e.ref.current===a))?void 0:r.textValue,i=function(e,r,n){var t;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=n?e.indexOf(n):-1,l=(t=Math.max(a,0),e.map((r,n)=>e[(t+n)%e.length]));1===o.length&&(l=l.filter(e=>e!==n));let i=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==n?i:void 0}(o.map(e=>e.textValue),t,l),u=null==(n=o.find(e=>e.textValue===i))?void 0:n.ref.current;!function e(r){U.current=r,window.clearTimeout(V.current),""!==r&&(V.current=window.setTimeout(()=>e(""),1e3))}(t),u&&setTimeout(()=>u.focus())};t.useEffect(()=>()=>window.clearTimeout(V.current),[]),(0,p.Oh)();let Q=t.useCallback(e=>{var r,n;return W.current===(null==(r=H.current)?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:n,y:t}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let l=r[e],i=r[a],u=l.x,s=l.y,d=i.x,c=i.y;s>t!=c>t&&n<(d-u)*(t-s)/(c-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null==(n=H.current)?void 0:n.area)},[]);return(0,j.jsx)(X,{scope:n,searchRef:U,onItemEnter:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:t.useCallback(e=>{var r;Q(e)||(null==(r=G.current)||r.focus(),T(null))},[Q]),onTriggerLeave:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:q,onPointerGraceIntentChange:t.useCallback(e=>{H.current=e},[]),children:(0,j.jsx)(Y,{...R?{as:$,allowPinchZoom:!0}:void 0,children:(0,j.jsx)(f.n,{asChild:!0,trapped:i,onMountAutoFocus:(0,o.m)(u,e=>{var r;e.preventDefault(),null==(r=G.current)||r.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,j.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:g,onPointerDownOutside:h,onFocusOutside:y,onInteractOutside:x,onDismiss:b,children:(0,j.jsx)(w.bL,{asChild:!0,...S,dir:E.dir,orientation:"vertical",loop:l,currentTabStopId:P,onCurrentTabStopIdChange:T,onEntryFocus:(0,o.m)(v,e=>{E.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,j.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eD(k.open),"data-radix-menu-content":"",dir:E.dir,...I,..._,ref:B,style:{outline:"none",..._.style},onKeyDown:(0,o.m)(_.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&t&&J(e.key));let o=G.current;if(e.target!==o||!D.includes(e.key))return;e.preventDefault();let a=O().filter(e=>!e.disabled).map(e=>e.ref.current);M.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let n of e)if(n===r||(n.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(V.current),U.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eE(e=>{let r=e.target,n=z.current!==e.clientX;e.currentTarget.contains(r)&&n&&(W.current=e.clientX>z.current?"right":"left",z.current=e.clientX)}))})})})})})})});Y.displayName=Z;var er=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,j.jsx)(u.sG.div,{role:"group",...t,ref:r})});er.displayName="MenuGroup";var en=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,j.jsx)(u.sG.div,{...t,ref:r})});en.displayName="MenuLabel";var et="MenuItem",eo="menu.itemSelect",ea=t.forwardRef((e,r)=>{let{disabled:n=!1,onSelect:l,...i}=e,s=t.useRef(null),d=K(et,e.__scopeMenu),c=z(et,e.__scopeMenu),p=(0,a.s)(r,s),f=t.useRef(!1);return(0,j.jsx)(el,{...i,ref:p,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!n&&e){let r=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==l?void 0:l(e),{once:!0}),(0,u.hO)(e,r),r.defaultPrevented?f.current=!1:d.onClose()}}),onPointerDown:r=>{var n;null==(n=e.onPointerDown)||n.call(e,r),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var r;f.current||null==(r=e.currentTarget)||r.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==c.searchRef.current;n||r&&" "===e.key||R.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=et;var el=t.forwardRef((e,r)=>{let{__scopeMenu:n,disabled:l=!1,textValue:i,...s}=e,d=z(et,n),c=L(n),p=t.useRef(null),f=(0,a.s)(r,p),[v,m]=t.useState(!1),[g,h]=t.useState("");return t.useEffect(()=>{let e=p.current;if(e){var r;h((null!=(r=e.textContent)?r:"").trim())}},[s.children]),(0,j.jsx)(I.ItemSlot,{scope:n,disabled:l,textValue:null!=i?i:g,children:(0,j.jsx)(w.q7,{asChild:!0,...c,focusable:!l,children:(0,j.jsx)(u.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...s,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eE(e=>{l?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eE(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),ei=t.forwardRef((e,r)=>{let{checked:n=!1,onCheckedChange:t,...a}=e;return(0,j.jsx)(em,{scope:e.__scopeMenu,checked:n,children:(0,j.jsx)(ea,{role:"menuitemcheckbox","aria-checked":e_(n)?"mixed":n,...a,ref:r,"data-state":ek(n),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!e_(n)||!n),{checkForDefaultPrevented:!1})})})});ei.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[es,ed]=O(eu,{value:void 0,onValueChange:()=>{}}),ec=t.forwardRef((e,r)=>{let{value:n,onValueChange:t,...o}=e,a=(0,x.c)(t);return(0,j.jsx)(es,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,j.jsx)(er,{...o,ref:r})})});ec.displayName=eu;var ep="MenuRadioItem",ef=t.forwardRef((e,r)=>{let{value:n,...t}=e,a=ed(ep,e.__scopeMenu),l=n===a.value;return(0,j.jsx)(em,{scope:e.__scopeMenu,checked:l,children:(0,j.jsx)(ea,{role:"menuitemradio","aria-checked":l,...t,ref:r,"data-state":ek(l),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});ef.displayName=ep;var ev="MenuItemIndicator",[em,eg]=O(ev,{checked:!1}),eh=t.forwardRef((e,r)=>{let{__scopeMenu:n,forceMount:t,...o}=e,a=eg(ev,n);return(0,j.jsx)(h.C,{present:t||e_(a.checked)||!0===a.checked,children:(0,j.jsx)(u.sG.span,{...o,ref:r,"data-state":ek(a.checked)})})});eh.displayName=ev;var ew=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,j.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:r})});ew.displayName="MenuSeparator";var ey=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=A(n);return(0,j.jsx)(m.i3,{...o,...t,ref:r})});ey.displayName="MenuArrow";var[ex,eb]=O("MenuSub"),eC="MenuSubTrigger",ej=t.forwardRef((e,r)=>{let n=F(eC,e.__scopeMenu),l=K(eC,e.__scopeMenu),i=eb(eC,e.__scopeMenu),u=z(eC,e.__scopeMenu),s=t.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=u,p={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,j.jsx)(V,{asChild:!0,...p,children:(0,j.jsx)(el,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":i.contentId,"data-state":eD(n.open),...e,ref:(0,a.t)(r,i.onTriggerChange),onClick:r=>{var t;null==(t=e.onClick)||t.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eE(r=>{u.onItemEnter(r),!r.defaultPrevented&&(e.disabled||n.open||s.current||(u.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eE(e=>{var r,t;f();let o=null==(r=n.content)?void 0:r.getBoundingClientRect();if(o){let r=null==(t=n.content)?void 0:t.dataset.side,a="right"===r,l=o[a?"left":"right"],i=o[a?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:i,y:o.top},{x:i,y:o.bottom},{x:l,y:o.bottom}],side:r}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let t=""!==u.searchRef.current;if(!e.disabled&&(!t||" "!==r.key)&&_[l.dir].includes(r.key)){var o;n.onOpenChange(!0),null==(o=n.content)||o.focus(),r.preventDefault()}})})})});ej.displayName=eC;var eR="MenuSubContent",eM=t.forwardRef((e,r)=>{let n=H(Z,e.__scopeMenu),{forceMount:l=n.forceMount,...i}=e,u=F(Z,e.__scopeMenu),s=K(Z,e.__scopeMenu),d=eb(eR,e.__scopeMenu),c=t.useRef(null),p=(0,a.s)(r,c);return(0,j.jsx)(I.Provider,{scope:e.__scopeMenu,children:(0,j.jsx)(h.C,{present:l||u.open,children:(0,j.jsx)(I.Slot,{scope:e.__scopeMenu,children:(0,j.jsx)(ee,{id:d.contentId,"aria-labelledby":d.triggerId,...i,ref:p,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;s.isUsingKeyboardRef.current&&(null==(r=c.current)||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),n=k[s.dir].includes(e.key);if(r&&n){var t;u.onOpenChange(!1),null==(t=d.trigger)||t.focus(),e.preventDefault()}})})})})})});function eD(e){return e?"open":"closed"}function e_(e){return"indeterminate"===e}function ek(e){return e_(e)?"indeterminate":e?"checked":"unchecked"}function eE(e){return r=>"mouse"===r.pointerType?e(r):void 0}eM.displayName=eR;var eI="DropdownMenu",[eN,eS]=(0,l.A)(eI,[P]),eO=P(),[eP,eA]=eN(eI),eL=e=>{let{__scopeDropdownMenu:r,children:n,dir:o,open:a,defaultOpen:l,onOpenChange:u,modal:s=!0}=e,d=eO(r),c=t.useRef(null),[p,f]=(0,i.i)({prop:a,defaultProp:null!=l&&l,onChange:u,caller:eI});return(0,j.jsx)(eP,{scope:r,triggerId:(0,v.B)(),triggerRef:c,contentId:(0,v.B)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:s,children:(0,j.jsx)(B,{...d,open:p,onOpenChange:f,dir:o,modal:s,children:n})})};eL.displayName=eI;var eT="DropdownMenuTrigger",eF=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,disabled:t=!1,...l}=e,i=eA(eT,n),s=eO(n);return(0,j.jsx)(V,{asChild:!0,...s,children:(0,j.jsx)(u.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...l,ref:(0,a.t)(r,i.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!t&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eF.displayName=eT;var eG=e=>{let{__scopeDropdownMenu:r,...n}=e,t=eO(r);return(0,j.jsx)(W,{...t,...n})};eG.displayName="DropdownMenuPortal";var eK="DropdownMenuContent",eB=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...a}=e,l=eA(eK,n),i=eO(n),u=t.useRef(!1);return(0,j.jsx)(Y,{id:l.contentId,"aria-labelledby":l.triggerId,...i,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;u.current||null==(r=l.triggerRef.current)||r.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey,t=2===r.button||n;(!l.modal||t)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eB.displayName=eK,t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,j.jsx)(er,{...o,...t,ref:r})}).displayName="DropdownMenuGroup",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,j.jsx)(en,{...o,...t,ref:r})}).displayName="DropdownMenuLabel";var eV=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,j.jsx)(ea,{...o,...t,ref:r})});eV.displayName="DropdownMenuItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,j.jsx)(ei,{...o,...t,ref:r})}).displayName="DropdownMenuCheckboxItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,j.jsx)(ec,{...o,...t,ref:r})}).displayName="DropdownMenuRadioGroup",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,j.jsx)(ef,{...o,...t,ref:r})}).displayName="DropdownMenuRadioItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,j.jsx)(eh,{...o,...t,ref:r})}).displayName="DropdownMenuItemIndicator";var eU=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,j.jsx)(ew,{...o,...t,ref:r})});eU.displayName="DropdownMenuSeparator",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,j.jsx)(ey,{...o,...t,ref:r})}).displayName="DropdownMenuArrow",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,j.jsx)(ej,{...o,...t,ref:r})}).displayName="DropdownMenuSubTrigger",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eO(n);return(0,j.jsx)(eM,{...o,...t,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eq=eL,eH=eF,eW=eG,eZ=eB,eX=eV,ez=eU}}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[365],{285:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var a=s(5155);s(2115);var r=s(9708),n=s(2085),i=s(9434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:s,size:n,asChild:d=!1,...o}=e,c=d?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:s,size:n,className:t})),...o})}},716:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>R});var a=s(5155),r=s(2115),n=s(5695),i=s(844),l=s(285),d=s(2523),o=s(968),c=s(9434);function u(e){let{className:t,...s}=e;return(0,a.jsx)(o.b,{"data-slot":"label",className:(0,c.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}var p=s(6695),m=s(704);function x(e){let{className:t,...s}=e;return(0,a.jsx)(m.bL,{"data-slot":"tabs",className:(0,c.cn)("flex flex-col gap-2",t),...s})}function h(e){let{className:t,...s}=e;return(0,a.jsx)(m.B8,{"data-slot":"tabs-list",className:(0,c.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...s})}function g(e){let{className:t,...s}=e;return(0,a.jsx)(m.l9,{"data-slot":"tabs-trigger",className:(0,c.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function v(e){let{className:t,...s}=e;return(0,a.jsx)(m.UC,{"data-slot":"tabs-content",className:(0,c.cn)("flex-1 outline-none",t),...s})}var f=s(8893),b=s(6474),j=s(5196),w=s(7863);function y(e){let{...t}=e;return(0,a.jsx)(f.bL,{"data-slot":"select",...t})}function N(e){let{...t}=e;return(0,a.jsx)(f.WT,{"data-slot":"select-value",...t})}function _(e){let{className:t,size:s="default",children:r,...n}=e;return(0,a.jsxs)(f.l9,{"data-slot":"select-trigger","data-size":s,className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[r,(0,a.jsx)(f.In,{asChild:!0,children:(0,a.jsx)(b.A,{className:"size-4 opacity-50"})})]})}function E(e){let{className:t,children:s,position:r="popper",...n}=e;return(0,a.jsx)(f.ZL,{children:(0,a.jsxs)(f.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...n,children:[(0,a.jsx)(k,{}),(0,a.jsx)(f.LM,{className:(0,c.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(C,{})]})})}function A(e){let{className:t,children:s,...r}=e;return(0,a.jsxs)(f.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...r,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(f.VF,{children:(0,a.jsx)(j.A,{className:"size-4"})})}),(0,a.jsx)(f.p4,{children:s})]})}function k(e){let{className:t,...s}=e;return(0,a.jsx)(f.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(w.A,{className:"size-4"})})}function C(e){let{className:t,...s}=e;return(0,a.jsx)(f.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(b.A,{className:"size-4"})})}let P=(0,s(2085).F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function S(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,c.cn)(P({variant:s}),t),...r})}function I(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,c.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...s})}var z=s(8749),B=s(2657),F=s(1154),T=s(6874),U=s.n(T);function R(){let[e,t]=(0,r.useState)(!1),[s,o]=(0,r.useState)(""),[c,m]=(0,r.useState)(!1),[f,b]=(0,r.useState)({email:"",password:"",confirmPassword:"",role:"user"}),{signIn:j,signUp:w,user:k}=(0,i.A)(),C=(0,n.useRouter)(),P=(0,n.useSearchParams)().get("mode")||"signin";(0,r.useEffect)(()=>{k&&C.push("/dashboard")},[k,C]);let T=e=>{b(t=>({...t,[e.target.name]:e.target.value})),o("")},R=async e=>{e.preventDefault(),t(!0),o("");try{await j(f.email,f.password),C.push("/dashboard")}catch(e){o(e.message||"Failed to sign in")}finally{t(!1)}},L=async e=>{if(e.preventDefault(),t(!0),o(""),f.password!==f.confirmPassword){o("Passwords do not match"),t(!1);return}if(f.password.length<6){o("Password must be at least 6 characters"),t(!1);return}try{await w(f.email,f.password,f.role),C.push("/dashboard")}catch(e){o(e.message||"Failed to create account")}finally{t(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsx)("div",{className:"text-center mb-8",children:(0,a.jsxs)(U(),{href:"/",className:"inline-flex items-center space-x-2 mb-6",children:[(0,a.jsx)("div",{className:"h-10 w-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"K"})}),(0,a.jsx)("span",{className:"font-bold text-2xl",children:"KaleidoneX"})]})}),(0,a.jsxs)(p.Zp,{children:[(0,a.jsxs)(p.aR,{className:"text-center",children:[(0,a.jsx)(p.ZB,{className:"text-2xl",children:"signup"===P?"Create Account":"Welcome Back"}),(0,a.jsx)(p.BT,{children:"signup"===P?"Sign up to start downloading premium templates":"Sign in to your account to continue"})]}),(0,a.jsxs)(p.Wu,{children:[(0,a.jsxs)(x,{value:P,className:"w-full",children:[(0,a.jsxs)(h,{className:"grid w-full grid-cols-2",children:[(0,a.jsx)(g,{value:"signin",asChild:!0,children:(0,a.jsx)(U(),{href:"/auth?mode=signin",children:"Sign In"})}),(0,a.jsx)(g,{value:"signup",asChild:!0,children:(0,a.jsx)(U(),{href:"/auth?mode=signup",children:"Sign Up"})})]}),(0,a.jsx)(v,{value:"signin",children:(0,a.jsxs)("form",{onSubmit:R,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u,{htmlFor:"email",children:"Email"}),(0,a.jsx)(d.p,{id:"email",name:"email",type:"email",placeholder:"Enter your email",value:f.email,onChange:T,required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u,{htmlFor:"password",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(d.p,{id:"password",name:"password",type:c?"text":"password",placeholder:"Enter your password",value:f.password,onChange:T,required:!0}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>m(!c),children:c?(0,a.jsx)(z.A,{className:"h-4 w-4"}):(0,a.jsx)(B.A,{className:"h-4 w-4"})})]})]}),s&&(0,a.jsx)(S,{variant:"destructive",children:(0,a.jsx)(I,{children:s})}),(0,a.jsxs)(l.$,{type:"submit",className:"w-full",disabled:e,children:[e&&(0,a.jsx)(F.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Sign In"]})]})}),(0,a.jsx)(v,{value:"signup",children:(0,a.jsxs)("form",{onSubmit:L,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u,{htmlFor:"signup-email",children:"Email"}),(0,a.jsx)(d.p,{id:"signup-email",name:"email",type:"email",placeholder:"Enter your email",value:f.email,onChange:T,required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u,{htmlFor:"role",children:"Account Type"}),(0,a.jsxs)(y,{value:f.role,onValueChange:e=>{b(t=>({...t,role:e}))},children:[(0,a.jsx)(_,{children:(0,a.jsx)(N,{placeholder:"Select account type"})}),(0,a.jsxs)(E,{children:[(0,a.jsx)(A,{value:"user",children:"User"}),(0,a.jsx)(A,{value:"admin",children:"Admin"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u,{htmlFor:"signup-password",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(d.p,{id:"signup-password",name:"password",type:c?"text":"password",placeholder:"Create a password",value:f.password,onChange:T,required:!0}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>m(!c),children:c?(0,a.jsx)(z.A,{className:"h-4 w-4"}):(0,a.jsx)(B.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u,{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,a.jsx)(d.p,{id:"confirmPassword",name:"confirmPassword",type:"password",placeholder:"Confirm your password",value:f.confirmPassword,onChange:T,required:!0})]}),s&&(0,a.jsx)(S,{variant:"destructive",children:(0,a.jsx)(I,{children:s})}),(0,a.jsxs)(l.$,{type:"submit",className:"w-full",disabled:e,children:[e&&(0,a.jsx)(F.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Create Account"]})]})})]}),(0,a.jsxs)("div",{className:"mt-6 text-center text-sm text-gray-600",children:["By continuing, you agree to our"," ",(0,a.jsx)(U(),{href:"/terms",className:"text-blue-600 hover:underline",children:"Terms of Service"})," ","and"," ",(0,a.jsx)(U(),{href:"/privacy",className:"text-blue-600 hover:underline",children:"Privacy Policy"})]})]})]})]})})}},844:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>h,A:()=>x});var a=s(5155),r=s(2115),n=s(6203),i=s(5317),l=s(3915),d=s(9509);let o={apiKey:d.env.NEXT_PUBLIC_FIREBASE_API_KEY,authDomain:d.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,projectId:d.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,storageBucket:d.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,messagingSenderId:d.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,appId:d.env.NEXT_PUBLIC_FIREBASE_APP_ID},c=(0,l.Wp)(o),u=(0,n.xI)(c),p=(0,i.aU)(c),m=(0,r.createContext)(void 0),x=()=>{let e=(0,r.useContext)(m);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},h=e=>{let{children:t}=e,[s,l]=(0,r.useState)(null),[d,o]=(0,r.useState)(null),[c,x]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=(0,n.hg)(u,async e=>{if(e){l(e);let t=await (0,i.x7)((0,i.H9)(p,"users",e.uid));t.exists()&&o(t.data())}else l(null),o(null);x(!1)});return()=>e()},[]);let h=async(e,t)=>{await (0,n.x9)(u,e,t)},g=async function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"user",{user:a}=await (0,n.eJ)(u,e,t),r={uid:a.uid,email:a.email,role:s,displayName:a.displayName||"",createdAt:new Date};await (0,i.BN)((0,i.H9)(p,"users",a.uid),r)},v=async()=>{await (0,n.CI)(u)};return(0,a.jsx)(m.Provider,{value:{user:s,userData:d,loading:c,signIn:h,signUp:g,logout:v},children:t})}},851:(e,t,s)=>{Promise.resolve().then(s.bind(s,716))},2523:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(5155);s(2115);var r=s(9434);function n(e){let{className:t,type:s,...n}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>c});var a=s(5155);s(2115);var r=s(9434);function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(2596),r=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,274,645,317,744,441,684,358],()=>t(851)),_N_E=e.O()}]);
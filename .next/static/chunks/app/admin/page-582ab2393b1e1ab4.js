(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{285:(e,s,a)=>{"use strict";a.d(s,{$:()=>d});var t=a(5155);a(2115);var r=a(9708),i=a(2085),n=a(9434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:a,size:i,asChild:d=!1,...c}=e,o=d?r.DX:"button";return(0,t.jsx)(o,{"data-slot":"button",className:(0,n.cn)(l({variant:a,size:i,className:s})),...c})}},381:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},844:(e,s,a)=>{"use strict";a.d(s,{AuthProvider:()=>v,A:()=>h});var t=a(5155),r=a(2115),i=a(6203),n=a(5317),l=a(3915),d=a(9509);let c={apiKey:d.env.NEXT_PUBLIC_FIREBASE_API_KEY,authDomain:d.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,projectId:d.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,storageBucket:d.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,messagingSenderId:d.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,appId:d.env.NEXT_PUBLIC_FIREBASE_APP_ID},o=(0,l.Wp)(c),x=(0,i.xI)(o),u=(0,n.aU)(o),m=(0,r.createContext)(void 0),h=()=>{let e=(0,r.useContext)(m);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},v=e=>{let{children:s}=e,[a,l]=(0,r.useState)(null),[d,c]=(0,r.useState)(null),[o,h]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=(0,i.hg)(x,async e=>{if(e){l(e);let s=await (0,n.x7)((0,n.H9)(u,"users",e.uid));s.exists()&&c(s.data())}else l(null),c(null);h(!1)});return()=>e()},[]);let v=async(e,s)=>{await (0,i.x9)(x,e,s)},p=async function(e,s){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"user",{user:t}=await (0,i.eJ)(x,e,s),r={uid:t.uid,email:t.email,role:a,displayName:t.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(u,"users",t.uid),r)},g=async()=>{await (0,i.CI)(x)};return(0,t.jsx)(m.Provider,{value:{user:a,userData:d,loading:o,signIn:v,signUp:p,logout:g},children:s})}},2822:(e,s,a)=>{Promise.resolve().then(a.bind(a,7127))},4616:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},6126:(e,s,a)=>{"use strict";a.d(s,{E:()=>d});var t=a(5155);a(2115);var r=a(9708),i=a(2085),n=a(9434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:a,asChild:i=!1,...d}=e,c=i?r.DX:"span";return(0,t.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(l({variant:a}),s),...d})}},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>n,wL:()=>o});var t=a(5155);a(2115);var r=a(9434);function i(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...a})}function n(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a})}function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...a})}function o(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",s),...a})}},7127:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>y});var t=a(5155);a(2115);var r=a(844),i=a(6695),n=a(285),l=a(6126),d=a(7580),c=a(7434),o=a(7809),x=a(9946);let u=(0,x.A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var m=a(4616);let h=(0,x.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var v=a(381),p=a(6874),g=a.n(p);let j={totalUsers:15420,totalTemplates:156,totalOrders:3240,totalRevenue:125600},f=[{id:"1",customerEmail:"<EMAIL>",templateName:"Modern Dashboard Pro",amount:49,status:"pending",date:"2024-01-15"},{id:"2",customerEmail:"<EMAIL>",templateName:"E-commerce Store",amount:79,status:"confirmed",date:"2024-01-15"},{id:"3",customerEmail:"<EMAIL>",templateName:"Landing Page Bundle",amount:39,status:"pending",date:"2024-01-14"}];function y(){let{user:e,userData:s}=(0,r.A)();return e&&(null==s?void 0:s.role)==="admin"?(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Admin Dashboard"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage templates, orders, users, and monitor your marketplace performance."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Users"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j.totalUsers.toLocaleString()})]}),(0,t.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,t.jsx)(d.A,{className:"h-6 w-6 text-blue-600"})})]})})}),(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Templates"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j.totalTemplates})]}),(0,t.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,t.jsx)(c.A,{className:"h-6 w-6 text-green-600"})})]})})}),(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Orders"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j.totalOrders.toLocaleString()})]}),(0,t.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,t.jsx)(o.A,{className:"h-6 w-6 text-yellow-600"})})]})})}),(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Revenue"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["$",j.totalRevenue.toLocaleString()]})]}),(0,t.jsx)("div",{className:"p-3 bg-purple-100 rounded-lg",children:(0,t.jsx)(u,{className:"h-6 w-6 text-purple-600"})})]})})})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Recent Orders"}),(0,t.jsx)(i.BT,{children:"Latest orders requiring your attention"})]}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsx)("div",{className:"space-y-4",children:f.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:e.templateName}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.customerEmail," • ",new Date(e.date).toLocaleDateString()]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(l.E,{variant:"confirmed"===e.status?"default":"secondary",children:e.status}),(0,t.jsxs)("span",{className:"font-medium",children:["$",e.amount]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(n.$,{size:"sm",variant:"outline",children:"View"}),"pending"===e.status&&(0,t.jsx)(n.$,{size:"sm",children:"Confirm"})]})]})]},e.id))}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,t.jsx)(g(),{href:"/admin/orders",children:"View All Orders"})})})]})]})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Quick Actions"}),(0,t.jsx)(i.BT,{children:"Common admin tasks"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsx)(n.$,{asChild:!0,className:"w-full justify-start",children:(0,t.jsxs)(g(),{href:"/admin/templates/new",children:[(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Add New Template"]})}),(0,t.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)(g(),{href:"/admin/templates",children:[(0,t.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Manage Templates"]})}),(0,t.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)(g(),{href:"/admin/orders",children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Manage Orders"]})}),(0,t.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)(g(),{href:"/admin/users",children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Manage Users"]})}),(0,t.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)(g(),{href:"/admin/analytics",children:[(0,t.jsx)(h,{className:"mr-2 h-4 w-4"}),"Analytics"]})}),(0,t.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full justify-start",children:(0,t.jsxs)(g(),{href:"/admin/settings",children:[(0,t.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Settings"]})})]})]}),(0,t.jsxs)(i.Zp,{className:"mt-6",children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)(i.ZB,{children:"System Status"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Server Status"}),(0,t.jsx)(l.E,{variant:"default",className:"bg-green-500",children:"Online"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Database"}),(0,t.jsx)(l.E,{variant:"default",className:"bg-green-500",children:"Connected"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Storage"}),(0,t.jsx)(l.E,{variant:"default",className:"bg-green-500",children:"Available"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Last Backup"}),(0,t.jsx)("span",{className:"text-sm text-gray-900",children:"2 hours ago"})]})]})})]})]})]})]}):(0,t.jsxs)("div",{className:"container mx-auto px-4 py-20 text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"You need admin privileges to access this page."}),(0,t.jsx)(n.$,{asChild:!0,children:(0,t.jsx)(g(),{href:"/dashboard",children:"Go to Dashboard"})})]})}},7434:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7580:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7809:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},9434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>i});var t=a(2596),r=a(9688);function i(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,t.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[992,965,274,645,441,684,358],()=>s(2822)),_N_E=e.O()}]);
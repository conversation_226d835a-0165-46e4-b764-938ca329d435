(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{285:(e,s,t)=>{"use strict";t.d(s,{$:()=>l});var a=t(5155);t(2115);var r=t(9708),i=t(2085),n=t(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:s,variant:t,size:i,asChild:l=!1,...c}=e,o=l?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,n.cn)(d({variant:t,size:i,className:s})),...c})}},844:(e,s,t)=>{"use strict";t.d(s,{AuthProvider:()=>v,A:()=>m});var a=t(5155),r=t(2115),i=t(6203),n=t(5317),d=t(3915),l=t(9509);let c={apiKey:l.env.NEXT_PUBLIC_FIREBASE_API_KEY,authDomain:l.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,projectId:l.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,storageBucket:l.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,messagingSenderId:l.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,appId:l.env.NEXT_PUBLIC_FIREBASE_APP_ID},o=(0,d.Wp)(c),u=(0,i.xI)(o),x=(0,n.aU)(o),h=(0,r.createContext)(void 0),m=()=>{let e=(0,r.useContext)(h);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},v=e=>{let{children:s}=e,[t,d]=(0,r.useState)(null),[l,c]=(0,r.useState)(null),[o,m]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=(0,i.hg)(u,async e=>{if(e){d(e);let s=await (0,n.x7)((0,n.H9)(x,"users",e.uid));s.exists()&&c(s.data())}else d(null),c(null);m(!1)});return()=>e()},[]);let v=async(e,s)=>{await (0,i.x9)(u,e,s)},p=async function(e,s){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"user",{user:a}=await (0,i.eJ)(u,e,s),r={uid:a.uid,email:a.email,role:t,displayName:a.displayName||"",createdAt:new Date};await (0,n.BN)((0,n.H9)(x,"users",a.uid),r)},g=async()=>{await (0,i.CI)(u)};return(0,a.jsx)(h.Provider,{value:{user:t,userData:l,loading:o,signIn:v,signUp:p,logout:g},children:s})}},1788:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3420:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var a=t(5155);t(2115);var r=t(844),i=t(6695),n=t(285),d=t(6126),l=t(6151),c=t(1788),o=t(8564);let u=(0,t(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var x=t(2657),h=t(4616),m=t(6874),v=t.n(m);let p={totalPurchases:12,totalDownloads:45,favoriteTemplates:8,pendingOrders:2},g=[{id:"1",templateName:"Modern Dashboard Pro",purchaseDate:"2024-01-15",amount:49,status:"completed"},{id:"2",templateName:"E-commerce Store Template",purchaseDate:"2024-01-10",amount:79,status:"completed"},{id:"3",templateName:"Landing Page Bundle",purchaseDate:"2024-01-08",amount:39,status:"pending"}];function j(){var e;let{user:s,userData:t}=(0,r.A)();return s?(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:["Welcome back, ",(null==t?void 0:t.displayName)||(null==(e=s.email)?void 0:e.split("@")[0]),"!"]}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage your templates, orders, and account settings from your dashboard."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Purchases"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.totalPurchases})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,a.jsx)(l.A,{className:"h-6 w-6 text-blue-600"})})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Downloads"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.totalDownloads})]}),(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,a.jsx)(c.A,{className:"h-6 w-6 text-green-600"})})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Favorites"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.favoriteTemplates})]}),(0,a.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,a.jsx)(o.A,{className:"h-6 w-6 text-yellow-600"})})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Orders"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:p.pendingOrders})]}),(0,a.jsx)("div",{className:"p-3 bg-orange-100 rounded-lg",children:(0,a.jsx)(u,{className:"h-6 w-6 text-orange-600"})})]})})})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Recent Purchases"}),(0,a.jsx)(i.BT,{children:"Your latest template purchases and downloads"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"space-y-4",children:g.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.templateName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Purchased on ",new Date(e.purchaseDate).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(d.E,{variant:"completed"===e.status?"default":"secondary",children:e.status}),(0,a.jsxs)("span",{className:"font-medium",children:["$",e.amount]}),"completed"===e.status&&(0,a.jsxs)(n.$,{size:"sm",variant:"outline",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-1"}),"Download"]})]})]},e.id))}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,a.jsx)(v(),{href:"/orders",children:"View All Orders"})})})]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Quick Actions"}),(0,a.jsx)(i.BT,{children:"Common tasks and shortcuts"})]}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsx)(n.$,{asChild:!0,className:"w-full justify-start",children:(0,a.jsxs)(v(),{href:"/templates",children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Browse Templates"]})}),(0,a.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full justify-start",children:(0,a.jsxs)(v(),{href:"/custom-request",children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Request Custom Design"]})}),(0,a.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full justify-start",children:(0,a.jsxs)(v(),{href:"/orders",children:[(0,a.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"View Orders"]})}),(0,a.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full justify-start",children:(0,a.jsxs)(v(),{href:"/favorites",children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"My Favorites"]})})]})]}),(0,a.jsxs)(i.Zp,{className:"mt-6",children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Account Information"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Email"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:s.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Account Type"}),(0,a.jsx)(d.E,{variant:(null==t?void 0:t.role)==="admin"?"default":"secondary",children:(null==t?void 0:t.role)||"user"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Member Since"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:(null==t?void 0:t.createdAt)?new Date(t.createdAt).toLocaleDateString():"N/A"})]})]})})]})]})]})]}):(0,a.jsxs)("div",{className:"container mx-auto px-4 py-20 text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Please sign in to access your dashboard"}),(0,a.jsx)(n.$,{asChild:!0,children:(0,a.jsx)(v(),{href:"/auth",children:"Sign In"})})]})}},4616:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5171:(e,s,t)=>{Promise.resolve().then(t.bind(t,3420))},6126:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(5155);t(2115);var r=t(9708),i=t(2085),n=t(9434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,asChild:i=!1,...l}=e,c=i?r.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(d({variant:t}),s),...l})}},6151:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>n,wL:()=>o});var a=t(5155);t(2115);var r=t(9434);function i(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function n(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...t})}function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...t})}function o(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",s),...t})}},8564:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9434:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var a=t(2596),r=t(9688);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[992,965,274,645,441,684,358],()=>s(5171)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{157:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4147,23)),Promise.resolve().then(s.t.bind(s,8489,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,1466)),Promise.resolve().then(s.bind(s,844))},285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var a=s(5155);s(2115);var r=s(9708),n=s(2085),i=s(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:n,asChild:o=!1,...l}=e,c=o?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:s,size:n,className:t})),...l})}},347:()=>{},844:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>f,A:()=>x});var a=s(5155),r=s(2115),n=s(6203),i=s(5317),d=s(3915),o=s(9509);let l={apiKey:o.env.NEXT_PUBLIC_FIREBASE_API_KEY,authDomain:o.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,projectId:o.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,storageBucket:o.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,messagingSenderId:o.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,appId:o.env.NEXT_PUBLIC_FIREBASE_APP_ID},c=(0,d.Wp)(l),u=(0,n.xI)(c),h=(0,i.aU)(c),m=(0,r.createContext)(void 0),x=()=>{let e=(0,r.useContext)(m);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},f=e=>{let{children:t}=e,[s,d]=(0,r.useState)(null),[o,l]=(0,r.useState)(null),[c,x]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=(0,n.hg)(u,async e=>{if(e){d(e);let t=await (0,i.x7)((0,i.H9)(h,"users",e.uid));t.exists()&&l(t.data())}else d(null),l(null);x(!1)});return()=>e()},[]);let f=async(e,t)=>{await (0,n.x9)(u,e,t)},v=async function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"user",{user:a}=await (0,n.eJ)(u,e,t),r={uid:a.uid,email:a.email,role:s,displayName:a.displayName||"",createdAt:new Date};await (0,i.BN)((0,i.H9)(h,"users",a.uid),r)},g=async()=>{await (0,n.CI)(u)};return(0,a.jsx)(m.Provider,{value:{user:s,userData:o,loading:c,signIn:f,signUp:v,logout:g},children:t})}},1466:(e,t,s)=>{"use strict";s.d(t,{Navbar:()=>S});var a=s(5155),r=s(2115),n=s(6874),i=s.n(n),d=s(844),o=s(285),l=s(8698),c=s(9434);function u(e){let{...t}=e;return(0,a.jsx)(l.bL,{"data-slot":"dropdown-menu",...t})}function h(e){let{...t}=e;return(0,a.jsx)(l.l9,{"data-slot":"dropdown-menu-trigger",...t})}function m(e){let{className:t,sideOffset:s=4,...r}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsx)(l.UC,{"data-slot":"dropdown-menu-content",sideOffset:s,className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r})})}function x(e){let{className:t,inset:s,variant:r="default",...n}=e;return(0,a.jsx)(l.q7,{"data-slot":"dropdown-menu-item","data-inset":s,"data-variant":r,className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n})}function f(e){let{className:t,...s}=e;return(0,a.jsx)(l.wv,{"data-slot":"dropdown-menu-separator",className:(0,c.cn)("bg-border -mx-1 my-1 h-px",t),...s})}var v=s(4011);function g(e){let{className:t,...s}=e;return(0,a.jsx)(v.bL,{"data-slot":"avatar",className:(0,c.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...s})}function p(e){let{className:t,...s}=e;return(0,a.jsx)(v.H4,{"data-slot":"avatar-fallback",className:(0,c.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...s})}var b=s(5452),j=s(4416);function N(e){let{...t}=e;return(0,a.jsx)(b.bL,{"data-slot":"sheet",...t})}function w(e){let{...t}=e;return(0,a.jsx)(b.l9,{"data-slot":"sheet-trigger",...t})}function y(e){let{...t}=e;return(0,a.jsx)(b.ZL,{"data-slot":"sheet-portal",...t})}function _(e){let{className:t,...s}=e;return(0,a.jsx)(b.hJ,{"data-slot":"sheet-overlay",className:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...s})}function C(e){let{className:t,children:s,side:r="right",...n}=e;return(0,a.jsxs)(y,{children:[(0,a.jsx)(_,{}),(0,a.jsxs)(b.UC,{"data-slot":"sheet-content",className:(0,c.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===r&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===r&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===r&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===r&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...n,children:[s,(0,a.jsxs)(b.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,a.jsx)(j.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}var E=s(381),k=s(1007),A=s(6151),I=s(4835),P=s(4783);let S=()=>{var e,t,s;let{user:n,userData:l,logout:c}=(0,d.A)(),[v,b]=(0,r.useState)(!1),j=async()=>{try{await c()}catch(e){console.error("Error logging out:",e)}},y=()=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i(),{href:"/",className:"text-sm font-medium hover:text-primary transition-colors",children:"Home"}),(0,a.jsx)(i(),{href:"/templates",className:"text-sm font-medium hover:text-primary transition-colors",children:"Templates"}),(0,a.jsx)(i(),{href:"/categories",className:"text-sm font-medium hover:text-primary transition-colors",children:"Categories"}),(0,a.jsx)(i(),{href:"/custom-request",className:"text-sm font-medium hover:text-primary transition-colors",children:"Custom Request"}),(0,a.jsx)(i(),{href:"/contact",className:"text-sm font-medium hover:text-primary transition-colors",children:"Contact"})]});return(0,a.jsx)("nav",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,a.jsxs)(i(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"h-8 w-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:"K"})}),(0,a.jsx)("span",{className:"font-bold text-xl",children:"KaleidoneX"})]}),(0,a.jsx)("div",{className:"hidden md:flex items-center space-x-6",children:(0,a.jsx)(y,{})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[n?(0,a.jsxs)(a.Fragment,{children:[(null==l?void 0:l.role)==="admin"&&(0,a.jsx)(o.$,{asChild:!0,variant:"outline",size:"sm",children:(0,a.jsxs)(i(),{href:"/admin",children:[(0,a.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Admin"]})}),(0,a.jsxs)(u,{children:[(0,a.jsx)(h,{asChild:!0,children:(0,a.jsx)(o.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,a.jsx)(g,{className:"h-8 w-8",children:(0,a.jsx)(p,{children:(null==l||null==(e=l.displayName)?void 0:e[0])||(null==(s=n.email)||null==(t=s[0])?void 0:t.toUpperCase())})})})}),(0,a.jsxs)(m,{className:"w-56",align:"end",forceMount:!0,children:[(0,a.jsx)(x,{asChild:!0,children:(0,a.jsxs)(i(),{href:"/dashboard",className:"flex items-center",children:[(0,a.jsx)(k.A,{className:"mr-2 h-4 w-4"}),"Dashboard"]})}),(0,a.jsx)(x,{asChild:!0,children:(0,a.jsxs)(i(),{href:"/orders",className:"flex items-center",children:[(0,a.jsx)(A.A,{className:"mr-2 h-4 w-4"}),"My Orders"]})}),(0,a.jsx)(f,{}),(0,a.jsxs)(x,{onClick:j,children:[(0,a.jsx)(I.A,{className:"mr-2 h-4 w-4"}),"Log out"]})]})]})]}):(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-2",children:[(0,a.jsx)(o.$,{asChild:!0,variant:"ghost",children:(0,a.jsx)(i(),{href:"/auth",children:"Sign In"})}),(0,a.jsx)(o.$,{asChild:!0,children:(0,a.jsx)(i(),{href:"/auth?mode=signup",children:"Get Started"})})]}),(0,a.jsxs)(N,{open:v,onOpenChange:b,children:[(0,a.jsx)(w,{asChild:!0,className:"md:hidden",children:(0,a.jsx)(o.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(P.A,{className:"h-5 w-5"})})}),(0,a.jsx)(C,{side:"right",className:"w-[300px] sm:w-[400px]",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-4 mt-6",children:[(0,a.jsx)(y,{}),!n&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.$,{asChild:!0,variant:"ghost",className:"justify-start",children:(0,a.jsx)(i(),{href:"/auth",children:"Sign In"})}),(0,a.jsx)(o.$,{asChild:!0,className:"justify-start",children:(0,a.jsx)(i(),{href:"/auth?mode=signup",children:"Get Started"})})]})]})})]})]})]})})})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(2596),r=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[896,992,965,274,645,317,558,441,684,358],()=>t(157)),_N_E=e.O()}]);
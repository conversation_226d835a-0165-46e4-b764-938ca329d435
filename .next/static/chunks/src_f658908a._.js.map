{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebaseServices.ts"], "sourcesContent": ["import { \n  collection, \n  doc, \n  getDocs, \n  getDoc, \n  addDoc, \n  updateDoc, \n  deleteDoc, \n  query, \n  orderBy, \n  limit, \n  where,\n  onSnapshot,\n  Timestamp\n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport { CustomRequest, User, ContactMessage } from '@/types';\n\n// Custom Requests Services\nexport const createCustomRequest = async (requestData: Omit<CustomRequest, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'customRequests'), {\n      ...requestData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating custom request:', error);\n    throw error;\n  }\n};\n\nexport const getCustomRequests = async () => {\n  try {\n    const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n  } catch (error) {\n    console.error('Error fetching custom requests:', error);\n    throw error;\n  }\n};\n\nexport const updateCustomRequestStatus = async (requestId: string, status: CustomRequest['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n    \n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n    \n    await updateDoc(doc(db, 'customRequests', requestId), updateData);\n  } catch (error) {\n    console.error('Error updating custom request:', error);\n    throw error;\n  }\n};\n\n// User Management Services\nexport const getAllUsers = async () => {\n  try {\n    const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n  } catch (error) {\n    console.error('Error fetching users:', error);\n    throw error;\n  }\n};\n\nexport const getUserById = async (userId: string) => {\n  try {\n    const docRef = doc(db, 'users', userId);\n    const docSnap = await getDoc(docRef);\n    \n    if (docSnap.exists()) {\n      return { id: docSnap.id, ...docSnap.data() } as User;\n    } else {\n      throw new Error('User not found');\n    }\n  } catch (error) {\n    console.error('Error fetching user:', error);\n    throw error;\n  }\n};\n\n// Dashboard Statistics Services\nexport const getDashboardStats = async () => {\n  try {\n    const [usersSnapshot, templatesSnapshot, requestsSnapshot] = await Promise.all([\n      getDocs(collection(db, 'users')),\n      getDocs(collection(db, 'templates')),\n      getDocs(collection(db, 'customRequests'))\n    ]);\n\n    const totalUsers = usersSnapshot.size;\n    const totalTemplates = templatesSnapshot.size;\n    const totalRequests = requestsSnapshot.size;\n    \n    // Calculate pending requests\n    const pendingRequests = requestsSnapshot.docs.filter(\n      doc => doc.data().status === 'pending'\n    ).length;\n\n    return {\n      totalUsers,\n      totalTemplates,\n      totalRequests,\n      pendingRequests,\n      totalSales: 0, // Placeholder for sales data\n      customizations: 0 // Placeholder for customizations data\n    };\n  } catch (error) {\n    console.error('Error fetching dashboard stats:', error);\n    throw error;\n  }\n};\n\n// Real-time listeners\nexport const subscribeToCustomRequests = (callback: (requests: CustomRequest[]) => void) => {\n  const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const requests = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n    callback(requests);\n  });\n};\n\nexport const subscribeToUsers = (callback: (users: User[]) => void) => {\n  const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const users = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n    callback(users);\n  });\n};\n\n// Contact Messages Services\nexport const getContactMessages = async () => {\n  try {\n    const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n  } catch (error) {\n    console.error('Error fetching contact messages:', error);\n    throw error;\n  }\n};\n\nexport const updateContactMessageStatus = async (messageId: string, status: ContactMessage['status']) => {\n  try {\n    await updateDoc(doc(db, 'contactMessages', messageId), {\n      status,\n      updatedAt: new Date()\n    });\n  } catch (error) {\n    console.error('Error updating contact message:', error);\n    throw error;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAeA;;;AAIO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,mBAAmB;YAC5D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACvE,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,4BAA4B,OAAO,WAAmB,QAAiC;IAClG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,kBAAkB,YAAY;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAC9D,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE;QAE7B,IAAI,QAAQ,MAAM,IAAI;YACpB,OAAO;gBAAE,IAAI,QAAQ,EAAE;gBAAE,GAAG,QAAQ,IAAI,EAAE;YAAC;QAC7C,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,CAAC,eAAe,mBAAmB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC7E,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE;SACxB;QAED,MAAM,aAAa,cAAc,IAAI;QACrC,MAAM,iBAAiB,kBAAkB,IAAI;QAC7C,MAAM,gBAAgB,iBAAiB,IAAI;QAE3C,6BAA6B;QAC7B,MAAM,kBAAkB,iBAAiB,IAAI,CAAC,MAAM,CAClD,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,KAAK,WAC7B,MAAM;QAER,OAAO;YACL;YACA;YACA;YACA;YACA,YAAY;YACZ,gBAAgB,EAAE,sCAAsC;QAC1D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,4BAA4B,CAAC;IACxC,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAEvE,OAAO,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAE9D,OAAO,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,QAAQ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAGO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACxE,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,OAAO,WAAmB;IAClE,IAAI;QACF,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,mBAAmB,YAAY;YACrD;YACA,WAAW,IAAI;QACjB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  Users,\n  FileText,\n  ShoppingCart,\n  DollarSign,\n  Plus,\n  Eye,\n  Settings,\n  BarChart3,\n  Database,\n  MessageSquare,\n  Palette,\n  Download,\n  Phone\n} from 'lucide-react';\nimport Link from 'next/link';\nimport {\n  getDashboardStats,\n  getCustomRequests,\n  getAllUsers,\n  updateCustomRequestStatus,\n  subscribeToCustomRequests\n} from '@/lib/firebaseServices';\nimport { CustomRequest, User } from '@/types';\n\ninterface DashboardStats {\n  totalUsers: number;\n  totalTemplates: number;\n  totalRequests: number;\n  pendingRequests: number;\n  totalSales: number;\n  customizations: number;\n}\n\nexport default function AdminDashboard() {\n  const { user, userData } = useAuth();\n  const [stats, setStats] = useState<DashboardStats>({\n    totalUsers: 0,\n    totalTemplates: 0,\n    totalRequests: 0,\n    pendingRequests: 0,\n    totalSales: 0,\n    customizations: 0\n  });\n  const [customRequests, setCustomRequests] = useState<CustomRequest[]>([]);\n  const [recentUsers, setRecentUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [activeTab, setActiveTab] = useState('templates');\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Fetch dashboard stats\n        const dashboardStats = await getDashboardStats();\n        setStats(dashboardStats);\n\n        // Fetch custom requests\n        const requests = await getCustomRequests();\n        setCustomRequests(requests.slice(0, 5)); // Show only recent 5\n\n        // Fetch recent users\n        const users = await getAllUsers();\n        setRecentUsers(users.slice(0, 5)); // Show only recent 5\n\n      } catch (error: any) {\n        console.error('Error fetching admin data:', error);\n        setError('Failed to load dashboard data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (user && userData?.role === 'admin') {\n      fetchData();\n\n      // Set up real-time listener for custom requests\n      const unsubscribe = subscribeToCustomRequests((requests) => {\n        setCustomRequests(requests.slice(0, 5));\n      });\n\n      return () => unsubscribe();\n    }\n  }, [user, userData]);\n\n  const handleUpdateRequestStatus = async (requestId: string, status: CustomRequest['status']) => {\n    try {\n      await updateCustomRequestStatus(requestId, status);\n      // The real-time listener will update the UI automatically\n    } catch (error: any) {\n      console.error('Error updating request status:', error);\n      setError('Failed to update request status');\n    }\n  };\n\n  if (!user || userData?.role !== 'admin') {\n    return (\n      <div className=\"container mx-auto px-4 py-20 text-center\">\n        <h1 className=\"text-2xl font-bold mb-4\">Access Denied</h1>\n        <p className=\"text-gray-600 mb-4\">You need admin privileges to access this page.</p>\n        <Button asChild>\n          <Link href=\"/dashboard\">Go to Dashboard</Link>\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                Admin Dashboard\n              </h1>\n              <p className=\"text-gray-600\">\n                Manage your marketplace and monitor performance\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Button asChild>\n                <Link href=\"/admin/add-template\">\n                  <Plus className=\"mr-2 h-4 w-4\" />\n                  Add Template\n                </Link>\n              </Button>\n              <Button asChild variant=\"outline\">\n                <Link href=\"/admin/setup\">\n                  <Download className=\"mr-2 h-4 w-4\" />\n                  Setup Sample Data\n                </Link>\n              </Button>\n            </div>\n          </div>\n        </div>\n\n      {/* Loading State */}\n      {loading && (\n        <div className=\"flex justify-center items-center py-12\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Loading dashboard data...</p>\n          </div>\n        </div>\n      )}\n\n      {/* Error State */}\n      {error && (\n        <div className=\"mb-8 p-4 bg-red-50 border border-red-200 rounded-lg\">\n          <p className=\"text-red-800\">{error}</p>\n        </div>\n      )}\n\n        {/* Stats Cards */}\n        {!loading && (\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 mb-8\">\n            {/* Templates Card */}\n            <Card className=\"bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-blue-100 text-sm font-medium mb-1\">Templates</p>\n                    <p className=\"text-3xl font-bold\">{stats.totalTemplates}</p>\n                    <p className=\"text-blue-100 text-xs\">Available templates</p>\n                  </div>\n                  <div className=\"p-3 bg-white/20 rounded-lg\">\n                    <FileText className=\"h-8 w-8 text-white\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Contact Requests Card */}\n            <Card className=\"bg-gradient-to-r from-green-500 to-green-600 text-white border-0\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-green-100 text-sm font-medium mb-1\">Contact Requests</p>\n                    <p className=\"text-3xl font-bold\">{stats.totalRequests}</p>\n                    <p className=\"text-green-100 text-xs\">Customer inquiries</p>\n                  </div>\n                  <div className=\"p-3 bg-white/20 rounded-lg\">\n                    <MessageSquare className=\"h-8 w-8 text-white\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Total Sales Card */}\n            <Card className=\"bg-gradient-to-r from-purple-500 to-purple-600 text-white border-0\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-purple-100 text-sm font-medium mb-1\">Total Sales</p>\n                    <p className=\"text-3xl font-bold\">{stats.pendingRequests}</p>\n                    <p className=\"text-purple-100 text-xs\">No revenue</p>\n                  </div>\n                  <div className=\"p-3 bg-white/20 rounded-lg\">\n                    <DollarSign className=\"h-8 w-8 text-white\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Customizations Card */}\n            <Card className=\"bg-gradient-to-r from-orange-500 to-orange-600 text-white border-0\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-orange-100 text-sm font-medium mb-1\">Customizations</p>\n                    <p className=\"text-3xl font-bold\">{stats.customizations}</p>\n                    <p className=\"text-orange-100 text-xs\">Total customizations</p>\n                  </div>\n                  <div className=\"p-3 bg-white/20 rounded-lg\">\n                    <Palette className=\"h-8 w-8 text-white\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Users Card */}\n            <Card className=\"bg-gradient-to-r from-cyan-500 to-cyan-600 text-white border-0\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-cyan-100 text-sm font-medium mb-1\">Users</p>\n                    <p className=\"text-3xl font-bold\">{stats.totalUsers}</p>\n                    <p className=\"text-cyan-100 text-xs\">Site visitors</p>\n                  </div>\n                  <div className=\"p-3 bg-white/20 rounded-lg\">\n                    <Users className=\"h-8 w-8 text-white\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Navigation Tabs */}\n        <div className=\"mb-8\">\n          <div className=\"border-b border-gray-200\">\n            <nav className=\"-mb-px flex space-x-8\">\n              <button\n                onClick={() => setActiveTab('templates')}\n                className={`border-b-2 py-2 px-1 text-sm font-medium ${\n                  activeTab === 'templates'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Templates Management\n              </button>\n              <button\n                onClick={() => setActiveTab('requests')}\n                className={`border-b-2 py-2 px-1 text-sm font-medium ${\n                  activeTab === 'requests'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Contact Requests\n              </button>\n              <button\n                onClick={() => setActiveTab('purchases')}\n                className={`border-b-2 py-2 px-1 text-sm font-medium ${\n                  activeTab === 'purchases'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Purchases\n              </button>\n              <button\n                onClick={() => setActiveTab('customizations')}\n                className={`border-b-2 py-2 px-1 text-sm font-medium ${\n                  activeTab === 'customizations'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Customizations\n              </button>\n            </nav>\n          </div>\n        </div>\n\n        {!loading && (\n          <div className=\"grid lg:grid-cols-3 gap-8\">\n            {/* Content based on active tab */}\n            <div className=\"lg:col-span-2\">\n              {activeTab === 'templates' && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle className=\"flex items-center justify-between\">\n                      Templates Management\n                      <Button asChild size=\"sm\">\n                        <Link href=\"/admin/add-template\">\n                          <Plus className=\"mr-2 h-4 w-4\" />\n                          Add Template\n                        </Link>\n                      </Button>\n                    </CardTitle>\n                    <CardDescription>\n                      Manage your template collection\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-4\">\n                      {/* Sample template items */}\n                      <div className=\"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50\">\n                        <div className=\"flex items-center space-x-4\">\n                          <div className=\"w-16 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg\"></div>\n                          <div>\n                            <h4 className=\"font-medium text-gray-900\">SaaS Dashboard Pro</h4>\n                            <p className=\"text-sm text-gray-600\">Technology</p>\n                            <p className=\"text-sm text-green-600 font-medium\">₹2499</p>\n                          </div>\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          <Button size=\"sm\" variant=\"outline\">\n                            <Eye className=\"mr-2 h-4 w-4\" />\n                            Preview\n                          </Button>\n                          <Button size=\"sm\" variant=\"destructive\">\n                            Delete\n                          </Button>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50\">\n                        <div className=\"flex items-center space-x-4\">\n                          <div className=\"w-16 h-12 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg\"></div>\n                          <div>\n                            <h4 className=\"font-medium text-gray-900\">Free Startup Landing</h4>\n                            <p className=\"text-sm text-gray-600\">Business</p>\n                            <p className=\"text-sm text-green-600 font-medium\">Free</p>\n                          </div>\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          <Button size=\"sm\" variant=\"outline\">\n                            <Eye className=\"mr-2 h-4 w-4\" />\n                            Preview\n                          </Button>\n                          <Button size=\"sm\" variant=\"destructive\">\n                            Delete\n                          </Button>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50\">\n                        <div className=\"flex items-center space-x-4\">\n                          <div className=\"w-16 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg\"></div>\n                          <div>\n                            <h4 className=\"font-medium text-gray-900\">Education Platform</h4>\n                            <p className=\"text-sm text-gray-600\">Education</p>\n                            <p className=\"text-sm text-green-600 font-medium\">₹3499</p>\n                          </div>\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          <Button size=\"sm\" variant=\"outline\">\n                            <Eye className=\"mr-2 h-4 w-4\" />\n                            Preview\n                          </Button>\n                          <Button size=\"sm\" variant=\"destructive\">\n                            Delete\n                          </Button>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"mt-6\">\n                      <Button asChild variant=\"outline\" className=\"w-full\">\n                        <Link href=\"/templates\">View All Templates</Link>\n                      </Button>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {activeTab === 'requests' && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle>Contact Requests</CardTitle>\n                    <CardDescription>\n                      Manage customer inquiries and requests\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-4\">\n                      <div className=\"p-4 border rounded-lg\">\n                        <div className=\"flex items-start justify-between\">\n                          <div>\n                            <h4 className=\"font-medium text-gray-900\">John Doe</h4>\n                            <p className=\"text-sm text-gray-600\"><EMAIL></p>\n                            <p className=\"text-sm text-gray-500 mt-2\">Need a custom e-commerce template for my business...</p>\n                          </div>\n                          <Badge variant=\"outline\">New</Badge>\n                        </div>\n                      </div>\n                      <div className=\"p-4 border rounded-lg\">\n                        <div className=\"flex items-start justify-between\">\n                          <div>\n                            <h4 className=\"font-medium text-gray-900\">Jane Smith</h4>\n                            <p className=\"text-sm text-gray-600\"><EMAIL></p>\n                            <p className=\"text-sm text-gray-500 mt-2\">Looking for a portfolio template with dark theme...</p>\n                          </div>\n                          <Badge variant=\"secondary\">In Progress</Badge>\n                        </div>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {activeTab === 'purchases' && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle>Recent Purchases</CardTitle>\n                    <CardDescription>\n                      Track template sales and downloads\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-4\">\n                      <div className=\"p-4 border rounded-lg\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h4 className=\"font-medium text-gray-900\">SaaS Dashboard Pro</h4>\n                            <p className=\"text-sm text-gray-600\">Purchased by: <EMAIL></p>\n                            <p className=\"text-sm text-gray-500\">2 hours ago</p>\n                          </div>\n                          <span className=\"text-green-600 font-medium\">₹2499</span>\n                        </div>\n                      </div>\n                      <div className=\"p-4 border rounded-lg\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h4 className=\"font-medium text-gray-900\">Education Platform</h4>\n                            <p className=\"text-sm text-gray-600\">Purchased by: <EMAIL></p>\n                            <p className=\"text-sm text-gray-500\">1 day ago</p>\n                          </div>\n                          <span className=\"text-green-600 font-medium\">₹3499</span>\n                        </div>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {activeTab === 'customizations' && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle>Customization Requests</CardTitle>\n                    <CardDescription>\n                      Manage custom template requests\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-4\">\n                      <div className=\"p-4 border rounded-lg\">\n                        <div className=\"flex items-start justify-between\">\n                          <div>\n                            <h4 className=\"font-medium text-gray-900\">Custom Dashboard</h4>\n                            <p className=\"text-sm text-gray-600\">Client: <EMAIL></p>\n                            <p className=\"text-sm text-gray-500 mt-2\">Need a custom admin dashboard with specific features...</p>\n                          </div>\n                          <Badge variant=\"outline\">Pending</Badge>\n                        </div>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n\n            {/* Quick Actions */}\n            <div>\n              <Card>\n                <CardHeader>\n                  <CardTitle>Quick Actions</CardTitle>\n                  <CardDescription>\n                    Common admin tasks\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <Button\n                    onClick={() => setActiveTab('templates')}\n                    variant={activeTab === 'templates' ? 'default' : 'outline'}\n                    className=\"w-full justify-start\"\n                  >\n                    <FileText className=\"mr-2 h-4 w-4\" />\n                    Templates\n                  </Button>\n\n                  <Button\n                    onClick={() => setActiveTab('requests')}\n                    variant={activeTab === 'requests' ? 'default' : 'outline'}\n                    className=\"w-full justify-start\"\n                  >\n                    <MessageSquare className=\"mr-2 h-4 w-4\" />\n                    Contact Requests\n                  </Button>\n\n                  <Button asChild variant=\"outline\" className=\"w-full justify-start\">\n                    <Link href=\"/templates\">\n                      <Eye className=\"mr-2 h-4 w-4\" />\n                      View Templates\n                    </Link>\n                  </Button>\n\n                  <Button asChild variant=\"outline\" className=\"w-full justify-start\">\n                    <Link href=\"/contact\">\n                      <Phone className=\"mr-2 h-4 w-4\" />\n                      Contact Page\n                    </Link>\n                  </Button>\n                </CardContent>\n              </Card>\n\n              {/* System Status */}\n              <Card className=\"mt-6\">\n                <CardHeader>\n                  <CardTitle>System Status</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <Database className=\"w-4 h-4 mr-2 text-green-600\" />\n                        <span className=\"text-sm text-gray-600\">Firebase</span>\n                      </div>\n                      <Badge variant=\"default\" className=\"bg-green-500\">Connected</Badge>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <Database className=\"w-4 h-4 mr-2 text-green-600\" />\n                        <span className=\"text-sm text-gray-600\">Firestore</span>\n                      </div>\n                      <Badge variant=\"default\" className=\"bg-green-500\">Active</Badge>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <Users className=\"w-4 h-4 mr-2 text-green-600\" />\n                        <span className=\"text-sm text-gray-600\">Authentication</span>\n                      </div>\n                      <Badge variant=\"default\" className=\"bg-green-500\">Working</Badge>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <FileText className=\"w-4 h-4 mr-2 text-gray-600\" />\n                        <span className=\"text-sm text-gray-600\">Total Templates</span>\n                      </div>\n                      <span className=\"text-sm text-gray-900\">{stats.totalTemplates}</span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;;;AAvBA;;;;;;;;;AAyCe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,YAAY;QACZ,gBAAgB;QAChB,eAAe;QACf,iBAAiB;QACjB,YAAY;QACZ,gBAAgB;IAClB;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;sDAAY;oBAChB,IAAI;wBACF,WAAW;wBAEX,wBAAwB;wBACxB,MAAM,iBAAiB,MAAM,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD;wBAC7C,SAAS;wBAET,wBAAwB;wBACxB,MAAM,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD;wBACvC,kBAAkB,SAAS,KAAK,CAAC,GAAG,KAAK,qBAAqB;wBAE9D,qBAAqB;wBACrB,MAAM,QAAQ,MAAM,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD;wBAC9B,eAAe,MAAM,KAAK,CAAC,GAAG,KAAK,qBAAqB;oBAE1D,EAAE,OAAO,OAAY;wBACnB,QAAQ,KAAK,CAAC,8BAA8B;wBAC5C,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA,IAAI,QAAQ,UAAU,SAAS,SAAS;gBACtC;gBAEA,gDAAgD;gBAChD,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,4BAAyB,AAAD;4DAAE,CAAC;wBAC7C,kBAAkB,SAAS,KAAK,CAAC,GAAG;oBACtC;;gBAEA;gDAAO,IAAM;;YACf;QACF;mCAAG;QAAC;QAAM;KAAS;IAEnB,MAAM,4BAA4B,OAAO,WAAmB;QAC1D,IAAI;YACF,MAAM,CAAA,GAAA,iIAAA,CAAA,4BAAyB,AAAD,EAAE,WAAW;QAC3C,0DAA0D;QAC5D,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACX;IACF;IAEA,IAAI,CAAC,QAAQ,UAAU,SAAS,SAAS;QACvC,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAClC,6LAAC,qIAAA,CAAA,SAAM;oBAAC,OAAO;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAa;;;;;;;;;;;;;;;;;IAIhC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,OAAO;kDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;;8DACT,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIrC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,SAAQ;kDACtB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;;8DACT,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAShD,yBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;gBAMlC,uBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;gBAK9B,CAAC,yBACA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;8DACtD,6LAAC;oDAAE,WAAU;8DAAsB,MAAM,cAAc;;;;;;8DACvD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5B,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA0C;;;;;;8DACvD,6LAAC;oDAAE,WAAU;8DAAsB,MAAM,aAAa;;;;;;8DACtD,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;sDAExC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOjC,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;8DACxD,6LAAC;oDAAE,WAAU;8DAAsB,MAAM,eAAe;;;;;;8DACxD,6LAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO9B,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;8DACxD,6LAAC;oDAAE,WAAU;8DAAsB,MAAM,cAAc;;;;;;8DACvD,6LAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO3B,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;8DACtD,6LAAC;oDAAE,WAAU;8DAAsB,MAAM,UAAU;;;;;;8DACnD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS7B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,cACV,kCACA,8EACJ;8CACH;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,kCACA,8EACJ;8CACH;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,cACV,kCACA,8EACJ;8CACH;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,mBACV,kCACA,8EACJ;8CACH;;;;;;;;;;;;;;;;;;;;;;gBAON,CAAC,yBACA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;gCACZ,cAAc,6BACb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;wDAAoC;sEAEvD,6LAAC,qIAAA,CAAA,SAAM;4DAAC,OAAO;4DAAC,MAAK;sEACnB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;;kFACT,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;8DAKvC,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;;8FACC,6LAAC;oFAAG,WAAU;8FAA4B;;;;;;8FAC1C,6LAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6LAAC;oFAAE,WAAU;8FAAqC;;;;;;;;;;;;;;;;;;8EAGtD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAQ;;8FACxB,6LAAC,mMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGlC,6LAAC,qIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAQ;sFAAc;;;;;;;;;;;;;;;;;;sEAM5C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;;8FACC,6LAAC;oFAAG,WAAU;8FAA4B;;;;;;8FAC1C,6LAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6LAAC;oFAAE,WAAU;8FAAqC;;;;;;;;;;;;;;;;;;8EAGtD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAQ;;8FACxB,6LAAC,mMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGlC,6LAAC,qIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAQ;sFAAc;;;;;;;;;;;;;;;;;;sEAM5C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;;8FACC,6LAAC;oFAAG,WAAU;8FAA4B;;;;;;8FAC1C,6LAAC;oFAAE,WAAU;8FAAwB;;;;;;8FACrC,6LAAC;oFAAE,WAAU;8FAAqC;;;;;;;;;;;;;;;;;;8EAGtD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAQ;;8FACxB,6LAAC,mMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGlC,6LAAC,qIAAA,CAAA,SAAM;4EAAC,MAAK;4EAAK,SAAQ;sFAAc;;;;;;;;;;;;;;;;;;;;;;;;8DAM9C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAC,SAAQ;wDAAU,WAAU;kEAC1C,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAOjC,cAAc,4BACb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAA4B;;;;;;sFAC1C,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;sFACrC,6LAAC;4EAAE,WAAU;sFAA6B;;;;;;;;;;;;8EAE5C,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAU;;;;;;;;;;;;;;;;;kEAG7B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAA4B;;;;;;sFAC1C,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;sFACrC,6LAAC;4EAAE,WAAU;sFAA6B;;;;;;;;;;;;8EAE5C,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQtC,cAAc,6BACb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAA4B;;;;;;sFAC1C,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;sFACrC,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAEvC,6LAAC;oEAAK,WAAU;8EAA6B;;;;;;;;;;;;;;;;;kEAGjD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAA4B;;;;;;sFAC1C,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;sFACrC,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAEvC,6LAAC;oEAAK,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQxD,cAAc,kCACb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAA4B;;;;;;kFAC1C,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;kFACrC,6LAAC;wEAAE,WAAU;kFAA6B;;;;;;;;;;;;0EAE5C,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUvC,6LAAC;;8CACC,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,aAAa;oDAC5B,SAAS,cAAc,cAAc,YAAY;oDACjD,WAAU;;sEAEV,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAIvC,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,aAAa;oDAC5B,SAAS,cAAc,aAAa,YAAY;oDAChD,WAAU;;sEAEV,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAI5C,6LAAC,qIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAC,SAAQ;oDAAU,WAAU;8DAC1C,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;;0EACT,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;8DAKpC,6LAAC,qIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAC,SAAQ;oDAAU,WAAU;8DAC1C,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;;0EACT,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;8CAQ1C,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAE1C,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAAe;;;;;;;;;;;;kEAEpD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAE1C,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAAe;;;;;;;;;;;;kEAEpD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAE1C,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAAe;;;;;;;;;;;;kEAEpD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,iNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAE1C,6LAAC;gEAAK,WAAU;0EAAyB,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnF;GAphBwB;;QACK,kIAAA,CAAA,UAAO;;;KADZ", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 543, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebaseServices.ts"], "sourcesContent": ["import { \n  collection, \n  doc, \n  getDocs, \n  getDoc, \n  addDoc, \n  updateDoc, \n  deleteDoc, \n  query, \n  orderBy, \n  limit, \n  where,\n  onSnapshot,\n  Timestamp\n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport { CustomRequest, User, ContactMessage } from '@/types';\n\n// Custom Requests Services\nexport const createCustomRequest = async (requestData: Omit<CustomRequest, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'customRequests'), {\n      ...requestData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating custom request:', error);\n    throw error;\n  }\n};\n\nexport const getCustomRequests = async () => {\n  try {\n    const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n  } catch (error) {\n    console.error('Error fetching custom requests:', error);\n    throw error;\n  }\n};\n\nexport const updateCustomRequestStatus = async (requestId: string, status: CustomRequest['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n    \n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n    \n    await updateDoc(doc(db, 'customRequests', requestId), updateData);\n  } catch (error) {\n    console.error('Error updating custom request:', error);\n    throw error;\n  }\n};\n\n// User Management Services\nexport const getAllUsers = async () => {\n  try {\n    const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n  } catch (error) {\n    console.error('Error fetching users:', error);\n    throw error;\n  }\n};\n\nexport const getUserById = async (userId: string) => {\n  try {\n    const docRef = doc(db, 'users', userId);\n    const docSnap = await getDoc(docRef);\n    \n    if (docSnap.exists()) {\n      return { id: docSnap.id, ...docSnap.data() } as User;\n    } else {\n      throw new Error('User not found');\n    }\n  } catch (error) {\n    console.error('Error fetching user:', error);\n    throw error;\n  }\n};\n\n// Dashboard Statistics Services\nexport const getDashboardStats = async () => {\n  try {\n    const [usersSnapshot, templatesSnapshot, requestsSnapshot] = await Promise.all([\n      getDocs(collection(db, 'users')),\n      getDocs(collection(db, 'templates')),\n      getDocs(collection(db, 'customRequests'))\n    ]);\n\n    const totalUsers = usersSnapshot.size;\n    const totalTemplates = templatesSnapshot.size;\n    const totalRequests = requestsSnapshot.size;\n    \n    // Calculate pending requests\n    const pendingRequests = requestsSnapshot.docs.filter(\n      doc => doc.data().status === 'pending'\n    ).length;\n\n    return {\n      totalUsers,\n      totalTemplates,\n      totalRequests,\n      pendingRequests,\n      totalSales: 0, // Placeholder for sales data\n      customizations: 0 // Placeholder for customizations data\n    };\n  } catch (error) {\n    console.error('Error fetching dashboard stats:', error);\n    throw error;\n  }\n};\n\n// Real-time listeners\nexport const subscribeToCustomRequests = (callback: (requests: CustomRequest[]) => void) => {\n  const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const requests = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n    callback(requests);\n  });\n};\n\nexport const subscribeToUsers = (callback: (users: User[]) => void) => {\n  const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const users = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n    callback(users);\n  });\n};\n\n// Contact Messages Services\nexport const getContactMessages = async () => {\n  try {\n    const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n  } catch (error) {\n    console.error('Error fetching contact messages:', error);\n    throw error;\n  }\n};\n\nexport const updateContactMessageStatus = async (messageId: string, status: ContactMessage['status']) => {\n  try {\n    await updateDoc(doc(db, 'contactMessages', messageId), {\n      status,\n      updatedAt: new Date()\n    });\n  } catch (error) {\n    console.error('Error updating contact message:', error);\n    throw error;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAeA;;;AAIO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,mBAAmB;YAC5D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACvE,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,4BAA4B,OAAO,WAAmB,QAAiC;IAClG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,kBAAkB,YAAY;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAC9D,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE;QAE7B,IAAI,QAAQ,MAAM,IAAI;YACpB,OAAO;gBAAE,IAAI,QAAQ,EAAE;gBAAE,GAAG,QAAQ,IAAI,EAAE;YAAC;QAC7C,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,CAAC,eAAe,mBAAmB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC7E,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE;SACxB;QAED,MAAM,aAAa,cAAc,IAAI;QACrC,MAAM,iBAAiB,kBAAkB,IAAI;QAC7C,MAAM,gBAAgB,iBAAiB,IAAI;QAE3C,6BAA6B;QAC7B,MAAM,kBAAkB,iBAAiB,IAAI,CAAC,MAAM,CAClD,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,KAAK,WAC7B,MAAM;QAER,OAAO;YACL;YACA;YACA;YACA;YACA,YAAY;YACZ,gBAAgB,EAAE,sCAAsC;QAC1D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,4BAA4B,CAAC;IACxC,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAEvE,OAAO,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAE9D,OAAO,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,QAAQ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAGO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACxE,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,OAAO,WAAmB;IAClE,IAAI;QACF,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,mBAAmB,YAAY;YACrD;YACA,WAAW,IAAI;QACjB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/app/custom-request/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  Loader2,\n  CheckCircle,\n  Clock,\n  DollarSign,\n  FileText,\n  Lightbulb,\n  Users,\n  Zap,\n  Palette,\n  Rocket,\n  Award,\n  BookOpen\n} from 'lucide-react';\nimport Link from 'next/link';\nimport { createCustomRequest } from '@/lib/firebaseServices';\n\nconst categories = [\n  'Dashboard',\n  'E-commerce',\n  'Landing Page',\n  'Portfolio',\n  'Corporate',\n  'Mobile App',\n  'Blog/CMS',\n  'Other'\n];\n\nconst features = [\n  {\n    icon: Palette,\n    title: 'Custom Design',\n    description: 'Tailored specifically to your brand and requirements'\n  },\n  {\n    icon: Rocket,\n    title: 'Fast Delivery',\n    description: 'Most projects completed within 5-10 business days'\n  },\n  {\n    icon: Users,\n    title: 'Expert Team',\n    description: 'Experienced designers and developers working on your project'\n  },\n  {\n    icon: BookOpen,\n    title: 'Full Documentation',\n    description: 'Complete setup guide and documentation included'\n  }\n];\n\nexport default function CustomRequestPage() {\n  const { user, userData } = useAuth();\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [error, setError] = useState('');\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    category: '',\n    budget: '',\n    deadline: '',\n    features: '',\n    inspiration: ''\n  });\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData(prev => ({\n      ...prev,\n      [e.target.name]: e.target.value\n    }));\n    setError('');\n  };\n\n  const handleCategoryChange = (value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      category: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError('');\n\n    // Validation\n    if (!formData.title || !formData.description || !formData.category) {\n      setError('Please fill in all required fields');\n      setIsLoading(false);\n      return;\n    }\n\n    try {\n      // Create custom request in Firestore\n      await createCustomRequest({\n        userId: user.uid,\n        userEmail: user.email!,\n        title: formData.title,\n        description: formData.description,\n        category: formData.category,\n        budget: formData.budget ? parseFloat(formData.budget.replace(/[^0-9.-]+/g, '')) : undefined,\n        deadline: formData.deadline ? new Date(formData.deadline) : undefined,\n        status: 'pending'\n      });\n\n      setIsSubmitted(true);\n    } catch (error: any) {\n      setError(error.message || 'Failed to submit request');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-20 text-center\">\n        <h1 className=\"text-2xl font-bold mb-4\">Sign in Required</h1>\n        <p className=\"text-gray-600 mb-6\">Please sign in to submit a custom design request.</p>\n        <Button asChild>\n          <Link href=\"/auth\">Sign In</Link>\n        </Button>\n      </div>\n    );\n  }\n\n  if (isSubmitted) {\n    return (\n      <div className=\"container mx-auto px-4 py-20\">\n        <Card className=\"max-w-2xl mx-auto text-center\">\n          <CardContent className=\"p-8\">\n            <div className=\"mb-6\">\n              <CheckCircle className=\"h-16 w-16 text-green-500 mx-auto mb-4\" />\n              <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Request Submitted!</h1>\n              <p className=\"text-gray-600\">\n                Thank you for your custom design request. Our team will review it and get back to you within 24 hours.\n              </p>\n            </div>\n            \n            <div className=\"space-y-4\">\n              <Button asChild className=\"w-full\">\n                <Link href=\"/dashboard\">Go to Dashboard</Link>\n              </Button>\n              <Button asChild variant=\"outline\" className=\"w-full\">\n                <Link href=\"/templates\">Browse Templates</Link>\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\">\n      {/* Header */}\n      <div className=\"text-center mb-8 sm:mb-12\">\n        <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">\n          Request Custom Design\n        </h1>\n        <p className=\"text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto\">\n          Can't find what you're looking for? Let our expert team create a custom template tailored to your specific needs.\n        </p>\n      </div>\n\n      <div className=\"grid lg:grid-cols-3 gap-6 lg:gap-8\">\n        {/* Form */}\n        <div className=\"lg:col-span-2\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Project Details</CardTitle>\n              <CardDescription>\n                Tell us about your project requirements and we'll create something amazing for you.\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"title\">Project Title *</Label>\n                  <Input\n                    id=\"title\"\n                    name=\"title\"\n                    placeholder=\"e.g., Modern SaaS Dashboard\"\n                    value={formData.title}\n                    onChange={handleInputChange}\n                    required\n                  />\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"category\">Category *</Label>\n                  <Select value={formData.category} onValueChange={handleCategoryChange}>\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"Select a category\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {categories.map((category) => (\n                        <SelectItem key={category} value={category}>\n                          {category}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"description\">Project Description *</Label>\n                  <Textarea\n                    id=\"description\"\n                    name=\"description\"\n                    placeholder=\"Describe your project in detail. What is the purpose? Who is the target audience? What features do you need?\"\n                    value={formData.description}\n                    onChange={handleInputChange}\n                    rows={4}\n                    required\n                  />\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"features\">Required Features</Label>\n                  <Textarea\n                    id=\"features\"\n                    name=\"features\"\n                    placeholder=\"List specific features you need (e.g., user authentication, payment integration, responsive design, etc.)\"\n                    value={formData.features}\n                    onChange={handleInputChange}\n                    rows={3}\n                  />\n                </div>\n\n                <div className=\"grid md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"budget\">Budget Range (USD)</Label>\n                    <Input\n                      id=\"budget\"\n                      name=\"budget\"\n                      placeholder=\"e.g., $500 - $1000\"\n                      value={formData.budget}\n                      onChange={handleInputChange}\n                    />\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"deadline\">Preferred Deadline</Label>\n                    <Input\n                      id=\"deadline\"\n                      name=\"deadline\"\n                      type=\"date\"\n                      value={formData.deadline}\n                      onChange={handleInputChange}\n                    />\n                  </div>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"inspiration\">Inspiration/References</Label>\n                  <Textarea\n                    id=\"inspiration\"\n                    name=\"inspiration\"\n                    placeholder=\"Share any websites, designs, or references that inspire you. Include URLs if possible.\"\n                    value={formData.inspiration}\n                    onChange={handleInputChange}\n                    rows={3}\n                  />\n                </div>\n\n                {error && (\n                  <Alert variant=\"destructive\">\n                    <AlertDescription>{error}</AlertDescription>\n                  </Alert>\n                )}\n\n                <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n                  {isLoading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n                  Submit Request\n                </Button>\n              </form>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Sidebar */}\n        <div className=\"space-y-6\">\n          {/* Features */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Why Choose Custom Design?</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              {features.map((feature, index) => {\n                const IconComponent = feature.icon;\n                return (\n                  <div key={index} className=\"flex items-start space-x-3\">\n                    <div className=\"p-2 bg-blue-100 rounded-lg\">\n                      <IconComponent className=\"h-5 w-5 text-blue-600\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-medium text-gray-900\">{feature.title}</h4>\n                      <p className=\"text-sm text-gray-600\">{feature.description}</p>\n                    </div>\n                  </div>\n                );\n              })}\n            </CardContent>\n          </Card>\n\n          {/* Process */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Our Process</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3\">\n                <Badge className=\"bg-blue-100 text-blue-800\">1</Badge>\n                <div>\n                  <p className=\"font-medium\">Submit Request</p>\n                  <p className=\"text-sm text-gray-600\">Fill out the form with your requirements</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-3\">\n                <Badge className=\"bg-blue-100 text-blue-800\">2</Badge>\n                <div>\n                  <p className=\"font-medium\">Review & Quote</p>\n                  <p className=\"text-sm text-gray-600\">We'll review and send you a detailed quote</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-3\">\n                <Badge className=\"bg-blue-100 text-blue-800\">3</Badge>\n                <div>\n                  <p className=\"font-medium\">Design & Develop</p>\n                  <p className=\"text-sm text-gray-600\">Our team creates your custom template</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-3\">\n                <Badge className=\"bg-blue-100 text-blue-800\">4</Badge>\n                <div>\n                  <p className=\"font-medium\">Delivery</p>\n                  <p className=\"text-sm text-gray-600\">Receive your completed template with documentation</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Contact */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Need Help?</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                Have questions about custom design? Our team is here to help.\n              </p>\n              <Button asChild variant=\"outline\" className=\"w-full\">\n                <Link href=\"/contact\">Contact Us</Link>\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;;;AA3BA;;;;;;;;;;;;;;AA6BA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,WAAW;IACf;QACE,MAAM,2MAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,iNAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;QACV,aAAa;IACf;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;YACjC,CAAC;QACD,SAAS;IACX;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,UAAU;YACZ,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,aAAa;QACb,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,QAAQ,EAAE;YAClE,SAAS;YACT,aAAa;YACb;QACF;QAEA,IAAI;YACF,qCAAqC;YACrC,MAAM,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD,EAAE;gBACxB,QAAQ,KAAK,GAAG;gBAChB,WAAW,KAAK,KAAK;gBACrB,OAAO,SAAS,KAAK;gBACrB,aAAa,SAAS,WAAW;gBACjC,UAAU,SAAS,QAAQ;gBAC3B,QAAQ,SAAS,MAAM,GAAG,WAAW,SAAS,MAAM,CAAC,OAAO,CAAC,cAAc,OAAO;gBAClF,UAAU,SAAS,QAAQ,GAAG,IAAI,KAAK,SAAS,QAAQ,IAAI;gBAC5D,QAAQ;YACV;YAEA,eAAe;QACjB,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAClC,6LAAC,qIAAA,CAAA,SAAM;oBAAC,OAAO;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAQ;;;;;;;;;;;;;;;;;IAI3B;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAK/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,WAAU;8CACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAa;;;;;;;;;;;8CAE1B,6LAAC,qIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,SAAQ;oCAAU,WAAU;8CAC1C,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOtC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgE;;;;;;kCAG9E,6LAAC;wBAAE,WAAU;kCAAkE;;;;;;;;;;;;0BAKjF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,QAAQ;;;;;;;;;;;;0DAIZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW;;;;;;kEAC1B,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO,SAAS,QAAQ;wDAAE,eAAe;;0EAC/C,6LAAC,qIAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,6LAAC,qIAAA,CAAA,gBAAa;0EACX,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,qIAAA,CAAA,aAAU;wEAAgB,OAAO;kFAC/B;uEADc;;;;;;;;;;;;;;;;;;;;;;0DAQzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAc;;;;;;kEAC7B,6LAAC,uIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,WAAW;wDAC3B,UAAU;wDACV,MAAM;wDACN,QAAQ;;;;;;;;;;;;0DAIZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW;;;;;;kEAC1B,6LAAC,uIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,QAAQ;wDACxB,UAAU;wDACV,MAAM;;;;;;;;;;;;0DAIV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAS;;;;;;0EACxB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,aAAY;gEACZ,OAAO,SAAS,MAAM;gEACtB,UAAU;;;;;;;;;;;;kEAId,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;0EAC1B,6LAAC,oIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,QAAQ;gEACxB,UAAU;;;;;;;;;;;;;;;;;;0DAKhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAc;;;;;;kEAC7B,6LAAC,uIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,WAAW;wDAC3B,UAAU;wDACV,MAAM;;;;;;;;;;;;4CAIT,uBACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DACb,cAAA,6LAAC,oIAAA,CAAA,mBAAgB;8DAAE;;;;;;;;;;;0DAIvB,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,WAAU;gDAAS,UAAU;;oDAChD,2BAAa,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS1E,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACpB,SAAS,GAAG,CAAC,CAAC,SAAS;4CACtB,MAAM,gBAAgB,QAAQ,IAAI;4CAClC,qBACE,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAc,WAAU;;;;;;;;;;;kEAE3B,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA6B,QAAQ,KAAK;;;;;;0EACxD,6LAAC;gEAAE,WAAU;0EAAyB,QAAQ,WAAW;;;;;;;;;;;;;+CANnD;;;;;wCAUd;;;;;;;;;;;;0CAKJ,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAA4B;;;;;;kEAC7C,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAA4B;;;;;;kEAC7C,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAA4B;;;;;;kEAC7C,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAA4B;;;;;;kEAC7C,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO7C,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAG1C,6LAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,SAAQ;gDAAU,WAAU;0DAC1C,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC;GAzTwB;;QACK,kIAAA,CAAA,UAAO;;;KADZ", "debugId": null}}]}
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[744],{704:(e,t,r)=>{r.d(t,{B8:()=>I,UC:()=>M,bL:()=>N,l9:()=>P});var n=r(2115),l=r(5185),o=r(6081),a=r(9196),i=r(8905),s=r(3655),u=r(4315),d=r(5845),c=r(1285),p=r(5155),f="Tabs",[v,h]=(0,o.A)(f,[a.RG]),m=(0,a.RG)(),[w,g]=v(f),y=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:l,defaultValue:o,orientation:a="horizontal",dir:i,activationMode:v="automatic",...h}=e,m=(0,u.jH)(i),[g,y]=(0,d.i)({prop:n,onChange:l,defaultProp:null!=o?o:"",caller:f});return(0,p.jsx)(w,{scope:r,baseId:(0,c.B)(),value:g,onValueChange:y,orientation:a,dir:m,activationMode:v,children:(0,p.jsx)(s.sG.div,{dir:m,"data-orientation":a,...h,ref:t})})});y.displayName=f;var x="TabsList",b=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...l}=e,o=g(x,r),i=m(r);return(0,p.jsx)(a.bL,{asChild:!0,...i,orientation:o.orientation,dir:o.dir,loop:n,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...l,ref:t})})});b.displayName=x;var S="TabsTrigger",C=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:o=!1,...i}=e,u=g(S,r),d=m(r),c=R(u.baseId,n),f=T(u.baseId,n),v=n===u.value;return(0,p.jsx)(a.q7,{asChild:!0,...d,focusable:!o,active:v,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":f,"data-state":v?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:c,...i,ref:t,onMouseDown:(0,l.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(n)}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(n)}),onFocus:(0,l.m)(e.onFocus,()=>{let e="manual"!==u.activationMode;v||o||!e||u.onValueChange(n)})})})});C.displayName=S;var j="TabsContent",k=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:l,forceMount:o,children:a,...u}=e,d=g(j,r),c=R(d.baseId,l),f=T(d.baseId,l),v=l===d.value,h=n.useRef(v);return n.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(i.C,{present:o||v,children:r=>{let{present:n}=r;return(0,p.jsx)(s.sG.div,{"data-state":v?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!n,id:f,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:n&&a})}})});function R(e,t){return"".concat(e,"-trigger-").concat(t)}function T(e,t){return"".concat(e,"-content-").concat(t)}k.displayName=j;var N=y,I=b,P=C,M=k},968:(e,t,r)=>{r.d(t,{b:()=>i});var n=r(2115),l=r(3655),o=r(5155),a=n.forwardRef((e,t)=>(0,o.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},1154:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2657:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5695:(e,t,r)=>{var n=r(8999);r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},6474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},8749:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8893:(e,t,r)=>{r.d(t,{UC:()=>eM,In:()=>eI,q7:()=>eE,VF:()=>eL,p4:()=>eA,ZL:()=>eP,bL:()=>eR,wn:()=>eB,PP:()=>eH,l9:()=>eT,WT:()=>eN,LM:()=>eD});var n=r(2115),l=r(7650);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(5185),i=r(7328),s=r(6101),u=r(6081),d=r(4315),c=r(9178),p=r(2293),f=r(7900),v=r(1285),h=r(8795),m=r(4378),w=r(3655),g=r(9708),y=r(9033),x=r(5845),b=r(2712),S=r(5155),C=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,S.jsx)(w.sG.span,{...e,ref:t,style:{...C,...e.style}})).displayName="VisuallyHidden";var j=r(8168),k=r(1114),R=[" ","Enter","ArrowUp","ArrowDown"],T=[" ","Enter"],N="Select",[I,P,M]=(0,i.N)(N),[D,E]=(0,u.A)(N,[M,h.Bk]),A=(0,h.Bk)(),[L,H]=D(N),[B,G]=D(N),_=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:u,dir:c,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,y=A(t),[b,C]=n.useState(null),[j,k]=n.useState(null),[R,T]=n.useState(!1),P=(0,d.jH)(c),[M,D]=(0,x.i)({prop:l,defaultProp:null!=o&&o,onChange:a,caller:N}),[E,H]=(0,x.i)({prop:i,defaultProp:s,onChange:u,caller:N}),G=n.useRef(null),_=!b||g||!!b.closest("form"),[V,F]=n.useState(new Set),K=Array.from(V).map(e=>e.props.value).join(";");return(0,S.jsx)(h.bL,{...y,children:(0,S.jsxs)(L,{required:w,scope:t,trigger:b,onTriggerChange:C,valueNode:j,onValueNodeChange:k,valueNodeHasChildren:R,onValueNodeHasChildrenChange:T,contentId:(0,v.B)(),value:E,onValueChange:H,open:M,onOpenChange:D,dir:P,triggerPointerDownPosRef:G,disabled:m,children:[(0,S.jsx)(I.Provider,{scope:t,children:(0,S.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{F(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{F(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),_?(0,S.jsxs)(eS,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:E,onChange:e=>H(e.target.value),disabled:m,form:g,children:[void 0===E?(0,S.jsx)("option",{value:""}):null,Array.from(V)]},K):null]})})};_.displayName=N;var V="SelectTrigger",F=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=A(r),u=H(V,r),d=u.disabled||l,c=(0,s.s)(t,u.onTriggerChange),p=P(r),f=n.useRef("touch"),[v,m,g]=ej(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=ek(t,e,r);void 0!==n&&u.onValueChange(n.value)}),y=e=>{d||(u.onOpenChange(!0),g()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,S.jsx)(h.Mz,{asChild:!0,...i,children:(0,S.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eC(u.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&y(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&R.includes(e.key)&&(y(),e.preventDefault())})})})});F.displayName=V;var K="SelectValue",O=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,u=H(K,r),{onValueNodeHasChildrenChange:d}=u,c=void 0!==o,p=(0,s.s)(t,u.onValueNodeChange);return(0,b.N)(()=>{d(c)},[d,c]),(0,S.jsx)(w.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eC(u.value)?(0,S.jsx)(S.Fragment,{children:a}):o})});O.displayName=K;var z=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,S.jsx)(w.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});z.displayName="SelectIcon";var W=e=>(0,S.jsx)(m.Z,{asChild:!0,...e});W.displayName="SelectPortal";var U="SelectContent",q=n.forwardRef((e,t)=>{let r=H(U,e.__scopeSelect),[o,a]=n.useState();return((0,b.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,S.jsx)(J,{...e,ref:t}):o?l.createPortal((0,S.jsx)(Z,{scope:e.__scopeSelect,children:(0,S.jsx)(I.Slot,{scope:e.__scopeSelect,children:(0,S.jsx)("div",{children:e.children})})}),o):null});q.displayName=U;var[Z,X]=D(U),Y=(0,g.TL)("SelectContent.RemoveScroll"),J=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:u,side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:C,...R}=e,T=H(U,r),[N,I]=n.useState(null),[M,D]=n.useState(null),E=(0,s.s)(t,e=>I(e)),[A,L]=n.useState(null),[B,G]=n.useState(null),_=P(r),[V,F]=n.useState(!1),K=n.useRef(!1);n.useEffect(()=>{if(N)return(0,j.Eq)(N)},[N]),(0,p.Oh)();let O=n.useCallback(e=>{let[t,...r]=_().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&M&&(M.scrollTop=0),r===n&&M&&(M.scrollTop=M.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[_,M]),z=n.useCallback(()=>O([A,N]),[O,A,N]);n.useEffect(()=>{V&&z()},[V,z]);let{onOpenChange:W,triggerPointerDownPosRef:q}=T;n.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!=(l=null==(r=q.current)?void 0:r.x)?l:0)),y:Math.abs(Math.round(t.pageY)-(null!=(o=null==(n=q.current)?void 0:n.y)?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||W(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,W,q]),n.useEffect(()=>{let e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[W]);let[X,J]=ej(e=>{let t=_().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=ek(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),ee=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==T.value&&T.value===t||n)&&(L(e),n&&(K.current=!0))},[T.value]),et=n.useCallback(()=>null==N?void 0:N.focus(),[N]),er=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==T.value&&T.value===t||n)&&G(e)},[T.value]),en="popper"===l?$:Q,el=en===$?{side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:C}:{};return(0,S.jsx)(Z,{scope:r,content:N,viewport:M,onViewportChange:D,itemRefCallback:ee,selectedItem:A,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:z,selectedItemText:B,position:l,isPositioned:V,searchRef:X,children:(0,S.jsx)(k.A,{as:Y,allowPinchZoom:!0,children:(0,S.jsx)(f.n,{asChild:!0,trapped:T.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null==(t=T.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,S.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>T.onOpenChange(!1),children:(0,S.jsx)(en,{role:"listbox",id:T.contentId,"data-state":T.open?"open":"closed",dir:T.dir,onContextMenu:e=>e.preventDefault(),...R,...el,onPlaced:()=>F(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:(0,a.m)(R.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||J(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=_().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>O(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=H(U,r),u=X(U,r),[d,c]=n.useState(null),[p,f]=n.useState(null),v=(0,s.s)(t,e=>f(e)),h=P(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:y,selectedItem:x,selectedItemText:C,focusSelectedItem:j}=u,k=n.useCallback(()=>{if(i.trigger&&i.valueNode&&d&&p&&y&&x&&C){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=C.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,u=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,u=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.right=c+"px"}let a=h(),s=window.innerHeight-20,u=y.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=f+v+u+parseInt(c.paddingBottom,10)+w,b=Math.min(5*x.offsetHeight,g),S=window.getComputedStyle(y),j=parseInt(S.paddingTop,10),k=parseInt(S.paddingBottom,10),R=e.top+e.height/2-10,T=x.offsetHeight/2,N=f+v+(x.offsetTop+T);if(N<=R){let e=a.length>0&&x===a[a.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-R,T+(e?k:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+w);d.style.height=N+t+"px"}else{let e=a.length>0&&x===a[0].ref.current;d.style.top="0px";let t=Math.max(R,f+y.offsetTop+(e?j:0)+T);d.style.height=t+(g-N)+"px",y.scrollTop=N-R+y.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=b+"px",d.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,d,p,y,x,C,i.dir,l]);(0,b.N)(()=>k(),[k]);let[R,T]=n.useState();(0,b.N)(()=>{p&&T(window.getComputedStyle(p).zIndex)},[p]);let N=n.useCallback(e=>{e&&!0===g.current&&(k(),null==j||j(),g.current=!1)},[k,j]);return(0,S.jsx)(ee,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:m,onScrollButtonChange:N,children:(0,S.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,S.jsx)(w.sG.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=A(r);return(0,S.jsx)(h.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});$.displayName="SelectPopperPosition";var[ee,et]=D(U,{}),er="SelectViewport",en=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=X(er,r),u=et(er,r),d=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,S.jsx)(I.Slot,{scope:r,children:(0,S.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});en.displayName=er;var el="SelectGroup",[eo,ea]=D(el);n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.B)();return(0,S.jsx)(eo,{scope:r,id:l,children:(0,S.jsx)(w.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})}).displayName=el;var ei="SelectLabel";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ea(ei,r);return(0,S.jsx)(w.sG.div,{id:l.id,...n,ref:t})}).displayName=ei;var es="SelectItem",[eu,ed]=D(es),ec=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...u}=e,d=H(es,r),c=X(es,r),p=d.value===l,[f,h]=n.useState(null!=i?i:""),[m,g]=n.useState(!1),y=(0,s.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,l,o)}),x=(0,v.B)(),b=n.useRef("touch"),C=()=>{o||(d.onValueChange(l),d.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,S.jsx)(eu,{scope:r,value:l,disabled:o,textId:x,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,S.jsx)(I.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,S.jsx)(w.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...u,ref:y,onFocus:(0,a.m)(u.onFocus,()=>g(!0)),onBlur:(0,a.m)(u.onBlur,()=>g(!1)),onClick:(0,a.m)(u.onClick,()=>{"mouse"!==b.current&&C()}),onPointerUp:(0,a.m)(u.onPointerUp,()=>{"mouse"===b.current&&C()}),onPointerDown:(0,a.m)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(u.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,a.m)(u.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(T.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});ec.displayName=es;var ep="SelectItemText",ef=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,u=H(ep,r),d=X(ep,r),c=ed(ep,r),p=G(ep,r),[f,v]=n.useState(null),h=(0,s.s)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null==(t=d.itemTextRefCallback)?void 0:t.call(d,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,g=n.useMemo(()=>(0,S.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=p;return(0,b.N)(()=>(y(g),()=>x(g)),[y,x,g]),(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(w.sG.span,{id:c.textId,...i,ref:h}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?l.createPortal(i.children,u.valueNode):null]})});ef.displayName=ep;var ev="SelectItemIndicator",eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ed(ev,r).isSelected?(0,S.jsx)(w.sG.span,{"aria-hidden":!0,...n,ref:t}):null});eh.displayName=ev;var em="SelectScrollUpButton",ew=n.forwardRef((e,t)=>{let r=X(em,e.__scopeSelect),l=et(em,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,S.jsx)(ex,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=em;var eg="SelectScrollDownButton",ey=n.forwardRef((e,t)=>{let r=X(eg,e.__scopeSelect),l=et(eg,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,S.jsx)(ex,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ey.displayName=eg;var ex=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=X("SelectScrollButton",r),s=n.useRef(null),u=P(r),d=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>d(),[d]),(0,b.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,S.jsx)(w.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{d()})})});n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,S.jsx)(w.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var eb="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=A(r),o=H(eb,r),a=X(eb,r);return o.open&&"popper"===a.position?(0,S.jsx)(h.i3,{...l,...n,ref:t}):null}).displayName=eb;var eS=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,...o}=e,a=n.useRef(null),i=(0,s.s)(t,a),u=function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(l);return n.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==l&&t){let r=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(r)}},[u,l]),(0,S.jsx)(w.sG.select,{...o,style:{...C,...o.style},ref:i,defaultValue:l})});function eC(e){return""===e||void 0===e}function ej(e){let t=(0,y.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function ek(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eS.displayName="SelectBubbleInput";var eR=_,eT=F,eN=O,eI=z,eP=W,eM=q,eD=en,eE=ec,eA=ef,eL=eh,eH=ew,eB=ey}}]);
[{"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/page.tsx": "1", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/auth/page.tsx": "2", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/dashboard/page.tsx": "3", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/layout.tsx": "4", "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/page.tsx": "5", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/CategoriesSection.tsx": "6", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/FeaturedTemplates.tsx": "7", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/HeroSection.tsx": "8", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/Navbar.tsx": "9", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/StatsSection.tsx": "10", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/alert.tsx": "11", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/avatar.tsx": "12", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx": "13", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/button.tsx": "14", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx": "15", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/dialog.tsx": "16", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/dropdown-menu.tsx": "17", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/form.tsx": "18", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/input.tsx": "19", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/label.tsx": "20", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/navigation-menu.tsx": "21", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/select.tsx": "22", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/sheet.tsx": "23", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/table.tsx": "24", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/tabs.tsx": "25", "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/textarea.tsx": "26", "/home/<USER>/Documents/augment-projects/kaleidonex/src/contexts/AuthContext.tsx": "27", "/home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebase.ts": "28", "/home/<USER>/Documents/augment-projects/kaleidonex/src/lib/utils.ts": "29", "/home/<USER>/Documents/augment-projects/kaleidonex/src/types/index.ts": "30"}, {"size": 9644, "mtime": 1749531556407, "results": "31", "hashOfConfig": "32"}, {"size": 10436, "mtime": 1749531484180, "results": "33", "hashOfConfig": "32"}, {"size": 8567, "mtime": 1749531523067, "results": "34", "hashOfConfig": "32"}, {"size": 977, "mtime": 1749531265580, "results": "35", "hashOfConfig": "32"}, {"size": 450, "mtime": 1749531355825, "results": "36", "hashOfConfig": "32"}, {"size": 3872, "mtime": 1749531426050, "results": "37", "hashOfConfig": "32"}, {"size": 5986, "mtime": 1749531405569, "results": "38", "hashOfConfig": "32"}, {"size": 4213, "mtime": 1749531380904, "results": "39", "hashOfConfig": "32"}, {"size": 5675, "mtime": 1749531291189, "results": "40", "hashOfConfig": "32"}, {"size": 5659, "mtime": 1749531451948, "results": "41", "hashOfConfig": "32"}, {"size": 1614, "mtime": 1749531721138, "results": "42", "hashOfConfig": "32"}, {"size": 1097, "mtime": 1749531171148, "results": "43", "hashOfConfig": "32"}, {"size": 1631, "mtime": 1749531171120, "results": "44", "hashOfConfig": "32"}, {"size": 2123, "mtime": 1749531170878, "results": "45", "hashOfConfig": "32"}, {"size": 1989, "mtime": 1749531170936, "results": "46", "hashOfConfig": "32"}, {"size": 3982, "mtime": 1749531171060, "results": "47", "hashOfConfig": "32"}, {"size": 8284, "mtime": 1749531171212, "results": "48", "hashOfConfig": "32"}, {"size": 3759, "mtime": 1749531171026, "results": "49", "hashOfConfig": "32"}, {"size": 967, "mtime": 1749531170946, "results": "50", "hashOfConfig": "32"}, {"size": 611, "mtime": 1749531170959, "results": "51", "hashOfConfig": "32"}, {"size": 6664, "mtime": 1749531171239, "results": "52", "hashOfConfig": "32"}, {"size": 6253, "mtime": 1749531171312, "results": "53", "hashOfConfig": "32"}, {"size": 4090, "mtime": 1749531171267, "results": "54", "hashOfConfig": "32"}, {"size": 2448, "mtime": 1749531171281, "results": "55", "hashOfConfig": "32"}, {"size": 1969, "mtime": 1749531171290, "results": "56", "hashOfConfig": "32"}, {"size": 759, "mtime": 1749531171318, "results": "57", "hashOfConfig": "32"}, {"size": 2608, "mtime": 1749531204577, "results": "58", "hashOfConfig": "32"}, {"size": 701, "mtime": 1749531192157, "results": "59", "hashOfConfig": "32"}, {"size": 166, "mtime": 1749531044432, "results": "60", "hashOfConfig": "32"}, {"size": 1341, "mtime": 1749531216136, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "9re6db", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/page.tsx", ["152"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/auth/page.tsx", ["153", "154"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/dashboard/page.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/layout.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/app/page.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/CategoriesSection.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/FeaturedTemplates.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/HeroSection.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/Navbar.tsx", ["155"], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/StatsSection.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/alert.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/avatar.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/button.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/dialog.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/dropdown-menu.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/form.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/input.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/label.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/navigation-menu.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/select.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/sheet.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/table.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/tabs.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/textarea.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/contexts/AuthContext.tsx", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebase.ts", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/lib/utils.ts", [], [], "/home/<USER>/Documents/augment-projects/kaleidonex/src/types/index.ts", [], [], {"ruleId": "156", "severity": 2, "message": "157", "line": 14, "column": 3, "nodeType": null, "messageId": "158", "endLine": 14, "endColumn": 6}, {"ruleId": "159", "severity": 2, "message": "160", "line": 61, "column": 21, "nodeType": "161", "messageId": "162", "endLine": 61, "endColumn": 24, "suggestions": "163"}, {"ruleId": "159", "severity": 2, "message": "160", "line": 88, "column": 21, "nodeType": "161", "messageId": "162", "endLine": 88, "endColumn": 24, "suggestions": "164"}, {"ruleId": "156", "severity": 2, "message": "165", "line": 16, "column": 53, "nodeType": null, "messageId": "158", "endLine": 16, "endColumn": 57}, "@typescript-eslint/no-unused-vars", "'Eye' is defined but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["166", "167"], ["168", "169"], "'Plus' is defined but never used.", {"messageId": "170", "fix": "171", "desc": "172"}, {"messageId": "173", "fix": "174", "desc": "175"}, {"messageId": "170", "fix": "176", "desc": "172"}, {"messageId": "173", "fix": "177", "desc": "175"}, "suggestUnknown", {"range": "178", "text": "179"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "180", "text": "181"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "182", "text": "179"}, {"range": "183", "text": "181"}, [1932, 1935], "unknown", [1932, 1935], "never", [2597, 2600], [2597, 2600]]
exports.id=486,exports.ids=[486],exports.modules={4482:(e,t,s)=>{"use strict";s.d(t,{Navbar:()=>S});var r=s(60687),a=s(43210),n=s(85814),o=s.n(n),i=s(7613),d=s(29523),l=s(26312),c=s(4780);function u({...e}){return(0,r.jsx)(l.bL,{"data-slot":"dropdown-menu",...e})}function m({...e}){return(0,r.jsx)(l.l9,{"data-slot":"dropdown-menu-trigger",...e})}function h({className:e,sideOffset:t=4,...s}){return(0,r.jsx)(l.ZL,{children:(0,r.jsx)(l.<PERSON>,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...s})})}function x({className:e,inset:t,variant:s="default",...a}){return(0,r.jsx)(l.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":s,className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...a})}function v({className:e,...t}){return(0,r.jsx)(l.wv,{"data-slot":"dropdown-menu-separator",className:(0,c.cn)("bg-border -mx-1 my-1 h-px",e),...t})}var f=s(11096);function p({className:e,...t}){return(0,r.jsx)(f.bL,{"data-slot":"avatar",className:(0,c.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function b({className:e,...t}){return(0,r.jsx)(f.H4,{"data-slot":"avatar-fallback",className:(0,c.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}var g=s(26134),j=s(11860);function N({...e}){return(0,r.jsx)(g.bL,{"data-slot":"sheet",...e})}function w({...e}){return(0,r.jsx)(g.l9,{"data-slot":"sheet-trigger",...e})}function y({...e}){return(0,r.jsx)(g.ZL,{"data-slot":"sheet-portal",...e})}function C({className:e,...t}){return(0,r.jsx)(g.hJ,{"data-slot":"sheet-overlay",className:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function P({className:e,children:t,side:s="right",...a}){return(0,r.jsxs)(y,{children:[(0,r.jsx)(C,{}),(0,r.jsxs)(g.UC,{"data-slot":"sheet-content",className:(0,c.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===s&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===s&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===s&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===s&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...a,children:[t,(0,r.jsxs)(g.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(j.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}var A=s(84027),_=s(58869),k=s(71057),E=s(40083),I=s(12941);let S=()=>{let{user:e,userData:t,logout:s}=(0,i.A)(),[n,l]=(0,a.useState)(!1),c=async()=>{try{await s()}catch(e){console.error("Error logging out:",e)}},f=()=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o(),{href:"/",className:"text-sm font-medium hover:text-primary transition-colors",children:"Home"}),(0,r.jsx)(o(),{href:"/templates",className:"text-sm font-medium hover:text-primary transition-colors",children:"Templates"}),(0,r.jsx)(o(),{href:"/categories",className:"text-sm font-medium hover:text-primary transition-colors",children:"Categories"}),(0,r.jsx)(o(),{href:"/custom-request",className:"text-sm font-medium hover:text-primary transition-colors",children:"Custom Request"}),(0,r.jsx)(o(),{href:"/contact",className:"text-sm font-medium hover:text-primary transition-colors",children:"Contact"})]});return(0,r.jsx)("nav",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsxs)(o(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"h-8 w-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"K"})}),(0,r.jsx)("span",{className:"font-bold text-xl",children:"KaleidoneX"})]}),(0,r.jsx)("div",{className:"hidden md:flex items-center space-x-6",children:(0,r.jsx)(f,{})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[e?(0,r.jsxs)(r.Fragment,{children:[t?.role==="admin"&&(0,r.jsx)(d.$,{asChild:!0,variant:"outline",size:"sm",children:(0,r.jsxs)(o(),{href:"/admin",children:[(0,r.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Admin"]})}),(0,r.jsxs)(u,{children:[(0,r.jsx)(m,{asChild:!0,children:(0,r.jsx)(d.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,r.jsx)(p,{className:"h-8 w-8",children:(0,r.jsx)(b,{children:t?.displayName?.[0]||e.email?.[0]?.toUpperCase()})})})}),(0,r.jsxs)(h,{className:"w-56",align:"end",forceMount:!0,children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsxs)(o(),{href:"/dashboard",className:"flex items-center",children:[(0,r.jsx)(_.A,{className:"mr-2 h-4 w-4"}),"Dashboard"]})}),(0,r.jsx)(x,{asChild:!0,children:(0,r.jsxs)(o(),{href:"/orders",className:"flex items-center",children:[(0,r.jsx)(k.A,{className:"mr-2 h-4 w-4"}),"My Orders"]})}),(0,r.jsx)(v,{}),(0,r.jsxs)(x,{onClick:c,children:[(0,r.jsx)(E.A,{className:"mr-2 h-4 w-4"}),"Log out"]})]})]})]}):(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-2",children:[(0,r.jsx)(d.$,{asChild:!0,variant:"ghost",children:(0,r.jsx)(o(),{href:"/auth",children:"Sign In"})}),(0,r.jsx)(d.$,{asChild:!0,children:(0,r.jsx)(o(),{href:"/auth?mode=signup",children:"Get Started"})})]}),(0,r.jsxs)(N,{open:n,onOpenChange:l,children:[(0,r.jsx)(w,{asChild:!0,className:"md:hidden",children:(0,r.jsx)(d.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(I.A,{className:"h-5 w-5"})})}),(0,r.jsx)(P,{side:"right",className:"w-[300px] sm:w-[400px]",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-4 mt-6",children:[(0,r.jsx)(f,{}),!e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.$,{asChild:!0,variant:"ghost",className:"justify-start",children:(0,r.jsx)(o(),{href:"/auth",children:"Sign In"})}),(0,r.jsx)(d.$,{asChild:!0,className:"justify-start",children:(0,r.jsx)(o(),{href:"/auth?mode=signup",children:"Get Started"})})]})]})})]})]})]})})})}},4780:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(49384),a=s(82348);function n(...e){return(0,a.QP)((0,r.$)(e))}},7613:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>x,A:()=>h});var r=s(60687),a=s(43210),n=s(50227),o=s(75535),i=s(67989);let d={apiKey:process.env.NEXT_PUBLIC_FIREBASE_API_KEY,authDomain:process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,projectId:process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,storageBucket:process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,messagingSenderId:process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,appId:process.env.NEXT_PUBLIC_FIREBASE_APP_ID},l=(0,i.Wp)(d),c=(0,n.xI)(l),u=(0,o.aU)(l),m=(0,a.createContext)(void 0),h=()=>{let e=(0,a.useContext)(m);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},x=({children:e})=>{let[t,s]=(0,a.useState)(null),[i,d]=(0,a.useState)(null),[l,h]=(0,a.useState)(!0);(0,a.useEffect)(()=>{let e=(0,n.hg)(c,async e=>{if(e){s(e);let t=await (0,o.x7)((0,o.H9)(u,"users",e.uid));t.exists()&&d(t.data())}else s(null),d(null);h(!1)});return()=>e()},[]);let x=async(e,t)=>{await (0,n.x9)(c,e,t)},v=async(e,t,s="user")=>{let{user:r}=await (0,n.eJ)(c,e,t),a={uid:r.uid,email:r.email,role:s,displayName:r.displayName||"",createdAt:new Date};await (0,o.BN)((0,o.H9)(u,"users",r.uid),a)},f=async()=>{await (0,n.CI)(c)};return(0,r.jsx)(m.Provider,{value:{user:t,userData:i,loading:l,signIn:x,signUp:v,logout:f},children:e})}},16868:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},27139:(e,t,s)=>{Promise.resolve().then(s.bind(s,4482)),Promise.resolve().then(s.bind(s,7613))},29131:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>a});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/contexts/AuthContext.tsx","useAuth");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/contexts/AuthContext.tsx","AuthProvider")},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var r=s(60687);s(43210);var a=s(8730),n=s(24224),o=s(4780);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:s,asChild:n=!1,...d}){let l=n?a.DX:"button";return(0,r.jsx)(l,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:s,className:e})),...d})}},30004:(e,t,s)=>{"use strict";s.d(t,{Navbar:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/components/Navbar.tsx","Navbar")},51115:(e,t,s)=>{Promise.resolve().then(s.bind(s,30004)),Promise.resolve().then(s.bind(s,29131))},61135:()=>{},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,metadata:()=>c});var r=s(37413),a=s(2202),n=s.n(a),o=s(64988),i=s.n(o);s(61135);var d=s(29131),l=s(30004);let c={title:"KaleidoneX - Premium Template Marketplace",description:"Discover and purchase premium templates for your projects"};function u({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${i().variable} antialiased`,children:(0,r.jsxs)(d.AuthProvider,{children:[(0,r.jsx)(l.Navbar,{}),(0,r.jsx)("main",{className:"min-h-screen",children:e})]})})})}},98308:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))}};
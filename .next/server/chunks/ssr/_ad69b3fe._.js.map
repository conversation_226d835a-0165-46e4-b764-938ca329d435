{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/app/templates/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Input } from \"@/components/ui/input\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Search, Eye, ShoppingCart, IndianRupee, Star, Heart, Download, ExternalLink, Zap, Award, Filter } from \"lucide-react\"\nimport { collection, getDocs, query, orderBy } from 'firebase/firestore';\nimport { db } from '@/lib/firebase';\nimport { Template } from '@/types';\nimport { toast } from \"sonner\"\nimport { useRouter, usePathname } from \"next/navigation\"\n\n// Mock data for templates\nconst allTemplates = [\n  {\n    id: '1',\n    title: 'Creative Portfolio',\n    description: 'Showcase your creative work with this stunning portfolio template',\n    category: 'Portfolio',\n    price: 1999,\n    originalPrice: 2999,\n    imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',\n    rating: 4.9,\n    downloads: 1234,\n    featured: true,\n    tags: ['Responsive', 'Modern', 'Fast'],\n    discount: 33,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n    createdBy: 'admin'\n  },\n  {\n    id: '2',\n    title: 'E-commerce Store',\n    description: 'Complete e-commerce solution with shopping cart and payment integration',\n    category: 'E-commerce',\n    price: 4999,\n    originalPrice: 7499,\n    imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop',\n    rating: 4.9,\n    downloads: 1234,\n    featured: true,\n    tags: ['Responsive', 'Modern', 'Fast'],\n    discount: 33,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n    createdBy: 'admin'\n  },\n  {\n    id: '3',\n    title: 'Education Platform',\n    description: 'Complete education platform template with course management',\n    category: 'Education',\n    price: 3499,\n    originalPrice: 5249,\n    imageUrl: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop',\n    rating: 4.9,\n    downloads: 1234,\n    featured: true,\n    tags: ['Responsive', 'Modern', 'Fast'],\n    discount: 33,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n    createdBy: 'admin'\n  },\n  {\n    id: '4',\n    title: 'Technology Dashboard',\n    description: 'Modern dashboard template for technology companies',\n    category: 'Technology',\n    price: 2999,\n    originalPrice: 4499,\n    imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop',\n    rating: 4.8,\n    downloads: 987,\n    featured: false,\n    tags: ['Responsive', 'Modern', 'Fast'],\n    discount: 33,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n    createdBy: 'admin'\n  },\n  {\n    id: '5',\n    title: 'Business Corporate',\n    description: 'Professional corporate website template',\n    category: 'Business',\n    price: 3999,\n    originalPrice: 5999,\n    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',\n    rating: 4.7,\n    downloads: 756,\n    featured: false,\n    tags: ['Responsive', 'Modern', 'Fast'],\n    discount: 33,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n    createdBy: 'admin'\n  },\n  {\n    id: '6',\n    title: 'Restaurant Website',\n    description: 'Beautiful restaurant website with menu and reservation system',\n    category: 'Restaurant',\n    price: 2499,\n    originalPrice: 3749,\n    imageUrl: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&h=300&fit=crop',\n    rating: 4.6,\n    downloads: 543,\n    featured: false,\n    tags: ['Responsive', 'Modern', 'Fast'],\n    discount: 33,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n    createdBy: 'admin'\n  }\n];\n\nconst categories = [\n  { name: 'All', count: 7 },\n  { name: 'Technology', count: 1 },\n  { name: 'Business', count: 2 },\n  { name: 'Education', count: 1 },\n  { name: 'Portfolio', count: 1 },\n  { name: 'E-commerce', count: 1 },\n  { name: 'Restaurant', count: 1 }\n];\nexport default function TemplatesPage() {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('All');\n  const [sortBy, setSortBy] = useState('Newest');\n  const [templates, setTemplates] = useState<Template[]>([]);\n  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  // Fetch templates from Firestore\n  useEffect(() => {\n    const fetchTemplates = async () => {\n      try {\n        const templatesQuery = query(collection(db, 'templates'), orderBy('createdAt', 'desc'));\n        const querySnapshot = await getDocs(templatesQuery);\n        const templatesData = querySnapshot.docs.map(doc => ({\n          id: doc.id,\n          ...doc.data(),\n          createdAt: doc.data().createdAt?.toDate() || new Date(),\n          updatedAt: doc.data().updatedAt?.toDate() || new Date(),\n        })) as Template[];\n\n        setTemplates(templatesData);\n        setFilteredTemplates(templatesData);\n      } catch (error) {\n        console.error('Error fetching templates:', error);\n        // Fallback to mock data if Firestore fails\n        setTemplates(allTemplates);\n        setFilteredTemplates(allTemplates);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTemplates();\n  }, []);\n\n  // Filter and sort templates\n  useEffect(() => {\n    let filtered = templates;\n\n    // Filter by search query\n    if (searchQuery) {\n      filtered = filtered.filter(template =>\n        template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))\n      );\n    }\n\n    // Filter by category\n    if (selectedCategory !== 'All') {\n      filtered = filtered.filter(template => template.category === selectedCategory);\n    }\n\n    // Sort templates\n    switch (sortBy) {\n      case 'Popular':\n        filtered.sort((a, b) => (b.downloads || 0) - (a.downloads || 0));\n        break;\n      case 'Price: Low to High':\n        filtered.sort((a, b) => a.price - b.price);\n        break;\n      case 'Price: High to Low':\n        filtered.sort((a, b) => b.price - a.price);\n        break;\n      case 'Rating':\n        filtered.sort((a, b) => (b.rating || 0) - (a.rating || 0));\n        break;\n      default:\n        // Newest (featured first, then by id)\n        filtered.sort((a, b) => {\n          if (a.featured && !b.featured) return -1;\n          if (!a.featured && b.featured) return 1;\n          return parseInt(b.id) - parseInt(a.id);\n        });\n    }\n\n    setFilteredTemplates(filtered);\n  }, [searchQuery, selectedCategory, sortBy]);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\">\n        {/* Categories Header */}\n        <div className=\"mb-6 sm:mb-8\">\n          <h2 className=\"text-lg sm:text-xl font-medium text-gray-900 mb-4\">Categories</h2>\n\n          {/* Category Pills */}\n          <div className=\"flex flex-wrap gap-2 sm:gap-3 mb-4 sm:mb-6\">\n            {categories.map((category) => (\n              <button\n                key={category.name}\n                onClick={() => setSelectedCategory(category.name)}\n                className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm font-medium transition-colors ${\n                  selectedCategory === category.name\n                    ? 'bg-gray-900 text-white'\n                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'\n                }`}\n              >\n                {category.name}\n                {category.name !== 'All' && (\n                  <span className=\"ml-1 sm:ml-2 text-xs opacity-70\">{category.count}</span>\n                )}\n              </button>\n            ))}\n          </div>\n\n          {/* Results count */}\n          <div className=\"text-xs sm:text-sm text-gray-600\">\n            Showing {filteredTemplates.length} of {templates.length} templates\n          </div>\n        </div>\n\n        {/* Loading State */}\n        {loading ? (\n          <div className=\"flex items-center justify-center py-20\">\n            <Loader2 className=\"h-8 w-8 animate-spin text-blue-600\" />\n            <span className=\"ml-2 text-gray-600\">Loading templates...</span>\n          </div>\n        ) : (\n          <>\n            {/* Templates Grid */}\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6\">\n              {filteredTemplates.map((template) => (\n                <div key={template.id} className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\">\n                  {/* Template Image */}\n                  <div className=\"relative group\">\n                    <div className=\"aspect-[4/3] bg-gray-100 flex items-center justify-center relative overflow-hidden\">\n                      {template.imageUrl.includes('unsplash') ? (\n                        <img\n                          src={template.imageUrl}\n                          alt={template.title}\n                          className=\"w-full h-full object-cover\"\n                        />\n                      ) : (\n                        <div className=\"flex flex-col items-center justify-center text-gray-400\">\n                          <div className=\"w-12 h-12 mb-2 flex items-center justify-center\">\n                            <FileText className=\"w-8 h-8\" />\n                          </div>\n                          <span className=\"text-sm font-medium\">Template Preview</span>\n                        </div>\n                      )}\n                    </div>\n\n                    {/* Category Badge */}\n                    <div className=\"absolute top-2 sm:top-3 left-2 sm:left-3\">\n                      <span className=\"px-2 py-1 bg-white text-gray-700 text-xs font-medium rounded\">\n                        {template.category}\n                      </span>\n                    </div>\n\n                    {/* Favorite Icon */}\n                    <button className=\"absolute top-2 sm:top-3 right-2 sm:right-3 p-1 sm:p-1.5 bg-white rounded-full shadow-sm hover:bg-gray-50\">\n                      <Heart className=\"w-3 h-3 sm:w-4 sm:h-4 text-gray-400\" />\n                    </button>\n                  </div>\n\n                  {/* Template Info */}\n                  <div className=\"p-3 sm:p-4\">\n                    <h3 className=\"font-semibold text-gray-900 mb-2 text-sm sm:text-base\">{template.title}</h3>\n                    <p className=\"text-xs sm:text-sm text-gray-600 mb-3 line-clamp-2\">{template.description}</p>\n\n                    {/* Tags */}\n                    <div className=\"flex flex-wrap gap-1 mb-3 sm:mb-4\">\n                      {template.tags.map((tag) => (\n                        <span key={tag} className=\"px-1.5 sm:px-2 py-0.5 sm:py-1 bg-gray-100 text-gray-600 text-xs rounded\">\n                          {tag}\n                        </span>\n                      ))}\n                    </div>\n\n                    {/* Price and Rating */}\n                    <div className=\"flex items-center justify-between mb-3 sm:mb-4\">\n                      <div className=\"flex items-center space-x-1 sm:space-x-2\">\n                        <span className=\"text-base sm:text-lg font-bold text-green-600\">₹{template.price}</span>\n                        {template.originalPrice && (\n                          <>\n                            <span className=\"text-xs sm:text-sm text-gray-400 line-through\">₹{template.originalPrice}</span>\n                            <span className=\"text-xs text-green-600 font-medium\">{template.discount}% OFF</span>\n                          </>\n                        )}\n                      </div>\n                      <div className=\"flex items-center text-xs text-gray-500\">\n                        <Star className=\"w-3 h-3 fill-yellow-400 text-yellow-400 mr-1\" />\n                        {template.rating}\n                      </div>\n                    </div>\n\n                    {/* Action Buttons */}\n                    <div className=\"flex space-x-2\">\n                      <Button variant=\"outline\" size=\"sm\" className=\"flex-1 text-xs sm:text-sm\">\n                        <Eye className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1\" />\n                        <span className=\"hidden sm:inline\">Preview</span>\n                        <span className=\"sm:hidden\">View</span>\n                      </Button>\n                      <Button size=\"sm\" className=\"flex-1 bg-blue-600 hover:bg-blue-700 text-xs sm:text-sm\">\n                        <ShoppingCart className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1\" />\n                        <span className=\"hidden sm:inline\">Buy Now</span>\n                        <span className=\"sm:hidden\">Buy</span>\n                      </Button>\n                    </div>\n\n                    {/* Downloads */}\n                    <div className=\"flex items-center justify-between mt-2 sm:mt-3 pt-2 sm:pt-3 border-t border-gray-100\">\n                      <div className=\"flex items-center text-xs text-gray-500\">\n                        <Download className=\"w-3 h-3 mr-1\" />\n                        <span className=\"hidden sm:inline\">{template.downloads?.toLocaleString()} downloads</span>\n                        <span className=\"sm:hidden\">{(template.downloads || 0) > 1000 ? `${Math.floor((template.downloads || 0) / 1000)}k` : template.downloads}</span>\n                      </div>\n                      <span className=\"text-xs text-gray-500 hidden sm:inline\">Updated recently</span>\n                      <span className=\"text-xs text-gray-500 sm:hidden\">Recent</span>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* No results */}\n            {filteredTemplates.length === 0 && (\n              <div className=\"text-center py-16\">\n                <div className=\"text-gray-400 mb-4\">\n                  <Filter className=\"h-16 w-16 mx-auto\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No templates found</h3>\n                <p className=\"text-gray-600 mb-6\">\n                  Try adjusting your search criteria or browse all templates\n                </p>\n                <Button onClick={() => {\n                  setSearchQuery('');\n                  setSelectedCategory('All');\n                  setSortBy('Newest');\n                }}>\n                  Clear Filters\n                </Button>\n              </div>\n            )}\n          </>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAVA;;;;;;;AAeA,0BAA0B;AAC1B,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,eAAe;QACf,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAc;YAAU;SAAO;QACtC,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;QACf,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,eAAe;QACf,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAc;YAAU;SAAO;QACtC,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;QACf,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,eAAe;QACf,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAc;YAAU;SAAO;QACtC,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;QACf,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,eAAe;QACf,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAc;YAAU;SAAO;QACtC,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;QACf,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,eAAe;QACf,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAc;YAAU;SAAO;QACtC,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;QACf,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,eAAe;QACf,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAc;YAAU;SAAO;QACtC,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;QACf,WAAW;IACb;CACD;AAED,MAAM,aAAa;IACjB;QAAE,MAAM;QAAO,OAAO;IAAE;IACxB;QAAE,MAAM;QAAc,OAAO;IAAE;IAC/B;QAAE,MAAM;QAAY,OAAO;IAAE;IAC7B;QAAE,MAAM;QAAa,OAAO;IAAE;IAC9B;QAAE,MAAM;QAAa,OAAO;IAAE;IAC9B;QAAE,MAAM;QAAc,OAAO;IAAE;IAC/B;QAAE,MAAM;QAAc,OAAO;IAAE;CAChC;AACc,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,MAAM,iBAAiB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,cAAc,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;gBAC/E,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;gBACpC,MAAM,gBAAgB,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;wBACnD,IAAI,IAAI,EAAE;wBACV,GAAG,IAAI,IAAI,EAAE;wBACb,WAAW,IAAI,IAAI,GAAG,SAAS,EAAE,YAAY,IAAI;wBACjD,WAAW,IAAI,IAAI,GAAG,SAAS,EAAE,YAAY,IAAI;oBACnD,CAAC;gBAED,aAAa;gBACb,qBAAqB;YACvB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,2CAA2C;gBAC3C,aAAa;gBACb,qBAAqB;YACvB,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,yBAAyB;QACzB,IAAI,aAAa;YACf,WAAW,SAAS,MAAM,CAAC,CAAA,WACzB,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC7D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACnE,SAAS,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEhF;QAEA,qBAAqB;QACrB,IAAI,qBAAqB,OAAO;YAC9B,WAAW,SAAS,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;QAC/D;QAEA,iBAAiB;QACjB,OAAQ;YACN,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,IAAI,CAAC;gBAC9D;YACF,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;gBACzC;YACF,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;gBACzC;YACF,KAAK;gBACH,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC;gBACxD;YACF;gBACE,sCAAsC;gBACtC,SAAS,IAAI,CAAC,CAAC,GAAG;oBAChB,IAAI,EAAE,QAAQ,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC;oBACvC,IAAI,CAAC,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE,OAAO;oBACtC,OAAO,SAAS,EAAE,EAAE,IAAI,SAAS,EAAE,EAAE;gBACvC;QACJ;QAEA,qBAAqB;IACvB,GAAG;QAAC;QAAa;QAAkB;KAAO;IAE1C,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;oCAEC,SAAS,IAAM,oBAAoB,SAAS,IAAI;oCAChD,WAAW,CAAC,0FAA0F,EACpG,qBAAqB,SAAS,IAAI,GAC9B,2BACA,mEACJ;;wCAED,SAAS,IAAI;wCACb,SAAS,IAAI,KAAK,uBACjB,8OAAC;4CAAK,WAAU;sDAAmC,SAAS,KAAK;;;;;;;mCAV9D,SAAS,IAAI;;;;;;;;;;sCAiBxB,8OAAC;4BAAI,WAAU;;gCAAmC;gCACvC,kBAAkB,MAAM;gCAAC;gCAAK,UAAU,MAAM;gCAAC;;;;;;;;;;;;;gBAK3D,wBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAQ,WAAU;;;;;;sCACnB,8OAAC;4BAAK,WAAU;sCAAqB;;;;;;;;;;;yCAGvC;;sCAEE,8OAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;oCAAsB,WAAU;;sDAE/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,SAAS,QAAQ,CAAC,QAAQ,CAAC,4BAC1B,8OAAC;wDACC,KAAK,SAAS,QAAQ;wDACtB,KAAK,SAAS,KAAK;wDACnB,WAAU;;;;;6EAGZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAS,WAAU;;;;;;;;;;;0EAEtB,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;;;;;;8DAM5C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,SAAS,QAAQ;;;;;;;;;;;8DAKtB,8OAAC;oDAAO,WAAU;8DAChB,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAKrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyD,SAAS,KAAK;;;;;;8DACrF,8OAAC;oDAAE,WAAU;8DAAsD,SAAS,WAAW;;;;;;8DAGvF,8OAAC;oDAAI,WAAU;8DACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,oBAClB,8OAAC;4DAAe,WAAU;sEACvB;2DADQ;;;;;;;;;;8DAOf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;;wEAAgD;wEAAE,SAAS,KAAK;;;;;;;gEAC/E,SAAS,aAAa,kBACrB;;sFACE,8OAAC;4EAAK,WAAU;;gFAAgD;gFAAE,SAAS,aAAa;;;;;;;sFACxF,8OAAC;4EAAK,WAAU;;gFAAsC,SAAS,QAAQ;gFAAC;;;;;;;;;;;;;;;sEAI9E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,SAAS,MAAM;;;;;;;;;;;;;8DAKpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;4DAAK,WAAU;;8EAC5C,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAmB;;;;;;8EACnC,8OAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;sEAE9B,8OAAC,kIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,WAAU;;8EAC1B,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;8EACxB,8OAAC;oEAAK,WAAU;8EAAmB;;;;;;8EACnC,8OAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;;;;;;;8DAKhC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAK,WAAU;;wEAAoB,SAAS,SAAS,EAAE;wEAAiB;;;;;;;8EACzE,8OAAC;oEAAK,WAAU;8EAAa,CAAC,SAAS,SAAS,IAAI,CAAC,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,CAAC,SAAS,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,GAAG,SAAS,SAAS;;;;;;;;;;;;sEAEzI,8OAAC;4DAAK,WAAU;sEAAyC;;;;;;sEACzD,8OAAC;4DAAK,WAAU;sEAAkC;;;;;;;;;;;;;;;;;;;mCAtF9C,SAAS,EAAE;;;;;;;;;;wBA8FxB,kBAAkB,MAAM,KAAK,mBAC5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;wCACf,eAAe;wCACf,oBAAoB;wCACpB,UAAU;oCACZ;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;;AAUnB", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "file": "eye.js", "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/lucide-react/src/icons/eye.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0',\n      key: '1nclc0',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi4wNjIgMTIuMzQ4YTEgMSAwIDAgMSAwLS42OTYgMTAuNzUgMTAuNzUgMCAwIDEgMTkuODc2IDAgMSAxIDAgMCAxIDAgLjY5NiAxMC43NSAxMC43NSAwIDAgMS0xOS44NzYgMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('eye', __iconNode);\n\nexport default Eye;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 813, "column": 0}, "map": {"version": 3, "file": "shopping-cart.js", "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/lucide-react/src/icons/shopping-cart.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '8', cy: '21', r: '1', key: 'jimo8o' }],\n  ['circle', { cx: '19', cy: '21', r: '1', key: '13723u' }],\n  [\n    'path',\n    {\n      d: 'M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12',\n      key: '9zh506',\n    },\n  ],\n];\n\n/**\n * @component @name ShoppingCart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI4IiBjeT0iMjEiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iMTkiIGN5PSIyMSIgcj0iMSIgLz4KICA8cGF0aCBkPSJNMi4wNSAyLjA1aDJsMi42NiAxMi40MmEyIDIgMCAwIDAgMiAxLjU4aDkuNzhhMiAyIDAgMCAwIDEuOTUtMS41N2wxLjY1LTcuNDNINS4xMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/shopping-cart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ShoppingCart = createLucideIcon('shopping-cart', __iconNode);\n\nexport default ShoppingCart;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "file": "star.js", "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/lucide-react/src/icons/star.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z',\n      key: 'r04s7s',\n    },\n  ],\n];\n\n/**\n * @component @name Star\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEuNTI1IDIuMjk1YS41My41MyAwIDAgMSAuOTUgMGwyLjMxIDQuNjc5YTIuMTIzIDIuMTIzIDAgMCAwIDEuNTk1IDEuMTZsNS4xNjYuNzU2YS41My41MyAwIDAgMSAuMjk0LjkwNGwtMy43MzYgMy42MzhhMi4xMjMgMi4xMjMgMCAwIDAtLjYxMSAxLjg3OGwuODgyIDUuMTRhLjUzLjUzIDAgMCAxLS43NzEuNTZsLTQuNjE4LTIuNDI4YTIuMTIyIDIuMTIyIDAgMCAwLTEuOTczIDBMNi4zOTYgMjEuMDFhLjUzLjUzIDAgMCAxLS43Ny0uNTZsLjg4MS01LjEzOWEyLjEyMiAyLjEyMiAwIDAgMC0uNjExLTEuODc5TDIuMTYgOS43OTVhLjUzLjUzIDAgMCAxIC4yOTQtLjkwNmw1LjE2NS0uNzU1YTIuMTIyIDIuMTIyIDAgMCAwIDEuNTk3LTEuMTZ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/star\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Star = createLucideIcon('star', __iconNode);\n\nexport default Star;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 909, "column": 0}, "map": {"version": 3, "file": "heart.js", "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/lucide-react/src/icons/heart.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z',\n      key: 'c3ymky',\n    },\n  ],\n];\n\n/**\n * @component @name Heart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMTRjMS40OS0xLjQ2IDMtMy4yMSAzLTUuNUE1LjUgNS41IDAgMCAwIDE2LjUgM2MtMS43NiAwLTMgLjUtNC41IDItMS41LTEuNS0yLjc0LTItNC41LTJBNS41IDUuNSAwIDAgMCAyIDguNWMwIDIuMyAxLjUgNC4wNSAzIDUuNWw3IDdaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/heart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Heart = createLucideIcon('heart', __iconNode);\n\nexport default Heart;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "file": "download.js", "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/lucide-react/src/icons/download.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 15V3', key: 'm9g1x1' }],\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n  ['path', { d: 'm7 10 5 5 5-5', key: 'brsn70' }],\n];\n\n/**\n * @component @name Download\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTVWMyIgLz4KICA8cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCIgLz4KICA8cGF0aCBkPSJtNyAxMCA1IDUgNS01IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/download\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Download = createLucideIcon('download', __iconNode);\n\nexport default Download;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "file": "funnel.js", "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/lucide-react/src/icons/funnel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z',\n      key: 'sc7q7i',\n    },\n  ],\n];\n\n/**\n * @component @name Funnel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMjBhMSAxIDAgMCAwIC41NTMuODk1bDIgMUExIDEgMCAwIDAgMTQgMjF2LTdhMiAyIDAgMCAxIC41MTctMS4zNDFMMjEuNzQgNC42N0ExIDEgMCAwIDAgMjEgM0gzYTEgMSAwIDAgMC0uNzQyIDEuNjdsNy4yMjUgNy45ODlBMiAyIDAgMCAxIDEwIDE0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/funnel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Funnel = createLucideIcon('funnel', __iconNode);\n\nexport default Funnel;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
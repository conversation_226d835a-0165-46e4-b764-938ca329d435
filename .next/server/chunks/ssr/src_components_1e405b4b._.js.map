{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Search, ArrowRight, Star, Users, Download } from 'lucide-react';\n\nexport const HeroSection = () => {\n  return (\n    <section className=\"bg-white py-16 lg:py-24\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          {/* Hero Content */}\n          <div className=\"mb-12\">\n            <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight\">\n              Premium Templates for\n              <span className=\"text-blue-600\">\n                {\" \"}Your Business\n              </span>\n            </h1>\n            <p className=\"text-lg text-gray-600 mb-8 max-w-2xl mx-auto\">\n              Professional, ready-to-use templates that help you build beautiful websites\n              and applications quickly and efficiently.\n            </p>\n          </div>\n\n          {/* Search Bar */}\n          <div className=\"mb-12\">\n            <div className=\"flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto\">\n              <div className=\"relative flex-1\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\n                <Input\n                  placeholder=\"Search templates, categories, or tags...\"\n                  className=\"pl-10 h-12 text-lg\"\n                />\n              </div>\n              <Button size=\"lg\" className=\"h-12 px-8\">\n                Search\n                <ArrowRight className=\"ml-2 h-5 w-5\" />\n              </Button>\n            </div>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-16\">\n            <Button asChild size=\"lg\" className=\"h-12 px-8\">\n              <Link href=\"/templates\">\n                Browse Templates\n                <ArrowRight className=\"ml-2 h-5 w-5\" />\n              </Link>\n            </Button>\n            <Button asChild variant=\"outline\" size=\"lg\" className=\"h-12 px-8\">\n              <Link href=\"/custom-request\">\n                Request Custom Design\n              </Link>\n            </Button>\n          </div>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto\">\n            <div className=\"flex flex-col items-center\">\n              <div className=\"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-3\">\n                <Download className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div className=\"text-2xl font-bold text-gray-900\">10,000+</div>\n              <div className=\"text-gray-600\">Downloads</div>\n            </div>\n            <div className=\"flex flex-col items-center\">\n              <div className=\"flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-3\">\n                <Star className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <div className=\"text-2xl font-bold text-gray-900\">4.9/5</div>\n              <div className=\"text-gray-600\">Average Rating</div>\n            </div>\n            <div className=\"flex flex-col items-center\">\n              <div className=\"flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-3\">\n                <Users className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"text-2xl font-bold text-gray-900\">5,000+</div>\n              <div className=\"text-gray-600\">Happy Customers</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Background Decoration */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full opacity-10 blur-3xl\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-purple-400 to-pink-400 rounded-full opacity-10 blur-3xl\"></div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AAQO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAkE;sDAE9E,8OAAC;4CAAK,WAAU;;gDACb;gDAAI;;;;;;;;;;;;;8CAGT,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAO9D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAGd,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;;4CAAY;0DAEtC,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAM5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,MAAK;oCAAK,WAAU;8CAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;4CAAa;0DAEtB,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG1B,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;8CACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAkB;;;;;;;;;;;;;;;;;sCAOjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/FeaturedTemplates.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardFooter } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Star, Eye, Download, ArrowRight } from 'lucide-react';\n\n// Mock data for featured templates\nconst featuredTemplates = [\n  {\n    id: '1',\n    title: 'Modern Dashboard',\n    description: 'Clean and modern dashboard template with dark mode support',\n    category: 'Dashboard',\n    price: 49,\n    imageUrl: '/api/placeholder/400/300',\n    rating: 4.9,\n    downloads: 1234,\n    featured: true,\n    tags: ['React', 'TypeScript', 'Tailwind']\n  },\n  {\n    id: '2',\n    title: 'E-commerce Store',\n    description: 'Complete e-commerce solution with shopping cart and checkout',\n    category: 'E-commerce',\n    price: 79,\n    imageUrl: '/api/placeholder/400/300',\n    rating: 4.8,\n    downloads: 856,\n    featured: true,\n    tags: ['Next.js', 'Stripe', 'Responsive']\n  },\n  {\n    id: '3',\n    title: 'Landing Page Pro',\n    description: 'High-converting landing page template for SaaS products',\n    category: 'Landing Page',\n    price: 39,\n    imageUrl: '/api/placeholder/400/300',\n    rating: 4.9,\n    downloads: 2341,\n    featured: true,\n    tags: ['HTML', 'CSS', 'JavaScript']\n  },\n  {\n    id: '4',\n    title: 'Portfolio Showcase',\n    description: 'Creative portfolio template for designers and developers',\n    category: 'Portfolio',\n    price: 29,\n    imageUrl: '/api/placeholder/400/300',\n    rating: 4.7,\n    downloads: 1567,\n    featured: true,\n    tags: ['Vue.js', 'GSAP', 'Responsive']\n  }\n];\n\nexport const FeaturedTemplates = () => {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Featured Templates\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Handpicked premium templates that deliver exceptional design and functionality\n          </p>\n        </div>\n\n        {/* Templates Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12\">\n          {featuredTemplates.map((template) => (\n            <Card key={template.id} className=\"group hover:shadow-xl transition-all duration-300 border-0 shadow-lg\">\n              <div className=\"relative overflow-hidden rounded-t-lg\">\n                <Image\n                  src={template.imageUrl}\n                  alt={template.title}\n                  width={400}\n                  height={300}\n                  className=\"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300\"\n                />\n                <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center\">\n                  <div className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-2\">\n                    <Button size=\"sm\" variant=\"secondary\">\n                      <Eye className=\"h-4 w-4 mr-1\" />\n                      Preview\n                    </Button>\n                  </div>\n                </div>\n                <Badge className=\"absolute top-3 left-3 bg-blue-600\">\n                  Featured\n                </Badge>\n              </div>\n              \n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <Badge variant=\"outline\">{template.category}</Badge>\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <Star className=\"h-4 w-4 fill-yellow-400 text-yellow-400 mr-1\" />\n                    {template.rating}\n                  </div>\n                </div>\n                \n                <h3 className=\"font-semibold text-lg text-gray-900 mb-2\">\n                  {template.title}\n                </h3>\n                \n                <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n                  {template.description}\n                </p>\n                \n                <div className=\"flex flex-wrap gap-1 mb-4\">\n                  {template.tags.slice(0, 2).map((tag) => (\n                    <Badge key={tag} variant=\"secondary\" className=\"text-xs\">\n                      {tag}\n                    </Badge>\n                  ))}\n                  {template.tags.length > 2 && (\n                    <Badge variant=\"secondary\" className=\"text-xs\">\n                      +{template.tags.length - 2}\n                    </Badge>\n                  )}\n                </div>\n                \n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <Download className=\"h-4 w-4 mr-1\" />\n                    {template.downloads.toLocaleString()}\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900\">\n                    ${template.price}\n                  </div>\n                </div>\n              </CardContent>\n              \n              <CardFooter className=\"p-6 pt-0\">\n                <Button asChild className=\"w-full\">\n                  <Link href={`/templates/${template.id}`}>\n                    View Details\n                    <ArrowRight className=\"ml-2 h-4 w-4\" />\n                  </Link>\n                </Button>\n              </CardFooter>\n            </Card>\n          ))}\n        </div>\n\n        {/* View All Button */}\n        <div className=\"text-center\">\n          <Button asChild size=\"lg\" variant=\"outline\">\n            <Link href=\"/templates\">\n              View All Templates\n              <ArrowRight className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AARA;;;;;;;;AAUA,mCAAmC;AACnC,MAAM,oBAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAS;YAAc;SAAW;IAC3C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAW;YAAU;SAAa;IAC3C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAQ;YAAO;SAAa;IACrC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAU;YAAQ;SAAa;IACxC;CACD;AAEM,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC,gIAAA,CAAA,OAAI;4BAAmB,WAAU;;8CAChC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,SAAS,QAAQ;4CACtB,KAAK,SAAS,KAAK;4CACnB,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAQ;;sEACxB,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;sDAKtC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAoC;;;;;;;;;;;;8CAKvD,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW,SAAS,QAAQ;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,SAAS,MAAM;;;;;;;;;;;;;sDAIpB,8OAAC;4CAAG,WAAU;sDACX,SAAS,KAAK;;;;;;sDAGjB,8OAAC;4CAAE,WAAU;sDACV,SAAS,WAAW;;;;;;sDAGvB,8OAAC;4CAAI,WAAU;;gDACZ,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC9B,8OAAC,iIAAA,CAAA,QAAK;wDAAW,SAAQ;wDAAY,WAAU;kEAC5C;uDADS;;;;;gDAIb,SAAS,IAAI,CAAC,MAAM,GAAG,mBACtB,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;;wDAAU;wDAC3C,SAAS,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;sDAK/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,SAAS,SAAS,CAAC,cAAc;;;;;;;8DAEpC,8OAAC;oDAAI,WAAU;;wDAAmC;wDAC9C,SAAS,KAAK;;;;;;;;;;;;;;;;;;;8CAKtB,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,WAAU;kDACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAM,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE;;gDAAE;8DAEvC,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2BAnEnB,SAAS,EAAE;;;;;;;;;;8BA4E1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,MAAK;wBAAK,SAAQ;kCAChC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;gCAAa;8CAEtB,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/CategoriesSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { \n  Monitor, \n  ShoppingCart, \n  Briefcase, \n  Users, \n  FileText, \n  Smartphone,\n  ArrowRight \n} from 'lucide-react';\n\n// Mock data for categories\nconst categories = [\n  {\n    id: '1',\n    name: 'Dashboard',\n    description: 'Admin panels and data visualization templates',\n    icon: Monitor,\n    templateCount: 45,\n    color: 'bg-blue-500'\n  },\n  {\n    id: '2',\n    name: 'E-commerce',\n    description: 'Online store and shopping cart templates',\n    icon: ShoppingCart,\n    templateCount: 32,\n    color: 'bg-green-500'\n  },\n  {\n    id: '3',\n    name: 'Portfolio',\n    description: 'Creative showcases for professionals',\n    icon: Briefcase,\n    templateCount: 28,\n    color: 'bg-purple-500'\n  },\n  {\n    id: '4',\n    name: 'Landing Page',\n    description: 'High-converting marketing pages',\n    icon: FileText,\n    templateCount: 56,\n    color: 'bg-orange-500'\n  },\n  {\n    id: '5',\n    name: 'Corporate',\n    description: 'Business and company websites',\n    icon: Users,\n    templateCount: 23,\n    color: 'bg-indigo-500'\n  },\n  {\n    id: '6',\n    name: 'Mobile App',\n    description: 'Mobile application UI templates',\n    icon: Smartphone,\n    templateCount: 19,\n    color: 'bg-pink-500'\n  }\n];\n\nexport const CategoriesSection = () => {\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Browse by Category\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Find the perfect template for your project from our organized categories\n          </p>\n        </div>\n\n        {/* Categories Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n          {categories.map((category) => {\n            const IconComponent = category.icon;\n            return (\n              <Link key={category.id} href={`/categories/${category.id}`}>\n                <Card className=\"group hover:shadow-xl transition-all duration-300 border-0 shadow-lg h-full cursor-pointer\">\n                  <CardContent className=\"p-8 text-center\">\n                    <div className={`inline-flex items-center justify-center w-16 h-16 ${category.color} rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300`}>\n                      <IconComponent className=\"h-8 w-8 text-white\" />\n                    </div>\n                    \n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors\">\n                      {category.name}\n                    </h3>\n                    \n                    <p className=\"text-gray-600 mb-4 leading-relaxed\">\n                      {category.description}\n                    </p>\n                    \n                    <div className=\"flex items-center justify-center text-sm text-gray-500\">\n                      <span className=\"font-medium\">{category.templateCount} templates</span>\n                      <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform\" />\n                    </div>\n                  </CardContent>\n                </Card>\n              </Link>\n            );\n          })}\n        </div>\n\n        {/* View All Categories Button */}\n        <div className=\"text-center\">\n          <Button asChild size=\"lg\" variant=\"outline\">\n            <Link href=\"/categories\">\n              View All Categories\n              <ArrowRight className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AAgBA,2BAA2B;AAC3B,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,wMAAA,CAAA,UAAO;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,sNAAA,CAAA,eAAY;QAClB,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,4MAAA,CAAA,YAAS;QACf,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,8MAAA,CAAA,WAAQ;QACd,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,8MAAA,CAAA,aAAU;QAChB,eAAe;QACf,OAAO;IACT;CACD;AAEM,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC;wBACf,MAAM,gBAAgB,SAAS,IAAI;wBACnC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAAmB,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE;sCACxD,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAW,CAAC,kDAAkD,EAAE,SAAS,KAAK,CAAC,yEAAyE,CAAC;sDAC5J,cAAA,8OAAC;gDAAc,WAAU;;;;;;;;;;;sDAG3B,8OAAC;4CAAG,WAAU;sDACX,SAAS,IAAI;;;;;;sDAGhB,8OAAC;4CAAE,WAAU;sDACV,SAAS,WAAW;;;;;;sDAGvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAe,SAAS,aAAa;wDAAC;;;;;;;8DACtD,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2BAjBnB,SAAS,EAAE;;;;;oBAuB1B;;;;;;8BAIF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,MAAK;wBAAK,SAAQ;kCAChC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;gCAAc;8CAEvB,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 1172, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/StatsSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { \n  Download, \n  Star, \n  Users, \n  Globe,\n  ArrowRight,\n  CheckCircle \n} from 'lucide-react';\n\nconst stats = [\n  {\n    icon: Download,\n    value: '50,000+',\n    label: 'Total Downloads',\n    description: 'Templates downloaded by developers worldwide'\n  },\n  {\n    icon: Star,\n    value: '4.9/5',\n    label: 'Average Rating',\n    description: 'Based on 10,000+ customer reviews'\n  },\n  {\n    icon: Users,\n    value: '15,000+',\n    label: 'Happy Customers',\n    description: 'Satisfied developers and designers'\n  },\n  {\n    icon: Globe,\n    value: '120+',\n    label: 'Countries',\n    description: 'Global reach across all continents'\n  }\n];\n\nconst features = [\n  'Premium quality designs',\n  'Regular updates and support',\n  'Mobile-responsive layouts',\n  'Clean, modern code',\n  'Comprehensive documentation',\n  'Lifetime access'\n];\n\nexport const StatsSection = () => {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-blue-900 via-blue-800 to-purple-900 text-white relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n        }} />\n      </div>\n\n      <div className=\"container mx-auto px-4 relative\">\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n          {/* Left Side - Stats */}\n          <div>\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n              Trusted by Developers Worldwide\n            </h2>\n            <p className=\"text-xl text-blue-100 mb-12\">\n              Join thousands of developers who have accelerated their projects with our premium templates.\n            </p>\n\n            {/* Stats Grid */}\n            <div className=\"grid grid-cols-2 gap-6 mb-12\">\n              {stats.map((stat, index) => {\n                const IconComponent = stat.icon;\n                return (\n                  <Card key={index} className=\"bg-white/10 backdrop-blur-sm border-white/20 text-white\">\n                    <CardContent className=\"p-6 text-center\">\n                      <div className=\"flex justify-center mb-4\">\n                        <div className=\"p-3 bg-white/20 rounded-lg\">\n                          <IconComponent className=\"h-6 w-6\" />\n                        </div>\n                      </div>\n                      <div className=\"text-2xl font-bold mb-1\">{stat.value}</div>\n                      <div className=\"text-sm font-medium text-blue-100 mb-2\">{stat.label}</div>\n                      <div className=\"text-xs text-blue-200\">{stat.description}</div>\n                    </CardContent>\n                  </Card>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Right Side - Features */}\n          <div>\n            <Card className=\"bg-white/10 backdrop-blur-sm border-white/20\">\n              <CardContent className=\"p-8\">\n                <h3 className=\"text-2xl font-bold text-white mb-6\">\n                  Why Choose KaleidoneX?\n                </h3>\n                \n                <div className=\"space-y-4 mb-8\">\n                  {features.map((feature, index) => (\n                    <div key={index} className=\"flex items-center\">\n                      <CheckCircle className=\"h-5 w-5 text-green-400 mr-3 flex-shrink-0\" />\n                      <span className=\"text-blue-100\">{feature}</span>\n                    </div>\n                  ))}\n                </div>\n\n                <div className=\"space-y-4\">\n                  <Button asChild size=\"lg\" className=\"w-full bg-white text-blue-900 hover:bg-blue-50\">\n                    <Link href=\"/templates\">\n                      Start Browsing Templates\n                      <ArrowRight className=\"ml-2 h-5 w-5\" />\n                    </Link>\n                  </Button>\n                  \n                  <Button asChild variant=\"outline\" size=\"lg\" className=\"w-full border-white/30 text-white hover:bg-white/10\">\n                    <Link href=\"/custom-request\">\n                      Request Custom Design\n                    </Link>\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-20\">\n          <h3 className=\"text-2xl font-bold mb-4\">\n            Ready to Build Something Amazing?\n          </h3>\n          <p className=\"text-blue-100 mb-8 max-w-2xl mx-auto\">\n            Get started with our premium templates and bring your ideas to life faster than ever before.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button asChild size=\"lg\" className=\"bg-white text-blue-900 hover:bg-blue-50\">\n              <Link href=\"/auth?mode=signup\">\n                Get Started Free\n                <ArrowRight className=\"ml-2 h-5 w-5\" />\n              </Link>\n            </Button>\n            <Button asChild variant=\"outline\" size=\"lg\" className=\"border-white/30 text-white hover:bg-white/10\">\n              <Link href=\"/contact\">\n                Contact Sales\n              </Link>\n            </Button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AAeA,MAAM,QAAQ;IACZ;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB,CAAC,gQAAgQ,CAAC;oBACrR;;;;;;;;;;;0BAGF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDAGpD,8OAAC;wCAAE,WAAU;kDAA8B;;;;;;kDAK3C,8OAAC;wCAAI,WAAU;kDACZ,MAAM,GAAG,CAAC,CAAC,MAAM;4CAChB,MAAM,gBAAgB,KAAK,IAAI;4CAC/B,qBACE,8OAAC,gIAAA,CAAA,OAAI;gDAAa,WAAU;0DAC1B,cAAA,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAc,WAAU;;;;;;;;;;;;;;;;sEAG7B,8OAAC;4DAAI,WAAU;sEAA2B,KAAK,KAAK;;;;;;sEACpD,8OAAC;4DAAI,WAAU;sEAA0C,KAAK,KAAK;;;;;;sEACnE,8OAAC;4DAAI,WAAU;sEAAyB,KAAK,WAAW;;;;;;;;;;;;+CATjD;;;;;wCAaf;;;;;;;;;;;;0CAKJ,8OAAC;0CACC,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DAInD,8OAAC;gDAAI,WAAU;0DACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;gEAAK,WAAU;0EAAiB;;;;;;;uDAFzB;;;;;;;;;;0DAOd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAC,MAAK;wDAAK,WAAU;kEAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;;gEAAa;8EAEtB,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAI1B,8OAAC,kIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAC,SAAQ;wDAAU,MAAK;wDAAK,WAAU;kEACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAGxC,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,MAAK;wCAAK,WAAU;kDAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;gDAAoB;8DAE7B,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC", "debugId": null}}]}
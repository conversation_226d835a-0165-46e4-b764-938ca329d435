{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/util/longbits.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = LongBits;\n\nvar util = require(\"../util/minimal\");\n\n/**\n * Constructs new long bits.\n * @classdesc Helper class for working with the low and high bits of a 64 bit value.\n * @memberof util\n * @constructor\n * @param {number} lo Low 32 bits, unsigned\n * @param {number} hi High 32 bits, unsigned\n */\nfunction LongBits(lo, hi) {\n\n    // note that the casts below are theoretically unnecessary as of today, but older statically\n    // generated converter code might still call the ctor with signed 32bits. kept for compat.\n\n    /**\n     * Low bits.\n     * @type {number}\n     */\n    this.lo = lo >>> 0;\n\n    /**\n     * High bits.\n     * @type {number}\n     */\n    this.hi = hi >>> 0;\n}\n\n/**\n * Zero bits.\n * @memberof util.LongBits\n * @type {util.LongBits}\n */\nvar zero = LongBits.zero = new LongBits(0, 0);\n\nzero.toNumber = function() { return 0; };\nzero.zzEncode = zero.zzDecode = function() { return this; };\nzero.length = function() { return 1; };\n\n/**\n * Zero hash.\n * @memberof util.LongBits\n * @type {string}\n */\nvar zeroHash = LongBits.zeroHash = \"\\0\\0\\0\\0\\0\\0\\0\\0\";\n\n/**\n * Constructs new long bits from the specified number.\n * @param {number} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.fromNumber = function fromNumber(value) {\n    if (value === 0)\n        return zero;\n    var sign = value < 0;\n    if (sign)\n        value = -value;\n    var lo = value >>> 0,\n        hi = (value - lo) / 4294967296 >>> 0;\n    if (sign) {\n        hi = ~hi >>> 0;\n        lo = ~lo >>> 0;\n        if (++lo > 4294967295) {\n            lo = 0;\n            if (++hi > 4294967295)\n                hi = 0;\n        }\n    }\n    return new LongBits(lo, hi);\n};\n\n/**\n * Constructs new long bits from a number, long or string.\n * @param {Long|number|string} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.from = function from(value) {\n    if (typeof value === \"number\")\n        return LongBits.fromNumber(value);\n    if (util.isString(value)) {\n        /* istanbul ignore else */\n        if (util.Long)\n            value = util.Long.fromString(value);\n        else\n            return LongBits.fromNumber(parseInt(value, 10));\n    }\n    return value.low || value.high ? new LongBits(value.low >>> 0, value.high >>> 0) : zero;\n};\n\n/**\n * Converts this long bits to a possibly unsafe JavaScript number.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {number} Possibly unsafe number\n */\nLongBits.prototype.toNumber = function toNumber(unsigned) {\n    if (!unsigned && this.hi >>> 31) {\n        var lo = ~this.lo + 1 >>> 0,\n            hi = ~this.hi     >>> 0;\n        if (!lo)\n            hi = hi + 1 >>> 0;\n        return -(lo + hi * 4294967296);\n    }\n    return this.lo + this.hi * 4294967296;\n};\n\n/**\n * Converts this long bits to a long.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long} Long\n */\nLongBits.prototype.toLong = function toLong(unsigned) {\n    return util.Long\n        ? new util.Long(this.lo | 0, this.hi | 0, Boolean(unsigned))\n        /* istanbul ignore next */\n        : { low: this.lo | 0, high: this.hi | 0, unsigned: Boolean(unsigned) };\n};\n\nvar charCodeAt = String.prototype.charCodeAt;\n\n/**\n * Constructs new long bits from the specified 8 characters long hash.\n * @param {string} hash Hash\n * @returns {util.LongBits} Bits\n */\nLongBits.fromHash = function fromHash(hash) {\n    if (hash === zeroHash)\n        return zero;\n    return new LongBits(\n        ( charCodeAt.call(hash, 0)\n        | charCodeAt.call(hash, 1) << 8\n        | charCodeAt.call(hash, 2) << 16\n        | charCodeAt.call(hash, 3) << 24) >>> 0\n    ,\n        ( charCodeAt.call(hash, 4)\n        | charCodeAt.call(hash, 5) << 8\n        | charCodeAt.call(hash, 6) << 16\n        | charCodeAt.call(hash, 7) << 24) >>> 0\n    );\n};\n\n/**\n * Converts this long bits to a 8 characters long hash.\n * @returns {string} Hash\n */\nLongBits.prototype.toHash = function toHash() {\n    return String.fromCharCode(\n        this.lo        & 255,\n        this.lo >>> 8  & 255,\n        this.lo >>> 16 & 255,\n        this.lo >>> 24      ,\n        this.hi        & 255,\n        this.hi >>> 8  & 255,\n        this.hi >>> 16 & 255,\n        this.hi >>> 24\n    );\n};\n\n/**\n * Zig-zag encodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzEncode = function zzEncode() {\n    var mask =   this.hi >> 31;\n    this.hi  = ((this.hi << 1 | this.lo >>> 31) ^ mask) >>> 0;\n    this.lo  = ( this.lo << 1                   ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Zig-zag decodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzDecode = function zzDecode() {\n    var mask = -(this.lo & 1);\n    this.lo  = ((this.lo >>> 1 | this.hi << 31) ^ mask) >>> 0;\n    this.hi  = ( this.hi >>> 1                  ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Calculates the length of this longbits when encoded as a varint.\n * @returns {number} Length\n */\nLongBits.prototype.length = function length() {\n    var part0 =  this.lo,\n        part1 = (this.lo >>> 28 | this.hi << 4) >>> 0,\n        part2 =  this.hi >>> 24;\n    return part2 === 0\n         ? part1 === 0\n           ? part0 < 16384\n             ? part0 < 128 ? 1 : 2\n             : part0 < 2097152 ? 3 : 4\n           : part1 < 16384\n             ? part1 < 128 ? 5 : 6\n             : part1 < 2097152 ? 7 : 8\n         : part2 < 128 ? 9 : 10;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,IAAI;AAEJ;;;;;;;CAOC,GACD,SAAS,SAAS,EAAE,EAAE,EAAE;IAEpB,4FAA4F;IAC5F,0FAA0F;IAE1F;;;KAGC,GACD,IAAI,CAAC,EAAE,GAAG,OAAO;IAEjB;;;KAGC,GACD,IAAI,CAAC,EAAE,GAAG,OAAO;AACrB;AAEA;;;;CAIC,GACD,IAAI,OAAO,SAAS,IAAI,GAAG,IAAI,SAAS,GAAG;AAE3C,KAAK,QAAQ,GAAG;IAAa,OAAO;AAAG;AACvC,KAAK,QAAQ,GAAG,KAAK,QAAQ,GAAG;IAAa,OAAO,IAAI;AAAE;AAC1D,KAAK,MAAM,GAAG;IAAa,OAAO;AAAG;AAErC;;;;CAIC,GACD,IAAI,WAAW,SAAS,QAAQ,GAAG;AAEnC;;;;CAIC,GACD,SAAS,UAAU,GAAG,SAAS,WAAW,KAAK;IAC3C,IAAI,UAAU,GACV,OAAO;IACX,IAAI,OAAO,QAAQ;IACnB,IAAI,MACA,QAAQ,CAAC;IACb,IAAI,KAAK,UAAU,GACf,KAAK,CAAC,QAAQ,EAAE,IAAI,eAAe;IACvC,IAAI,MAAM;QACN,KAAK,CAAC,OAAO;QACb,KAAK,CAAC,OAAO;QACb,IAAI,EAAE,KAAK,YAAY;YACnB,KAAK;YACL,IAAI,EAAE,KAAK,YACP,KAAK;QACb;IACJ;IACA,OAAO,IAAI,SAAS,IAAI;AAC5B;AAEA;;;;CAIC,GACD,SAAS,IAAI,GAAG,SAAS,KAAK,KAAK;IAC/B,IAAI,OAAO,UAAU,UACjB,OAAO,SAAS,UAAU,CAAC;IAC/B,IAAI,KAAK,QAAQ,CAAC,QAAQ;QACtB,wBAAwB,GACxB,IAAI,KAAK,IAAI,EACT,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC;aAE7B,OAAO,SAAS,UAAU,CAAC,SAAS,OAAO;IACnD;IACA,OAAO,MAAM,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,SAAS,MAAM,GAAG,KAAK,GAAG,MAAM,IAAI,KAAK,KAAK;AACvF;AAEA;;;;CAIC,GACD,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAS,QAAQ;IACpD,IAAI,CAAC,YAAY,IAAI,CAAC,EAAE,KAAK,IAAI;QAC7B,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,MAAM,GACtB,KAAK,CAAC,IAAI,CAAC,EAAE,KAAS;QAC1B,IAAI,CAAC,IACD,KAAK,KAAK,MAAM;QACpB,OAAO,CAAC,CAAC,KAAK,KAAK,UAAU;IACjC;IACA,OAAO,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG;AAC/B;AAEA;;;;CAIC,GACD,SAAS,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,QAAQ;IAChD,OAAO,KAAK,IAAI,GACV,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,QAAQ,aAEhD;QAAE,KAAK,IAAI,CAAC,EAAE,GAAG;QAAG,MAAM,IAAI,CAAC,EAAE,GAAG;QAAG,UAAU,QAAQ;IAAU;AAC7E;AAEA,IAAI,aAAa,OAAO,SAAS,CAAC,UAAU;AAE5C;;;;CAIC,GACD,SAAS,QAAQ,GAAG,SAAS,SAAS,IAAI;IACtC,IAAI,SAAS,UACT,OAAO;IACX,OAAO,IAAI,SACP,CAAE,WAAW,IAAI,CAAC,MAAM,KACtB,WAAW,IAAI,CAAC,MAAM,MAAM,IAC5B,WAAW,IAAI,CAAC,MAAM,MAAM,KAC5B,WAAW,IAAI,CAAC,MAAM,MAAM,EAAE,MAAM,GAEtC,CAAE,WAAW,IAAI,CAAC,MAAM,KACtB,WAAW,IAAI,CAAC,MAAM,MAAM,IAC5B,WAAW,IAAI,CAAC,MAAM,MAAM,KAC5B,WAAW,IAAI,CAAC,MAAM,MAAM,EAAE,MAAM;AAE9C;AAEA;;;CAGC,GACD,SAAS,SAAS,CAAC,MAAM,GAAG,SAAS;IACjC,OAAO,OAAO,YAAY,CACtB,IAAI,CAAC,EAAE,GAAU,KACjB,IAAI,CAAC,EAAE,KAAK,IAAK,KACjB,IAAI,CAAC,EAAE,KAAK,KAAK,KACjB,IAAI,CAAC,EAAE,KAAK,IACZ,IAAI,CAAC,EAAE,GAAU,KACjB,IAAI,CAAC,EAAE,KAAK,IAAK,KACjB,IAAI,CAAC,EAAE,KAAK,KAAK,KACjB,IAAI,CAAC,EAAE,KAAK;AAEpB;AAEA;;;CAGC,GACD,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAS;IACnC,IAAI,OAAS,IAAI,CAAC,EAAE,IAAI;IACxB,IAAI,CAAC,EAAE,GAAI,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,IAAI,MAAM;IACxD,IAAI,CAAC,EAAE,GAAI,CAAE,IAAI,CAAC,EAAE,IAAI,IAAsB,IAAI,MAAM;IACxD,OAAO,IAAI;AACf;AAEA;;;CAGC,GACD,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAS;IACnC,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IACxB,IAAI,CAAC,EAAE,GAAI,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,IAAI,MAAM;IACxD,IAAI,CAAC,EAAE,GAAI,CAAE,IAAI,CAAC,EAAE,KAAK,IAAqB,IAAI,MAAM;IACxD,OAAO,IAAI;AACf;AAEA;;;CAGC,GACD,SAAS,SAAS,CAAC,MAAM,GAAG,SAAS;IACjC,IAAI,QAAS,IAAI,CAAC,EAAE,EAChB,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,GAC5C,QAAS,IAAI,CAAC,EAAE,KAAK;IACzB,OAAO,UAAU,IACV,UAAU,IACR,QAAQ,QACN,QAAQ,MAAM,IAAI,IAClB,QAAQ,UAAU,IAAI,IACxB,QAAQ,QACN,QAAQ,MAAM,IAAI,IAClB,QAAQ,UAAU,IAAI,IAC1B,QAAQ,MAAM,IAAI;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/util/minimal.js"], "sourcesContent": ["\"use strict\";\nvar util = exports;\n\n// used to return a Promise where callback is omitted\nutil.asPromise = require(\"@protobufjs/aspromise\");\n\n// converts to / from base64 encoded strings\nutil.base64 = require(\"@protobufjs/base64\");\n\n// base class of rpc.Service\nutil.EventEmitter = require(\"@protobufjs/eventemitter\");\n\n// float handling accross browsers\nutil.float = require(\"@protobufjs/float\");\n\n// requires modules optionally and hides the call from bundlers\nutil.inquire = require(\"@protobufjs/inquire\");\n\n// converts to / from utf8 encoded strings\nutil.utf8 = require(\"@protobufjs/utf8\");\n\n// provides a node-like buffer pool in the browser\nutil.pool = require(\"@protobufjs/pool\");\n\n// utility to work with the low and high bits of a 64 bit value\nutil.LongBits = require(\"./longbits\");\n\n/**\n * Whether running within node or not.\n * @memberof util\n * @type {boolean}\n */\nutil.isNode = Boolean(typeof global !== \"undefined\"\n                   && global\n                   && global.process\n                   && global.process.versions\n                   && global.process.versions.node);\n\n/**\n * Global object reference.\n * @memberof util\n * @type {Object}\n */\nutil.global = util.isNode && global\n           || typeof window !== \"undefined\" && window\n           || typeof self   !== \"undefined\" && self\n           || this; // eslint-disable-line no-invalid-this\n\n/**\n * An immuable empty array.\n * @memberof util\n * @type {Array.<*>}\n * @const\n */\nutil.emptyArray = Object.freeze ? Object.freeze([]) : /* istanbul ignore next */ []; // used on prototypes\n\n/**\n * An immutable empty object.\n * @type {Object}\n * @const\n */\nutil.emptyObject = Object.freeze ? Object.freeze({}) : /* istanbul ignore next */ {}; // used on prototypes\n\n/**\n * Tests if the specified value is an integer.\n * @function\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is an integer\n */\nutil.isInteger = Number.isInteger || /* istanbul ignore next */ function isInteger(value) {\n    return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\n};\n\n/**\n * Tests if the specified value is a string.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a string\n */\nutil.isString = function isString(value) {\n    return typeof value === \"string\" || value instanceof String;\n};\n\n/**\n * Tests if the specified value is a non-null object.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a non-null object\n */\nutil.isObject = function isObject(value) {\n    return value && typeof value === \"object\";\n};\n\n/**\n * Checks if a property on a message is considered to be present.\n * This is an alias of {@link util.isSet}.\n * @function\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isset =\n\n/**\n * Checks if a property on a message is considered to be present.\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isSet = function isSet(obj, prop) {\n    var value = obj[prop];\n    if (value != null && obj.hasOwnProperty(prop)) // eslint-disable-line eqeqeq, no-prototype-builtins\n        return typeof value !== \"object\" || (Array.isArray(value) ? value.length : Object.keys(value).length) > 0;\n    return false;\n};\n\n/**\n * Any compatible Buffer instance.\n * This is a minimal stand-alone definition of a Buffer instance. The actual type is that exported by node's typings.\n * @interface Buffer\n * @extends Uint8Array\n */\n\n/**\n * Node's Buffer class if available.\n * @type {Constructor<Buffer>}\n */\nutil.Buffer = (function() {\n    try {\n        var Buffer = util.inquire(\"buffer\").Buffer;\n        // refuse to use non-node buffers if not explicitly assigned (perf reasons):\n        return Buffer.prototype.utf8Write ? Buffer : /* istanbul ignore next */ null;\n    } catch (e) {\n        /* istanbul ignore next */\n        return null;\n    }\n})();\n\n// Internal alias of or polyfull for Buffer.from.\nutil._Buffer_from = null;\n\n// Internal alias of or polyfill for Buffer.allocUnsafe.\nutil._Buffer_allocUnsafe = null;\n\n/**\n * Creates a new buffer of whatever type supported by the environment.\n * @param {number|number[]} [sizeOrArray=0] Buffer size or number array\n * @returns {Uint8Array|Buffer} Buffer\n */\nutil.newBuffer = function newBuffer(sizeOrArray) {\n    /* istanbul ignore next */\n    return typeof sizeOrArray === \"number\"\n        ? util.Buffer\n            ? util._Buffer_allocUnsafe(sizeOrArray)\n            : new util.Array(sizeOrArray)\n        : util.Buffer\n            ? util._Buffer_from(sizeOrArray)\n            : typeof Uint8Array === \"undefined\"\n                ? sizeOrArray\n                : new Uint8Array(sizeOrArray);\n};\n\n/**\n * Array implementation used in the browser. `Uint8Array` if supported, otherwise `Array`.\n * @type {Constructor<Uint8Array>}\n */\nutil.Array = typeof Uint8Array !== \"undefined\" ? Uint8Array /* istanbul ignore next */ : Array;\n\n/**\n * Any compatible Long instance.\n * This is a minimal stand-alone definition of a Long instance. The actual type is that exported by long.js.\n * @interface Long\n * @property {number} low Low bits\n * @property {number} high High bits\n * @property {boolean} unsigned Whether unsigned or not\n */\n\n/**\n * Long.js's Long class if available.\n * @type {Constructor<Long>}\n */\nutil.Long = /* istanbul ignore next */ util.global.dcodeIO && /* istanbul ignore next */ util.global.dcodeIO.Long\n         || /* istanbul ignore next */ util.global.Long\n         || util.inquire(\"long\");\n\n/**\n * Regular expression used to verify 2 bit (`bool`) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key2Re = /^true|false|0|1$/;\n\n/**\n * Regular expression used to verify 32 bit (`int32` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key32Re = /^-?(?:0|[1-9][0-9]*)$/;\n\n/**\n * Regular expression used to verify 64 bit (`int64` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key64Re = /^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;\n\n/**\n * Converts a number or long to an 8 characters long hash string.\n * @param {Long|number} value Value to convert\n * @returns {string} Hash\n */\nutil.longToHash = function longToHash(value) {\n    return value\n        ? util.LongBits.from(value).toHash()\n        : util.LongBits.zeroHash;\n};\n\n/**\n * Converts an 8 characters long hash string to a long or number.\n * @param {string} hash Hash\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long|number} Original value\n */\nutil.longFromHash = function longFromHash(hash, unsigned) {\n    var bits = util.LongBits.fromHash(hash);\n    if (util.Long)\n        return util.Long.fromBits(bits.lo, bits.hi, unsigned);\n    return bits.toNumber(Boolean(unsigned));\n};\n\n/**\n * Merges the properties of the source object into the destination object.\n * @memberof util\n * @param {Object.<string,*>} dst Destination object\n * @param {Object.<string,*>} src Source object\n * @param {boolean} [ifNotSet=false] Merges only if the key is not already set\n * @returns {Object.<string,*>} Destination object\n */\nfunction merge(dst, src, ifNotSet) { // used by converters\n    for (var keys = Object.keys(src), i = 0; i < keys.length; ++i)\n        if (dst[keys[i]] === undefined || !ifNotSet)\n            dst[keys[i]] = src[keys[i]];\n    return dst;\n}\n\nutil.merge = merge;\n\n/**\n * Converts the first character of a string to lower case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.lcFirst = function lcFirst(str) {\n    return str.charAt(0).toLowerCase() + str.substring(1);\n};\n\n/**\n * Creates a custom error constructor.\n * @memberof util\n * @param {string} name Error name\n * @returns {Constructor<Error>} Custom error constructor\n */\nfunction newError(name) {\n\n    function CustomError(message, properties) {\n\n        if (!(this instanceof CustomError))\n            return new CustomError(message, properties);\n\n        // Error.call(this, message);\n        // ^ just returns a new error instance because the ctor can be called as a function\n\n        Object.defineProperty(this, \"message\", { get: function() { return message; } });\n\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) // node\n            Error.captureStackTrace(this, CustomError);\n        else\n            Object.defineProperty(this, \"stack\", { value: new Error().stack || \"\" });\n\n        if (properties)\n            merge(this, properties);\n    }\n\n    CustomError.prototype = Object.create(Error.prototype, {\n        constructor: {\n            value: CustomError,\n            writable: true,\n            enumerable: false,\n            configurable: true,\n        },\n        name: {\n            get: function get() { return name; },\n            set: undefined,\n            enumerable: false,\n            // configurable: false would accurately preserve the behavior of\n            // the original, but I'm guessing that was not intentional.\n            // For an actual error subclass, this property would\n            // be configurable.\n            configurable: true,\n        },\n        toString: {\n            value: function value() { return this.name + \": \" + this.message; },\n            writable: true,\n            enumerable: false,\n            configurable: true,\n        },\n    });\n\n    return CustomError;\n}\n\nutil.newError = newError;\n\n/**\n * Constructs a new protocol error.\n * @classdesc Error subclass indicating a protocol specifc error.\n * @memberof util\n * @extends Error\n * @template T extends Message<T>\n * @constructor\n * @param {string} message Error message\n * @param {Object.<string,*>} [properties] Additional properties\n * @example\n * try {\n *     MyMessage.decode(someBuffer); // throws if required fields are missing\n * } catch (e) {\n *     if (e instanceof ProtocolError && e.instance)\n *         console.log(\"decoded so far: \" + JSON.stringify(e.instance));\n * }\n */\nutil.ProtocolError = newError(\"ProtocolError\");\n\n/**\n * So far decoded message instance.\n * @name util.ProtocolError#instance\n * @type {Message<T>}\n */\n\n/**\n * A OneOf getter as returned by {@link util.oneOfGetter}.\n * @typedef OneOfGetter\n * @type {function}\n * @returns {string|undefined} Set field name, if any\n */\n\n/**\n * Builds a getter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfGetter} Unbound getter\n */\nutil.oneOfGetter = function getOneOf(fieldNames) {\n    var fieldMap = {};\n    for (var i = 0; i < fieldNames.length; ++i)\n        fieldMap[fieldNames[i]] = 1;\n\n    /**\n     * @returns {string|undefined} Set field name, if any\n     * @this Object\n     * @ignore\n     */\n    return function() { // eslint-disable-line consistent-return\n        for (var keys = Object.keys(this), i = keys.length - 1; i > -1; --i)\n            if (fieldMap[keys[i]] === 1 && this[keys[i]] !== undefined && this[keys[i]] !== null)\n                return keys[i];\n    };\n};\n\n/**\n * A OneOf setter as returned by {@link util.oneOfSetter}.\n * @typedef OneOfSetter\n * @type {function}\n * @param {string|undefined} value Field name\n * @returns {undefined}\n */\n\n/**\n * Builds a setter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfSetter} Unbound setter\n */\nutil.oneOfSetter = function setOneOf(fieldNames) {\n\n    /**\n     * @param {string} name Field name\n     * @returns {undefined}\n     * @this Object\n     * @ignore\n     */\n    return function(name) {\n        for (var i = 0; i < fieldNames.length; ++i)\n            if (fieldNames[i] !== name)\n                delete this[fieldNames[i]];\n    };\n};\n\n/**\n * Default conversion options used for {@link Message#toJSON} implementations.\n *\n * These options are close to proto3's JSON mapping with the exception that internal types like Any are handled just like messages. More precisely:\n *\n * - Longs become strings\n * - Enums become string keys\n * - Bytes become base64 encoded strings\n * - (Sub-)Messages become plain objects\n * - Maps become plain objects with all string keys\n * - Repeated fields become arrays\n * - NaN and Infinity for float and double fields become strings\n *\n * @type {IConversionOptions}\n * @see https://developers.google.com/protocol-buffers/docs/proto3?hl=en#json\n */\nutil.toJSONOptions = {\n    longs: String,\n    enums: String,\n    bytes: String,\n    json: true\n};\n\n// Sets up buffer utility according to the environment (called in index-minimal)\nutil._configure = function() {\n    var Buffer = util.Buffer;\n    /* istanbul ignore if */\n    if (!Buffer) {\n        util._Buffer_from = util._Buffer_allocUnsafe = null;\n        return;\n    }\n    // because node 4.x buffers are incompatible & immutable\n    // see: https://github.com/dcodeIO/protobuf.js/pull/665\n    util._Buffer_from = Buffer.from !== Uint8Array.from && Buffer.from ||\n        /* istanbul ignore next */\n        function Buffer_from(value, encoding) {\n            return new Buffer(value, encoding);\n        };\n    util._Buffer_allocUnsafe = Buffer.allocUnsafe ||\n        /* istanbul ignore next */\n        function Buffer_allocUnsafe(size) {\n            return new Buffer(size);\n        };\n};\n"], "names": [], "mappings": "AAAA;AACA,IAAI,OAAO;AAEX,qDAAqD;AACrD,KAAK,SAAS;AAEd,4CAA4C;AAC5C,KAAK,MAAM;AAEX,4BAA4B;AAC5B,KAAK,YAAY;AAEjB,kCAAkC;AAClC,KAAK,KAAK;AAEV,+DAA+D;AAC/D,KAAK,OAAO;AAEZ,0CAA0C;AAC1C,KAAK,IAAI;AAET,kDAAkD;AAClD,KAAK,IAAI;AAET,+DAA+D;AAC/D,KAAK,QAAQ;AAEb;;;;CAIC,GACD,KAAK,MAAM,GAAG,QAAQ,OAAO,WAAW,eAClB,UACA,OAAO,OAAO,IACd,OAAO,OAAO,CAAC,QAAQ,IACvB,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI;AAElD;;;;CAIC,GACD,KAAK,MAAM,GAAG,KAAK,MAAM,IAAI,UACf,OAAO,WAAW,eAAe,UACjC,OAAO,SAAW,eAAe,QACjC,IAAI,EAAE,sCAAsC;AAE1D;;;;;CAKC,GACD,KAAK,UAAU,GAAG,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC,EAAE,IAAI,wBAAwB,GAAG,EAAE,EAAE,qBAAqB;AAE1G;;;;CAIC,GACD,KAAK,WAAW,GAAG,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC,CAAC,KAAK,wBAAwB,GAAG,CAAC,GAAG,qBAAqB;AAE3G;;;;;CAKC,GACD,KAAK,SAAS,GAAG,OAAO,SAAS,IAAI,wBAAwB,GAAG,SAAS,UAAU,KAAK;IACpF,OAAO,OAAO,UAAU,YAAY,SAAS,UAAU,KAAK,KAAK,CAAC,WAAW;AACjF;AAEA;;;;CAIC,GACD,KAAK,QAAQ,GAAG,SAAS,SAAS,KAAK;IACnC,OAAO,OAAO,UAAU,YAAY,iBAAiB;AACzD;AAEA;;;;CAIC,GACD,KAAK,QAAQ,GAAG,SAAS,SAAS,KAAK;IACnC,OAAO,SAAS,OAAO,UAAU;AACrC;AAEA;;;;;;;CAOC,GACD,KAAK,KAAK,GAEV;;;;;CAKC,GACD,KAAK,KAAK,GAAG,SAAS,MAAM,GAAG,EAAE,IAAI;IACjC,IAAI,QAAQ,GAAG,CAAC,KAAK;IACrB,IAAI,SAAS,QAAQ,IAAI,cAAc,CAAC,OACpC,OAAO,OAAO,UAAU,YAAY,CAAC,MAAM,OAAO,CAAC,SAAS,MAAM,MAAM,GAAG,OAAO,IAAI,CAAC,OAAO,MAAM,IAAI;IAC5G,OAAO;AACX;AAEA;;;;;CAKC,GAED;;;CAGC,GACD,KAAK,MAAM,GAAG,AAAC;IACX,IAAI;QACA,IAAI,SAAS,KAAK,OAAO,CAAC,UAAU,MAAM;QAC1C,4EAA4E;QAC5E,OAAO,OAAO,SAAS,CAAC,SAAS,GAAG,SAAS,wBAAwB,GAAG;IAC5E,EAAE,OAAO,GAAG;QACR,wBAAwB,GACxB,OAAO;IACX;AACJ;AAEA,iDAAiD;AACjD,KAAK,YAAY,GAAG;AAEpB,wDAAwD;AACxD,KAAK,mBAAmB,GAAG;AAE3B;;;;CAIC,GACD,KAAK,SAAS,GAAG,SAAS,UAAU,WAAW;IAC3C,wBAAwB,GACxB,OAAO,OAAO,gBAAgB,WACxB,KAAK,MAAM,GACP,KAAK,mBAAmB,CAAC,eACzB,IAAI,KAAK,KAAK,CAAC,eACnB,KAAK,MAAM,GACP,KAAK,YAAY,CAAC,eAClB,OAAO,eAAe,cAClB,cACA,IAAI,WAAW;AACjC;AAEA;;;CAGC,GACD,KAAK,KAAK,GAAG,OAAO,eAAe,cAAc,WAAW,wBAAwB,MAAK;AAEzF;;;;;;;CAOC,GAED;;;CAGC,GACD,KAAK,IAAI,GAAG,wBAAwB,GAAG,KAAK,MAAM,CAAC,OAAO,IAAI,wBAAwB,GAAG,KAAK,MAAM,CAAC,OAAO,CAAC,IAAI,IACrG,wBAAwB,GAAG,KAAK,MAAM,CAAC,IAAI,IAC3C,KAAK,OAAO,CAAC;AAEzB;;;;CAIC,GACD,KAAK,MAAM,GAAG;AAEd;;;;CAIC,GACD,KAAK,OAAO,GAAG;AAEf;;;;CAIC,GACD,KAAK,OAAO,GAAG;AAEf;;;;CAIC,GACD,KAAK,UAAU,GAAG,SAAS,WAAW,KAAK;IACvC,OAAO,QACD,KAAK,QAAQ,CAAC,IAAI,CAAC,OAAO,MAAM,KAChC,KAAK,QAAQ,CAAC,QAAQ;AAChC;AAEA;;;;;CAKC,GACD,KAAK,YAAY,GAAG,SAAS,aAAa,IAAI,EAAE,QAAQ;IACpD,IAAI,OAAO,KAAK,QAAQ,CAAC,QAAQ,CAAC;IAClC,IAAI,KAAK,IAAI,EACT,OAAO,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE;IAChD,OAAO,KAAK,QAAQ,CAAC,QAAQ;AACjC;AAEA;;;;;;;CAOC,GACD,SAAS,MAAM,GAAG,EAAE,GAAG,EAAE,QAAQ;IAC7B,IAAK,IAAI,OAAO,OAAO,IAAI,CAAC,MAAM,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EACxD,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,aAAa,CAAC,UAC/B,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;IACnC,OAAO;AACX;AAEA,KAAK,KAAK,GAAG;AAEb;;;;CAIC,GACD,KAAK,OAAO,GAAG,SAAS,QAAQ,GAAG;IAC/B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,SAAS,CAAC;AACvD;AAEA;;;;;CAKC,GACD,SAAS,SAAS,IAAI;IAElB,SAAS,YAAY,OAAO,EAAE,UAAU;QAEpC,IAAI,CAAC,CAAC,IAAI,YAAY,WAAW,GAC7B,OAAO,IAAI,YAAY,SAAS;QAEpC,6BAA6B;QAC7B,mFAAmF;QAEnF,OAAO,cAAc,CAAC,IAAI,EAAE,WAAW;YAAE,KAAK;gBAAa,OAAO;YAAS;QAAE;QAE7E,wBAAwB,GACxB,IAAI,MAAM,iBAAiB,EACvB,MAAM,iBAAiB,CAAC,IAAI,EAAE;aAE9B,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS;YAAE,OAAO,IAAI,QAAQ,KAAK,IAAI;QAAG;QAE1E,IAAI,YACA,MAAM,IAAI,EAAE;IACpB;IAEA,YAAY,SAAS,GAAG,OAAO,MAAM,CAAC,MAAM,SAAS,EAAE;QACnD,aAAa;YACT,OAAO;YACP,UAAU;YACV,YAAY;YACZ,cAAc;QAClB;QACA,MAAM;YACF,KAAK,SAAS;gBAAQ,OAAO;YAAM;YACnC,KAAK;YACL,YAAY;YACZ,gEAAgE;YAChE,2DAA2D;YAC3D,oDAAoD;YACpD,mBAAmB;YACnB,cAAc;QAClB;QACA,UAAU;YACN,OAAO,SAAS;gBAAU,OAAO,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI,CAAC,OAAO;YAAE;YAClE,UAAU;YACV,YAAY;YACZ,cAAc;QAClB;IACJ;IAEA,OAAO;AACX;AAEA,KAAK,QAAQ,GAAG;AAEhB;;;;;;;;;;;;;;;;CAgBC,GACD,KAAK,aAAa,GAAG,SAAS;AAE9B;;;;CAIC,GAED;;;;;CAKC,GAED;;;;CAIC,GACD,KAAK,WAAW,GAAG,SAAS,SAAS,UAAU;IAC3C,IAAI,WAAW,CAAC;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,EAAE,EACrC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG;IAE9B;;;;KAIC,GACD,OAAO;QACH,IAAK,IAAI,OAAO,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,EAC9D,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,aAAa,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,MAC5E,OAAO,IAAI,CAAC,EAAE;IAC1B;AACJ;AAEA;;;;;;CAMC,GAED;;;;CAIC,GACD,KAAK,WAAW,GAAG,SAAS,SAAS,UAAU;IAE3C;;;;;KAKC,GACD,OAAO,SAAS,IAAI;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,EAAE,EACrC,IAAI,UAAU,CAAC,EAAE,KAAK,MAClB,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;IACtC;AACJ;AAEA;;;;;;;;;;;;;;;CAeC,GACD,KAAK,aAAa,GAAG;IACjB,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;AACV;AAEA,gFAAgF;AAChF,KAAK,UAAU,GAAG;IACd,IAAI,SAAS,KAAK,MAAM;IACxB,sBAAsB,GACtB,IAAI,CAAC,QAAQ;QACT,KAAK,YAAY,GAAG,KAAK,mBAAmB,GAAG;QAC/C;IACJ;IACA,wDAAwD;IACxD,uDAAuD;IACvD,KAAK,YAAY,GAAG,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,OAAO,IAAI,IAC9D,wBAAwB,GACxB,SAAS,YAAY,KAAK,EAAE,QAAQ;QAChC,OAAO,IAAI,OAAO,OAAO;IAC7B;IACJ,KAAK,mBAAmB,GAAG,OAAO,WAAW,IACzC,wBAAwB,GACxB,SAAS,mBAAmB,IAAI;QAC5B,OAAO,IAAI,OAAO;IACtB;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/roots.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = {};\n\n/**\n * Named roots.\n * This is where pbjs stores generated structures (the option `-r, --root` specifies a name).\n * Can also be used manually to make roots available across modules.\n * @name roots\n * @type {Object.<string,Root>}\n * @example\n * // pbjs -r myroot -o compiled.js ...\n *\n * // in another module:\n * require(\"./compiled.js\");\n *\n * // in any subsequent module:\n * var root = protobuf.roots[\"myroot\"];\n */\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,CAAC,GAElB;;;;;;;;;;;;;;CAcC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/namespace.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = Namespace;\n\n// extends ReflectionObject\nvar ReflectionObject = require(\"./object\");\n((Namespace.prototype = Object.create(ReflectionObject.prototype)).constructor = Namespace).className = \"Namespace\";\n\nvar Field    = require(\"./field\"),\n    util     = require(\"./util\"),\n    OneOf    = require(\"./oneof\");\n\nvar Type,    // cyclic\n    Service,\n    Enum;\n\n/**\n * Constructs a new namespace instance.\n * @name Namespace\n * @classdesc Reflected namespace.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a namespace from JSON.\n * @memberof Namespace\n * @function\n * @param {string} name Namespace name\n * @param {Object.<string,*>} json JSON object\n * @returns {Namespace} Created namespace\n * @throws {TypeError} If arguments are invalid\n */\nNamespace.fromJSON = function fromJSON(name, json) {\n    return new Namespace(name, json.options).addJSON(json.nested);\n};\n\n/**\n * Converts an array of reflection objects to JSON.\n * @memberof Namespace\n * @param {ReflectionObject[]} array Object array\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {Object.<string,*>|undefined} JSON object or `undefined` when array is empty\n */\nfunction arrayToJSON(array, toJSONOptions) {\n    if (!(array && array.length))\n        return undefined;\n    var obj = {};\n    for (var i = 0; i < array.length; ++i)\n        obj[array[i].name] = array[i].toJSON(toJSONOptions);\n    return obj;\n}\n\nNamespace.arrayToJSON = arrayToJSON;\n\n/**\n * Tests if the specified id is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedId = function isReservedId(reserved, id) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (typeof reserved[i] !== \"string\" && reserved[i][0] <= id && reserved[i][1] > id)\n                return true;\n    return false;\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedName = function isReservedName(reserved, name) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (reserved[i] === name)\n                return true;\n    return false;\n};\n\n/**\n * Not an actual constructor. Use {@link Namespace} instead.\n * @classdesc Base class of all reflection objects containing nested objects. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports NamespaceBase\n * @extends ReflectionObject\n * @abstract\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n * @see {@link Namespace}\n */\nfunction Namespace(name, options) {\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Nested objects by name.\n     * @type {Object.<string,ReflectionObject>|undefined}\n     */\n    this.nested = undefined; // toJSON\n\n    /**\n     * Cached nested objects as an array.\n     * @type {ReflectionObject[]|null}\n     * @private\n     */\n    this._nestedArray = null;\n\n    /**\n     * Cache lookup calls for any objects contains anywhere under this namespace.\n     * This drastically speeds up resolve for large cross-linked protos where the same\n     * types are looked up repeatedly.\n     * @type {Object.<string,ReflectionObject|null>}\n     * @private\n     */\n    this._lookupCache = {};\n\n    /**\n     * Whether or not objects contained in this namespace need feature resolution.\n     * @type {boolean}\n     * @protected\n     */\n    this._needsRecursiveFeatureResolution = true;\n\n    /**\n     * Whether or not objects contained in this namespace need a resolve.\n     * @type {boolean}\n     * @protected\n     */\n    this._needsRecursiveResolve = true;\n}\n\nfunction clearCache(namespace) {\n    namespace._nestedArray = null;\n    namespace._lookupCache = {};\n\n    // Also clear parent caches, since they include nested lookups.\n    var parent = namespace;\n    while(parent = parent.parent) {\n        parent._lookupCache = {};\n    }\n    return namespace;\n}\n\n/**\n * Nested objects of this namespace as an array for iteration.\n * @name NamespaceBase#nestedArray\n * @type {ReflectionObject[]}\n * @readonly\n */\nObject.defineProperty(Namespace.prototype, \"nestedArray\", {\n    get: function() {\n        return this._nestedArray || (this._nestedArray = util.toArray(this.nested));\n    }\n});\n\n/**\n * Namespace descriptor.\n * @interface INamespace\n * @property {Object.<string,*>} [options] Namespace options\n * @property {Object.<string,AnyNestedObject>} [nested] Nested object descriptors\n */\n\n/**\n * Any extension field descriptor.\n * @typedef AnyExtensionField\n * @type {IExtensionField|IExtensionMapField}\n */\n\n/**\n * Any nested object descriptor.\n * @typedef AnyNestedObject\n * @type {IEnum|IType|IService|AnyExtensionField|INamespace|IOneOf}\n */\n\n/**\n * Converts this namespace to a namespace descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {INamespace} Namespace descriptor\n */\nNamespace.prototype.toJSON = function toJSON(toJSONOptions) {\n    return util.toObject([\n        \"options\" , this.options,\n        \"nested\"  , arrayToJSON(this.nestedArray, toJSONOptions)\n    ]);\n};\n\n/**\n * Adds nested objects to this namespace from nested object descriptors.\n * @param {Object.<string,AnyNestedObject>} nestedJson Any nested object descriptors\n * @returns {Namespace} `this`\n */\nNamespace.prototype.addJSON = function addJSON(nestedJson) {\n    var ns = this;\n    /* istanbul ignore else */\n    if (nestedJson) {\n        for (var names = Object.keys(nestedJson), i = 0, nested; i < names.length; ++i) {\n            nested = nestedJson[names[i]];\n            ns.add( // most to least likely\n                ( nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : nested.id !== undefined\n                ? Field.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    }\n    return this;\n};\n\n/**\n * Gets the nested object of the specified name.\n * @param {string} name Nested object name\n * @returns {ReflectionObject|null} The reflection object or `null` if it doesn't exist\n */\nNamespace.prototype.get = function get(name) {\n    return this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Gets the values of the nested {@link Enum|enum} of the specified name.\n * This methods differs from {@link Namespace#get|get} in that it returns an enum's values directly and throws instead of returning `null`.\n * @param {string} name Nested enum name\n * @returns {Object.<string,number>} Enum values\n * @throws {Error} If there is no such enum\n */\nNamespace.prototype.getEnum = function getEnum(name) {\n    if (this.nested && this.nested[name] instanceof Enum)\n        return this.nested[name].values;\n    throw Error(\"no such enum: \" + name);\n};\n\n/**\n * Adds a nested object to this namespace.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name\n */\nNamespace.prototype.add = function add(object) {\n\n    if (!(object instanceof Field && object.extend !== undefined || object instanceof Type  || object instanceof OneOf || object instanceof Enum || object instanceof Service || object instanceof Namespace))\n        throw TypeError(\"object must be a valid nested object\");\n\n    if (!this.nested)\n        this.nested = {};\n    else {\n        var prev = this.get(object.name);\n        if (prev) {\n            if (prev instanceof Namespace && object instanceof Namespace && !(prev instanceof Type || prev instanceof Service)) {\n                // replace plain namespace but keep existing nested elements and options\n                var nested = prev.nestedArray;\n                for (var i = 0; i < nested.length; ++i)\n                    object.add(nested[i]);\n                this.remove(prev);\n                if (!this.nested)\n                    this.nested = {};\n                object.setOptions(prev.options, true);\n\n            } else\n                throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n        }\n    }\n    this.nested[object.name] = object;\n\n    if (!(this instanceof Type || this instanceof Service || this instanceof Enum || this instanceof Field)) {\n        // This is a package or a root namespace.\n        if (!object._edition) {\n            // Make sure that some edition is set if it hasn't already been specified.\n            object._edition = object._defaultEdition;\n        }\n    }\n\n    this._needsRecursiveFeatureResolution = true;\n    this._needsRecursiveResolve = true;\n\n    // Also clear parent caches, since they need to recurse down.\n    var parent = this;\n    while(parent = parent.parent) {\n        parent._needsRecursiveFeatureResolution = true;\n        parent._needsRecursiveResolve = true;\n    }\n\n    object.onAdd(this);\n    return clearCache(this);\n};\n\n/**\n * Removes a nested object from this namespace.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this namespace\n */\nNamespace.prototype.remove = function remove(object) {\n\n    if (!(object instanceof ReflectionObject))\n        throw TypeError(\"object must be a ReflectionObject\");\n    if (object.parent !== this)\n        throw Error(object + \" is not a member of \" + this);\n\n    delete this.nested[object.name];\n    if (!Object.keys(this.nested).length)\n        this.nested = undefined;\n\n    object.onRemove(this);\n    return clearCache(this);\n};\n\n/**\n * Defines additial namespaces within this one if not yet existing.\n * @param {string|string[]} path Path to create\n * @param {*} [json] Nested types to create from JSON\n * @returns {Namespace} Pointer to the last namespace created or `this` if path is empty\n */\nNamespace.prototype.define = function define(path, json) {\n\n    if (util.isString(path))\n        path = path.split(\".\");\n    else if (!Array.isArray(path))\n        throw TypeError(\"illegal path\");\n    if (path && path.length && path[0] === \"\")\n        throw Error(\"path must be relative\");\n\n    var ptr = this;\n    while (path.length > 0) {\n        var part = path.shift();\n        if (ptr.nested && ptr.nested[part]) {\n            ptr = ptr.nested[part];\n            if (!(ptr instanceof Namespace))\n                throw Error(\"path conflicts with non-namespace objects\");\n        } else\n            ptr.add(ptr = new Namespace(part));\n    }\n    if (json)\n        ptr.addJSON(json);\n    return ptr;\n};\n\n/**\n * Resolves this namespace's and all its nested objects' type references. Useful to validate a reflection tree, but comes at a cost.\n * @returns {Namespace} `this`\n */\nNamespace.prototype.resolveAll = function resolveAll() {\n    if (!this._needsRecursiveResolve) return this;\n\n    this._resolveFeaturesRecursive(this._edition);\n\n    var nested = this.nestedArray, i = 0;\n    this.resolve();\n    while (i < nested.length)\n        if (nested[i] instanceof Namespace)\n            nested[i++].resolveAll();\n        else\n            nested[i++].resolve();\n    this._needsRecursiveResolve = false;\n    return this;\n};\n\n/**\n * @override\n */\nNamespace.prototype._resolveFeaturesRecursive = function _resolveFeaturesRecursive(edition) {\n    if (!this._needsRecursiveFeatureResolution) return this;\n    this._needsRecursiveFeatureResolution = false;\n\n    edition = this._edition || edition;\n\n    ReflectionObject.prototype._resolveFeaturesRecursive.call(this, edition);\n    this.nestedArray.forEach(nested => {\n        nested._resolveFeaturesRecursive(edition);\n    });\n    return this;\n};\n\n/**\n * Recursively looks up the reflection object matching the specified path in the scope of this namespace.\n * @param {string|string[]} path Path to look up\n * @param {*|Array.<*>} filterTypes Filter types, any combination of the constructors of `protobuf.Type`, `protobuf.Enum`, `protobuf.Service` etc.\n * @param {boolean} [parentAlreadyChecked=false] If known, whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n */\nNamespace.prototype.lookup = function lookup(path, filterTypes, parentAlreadyChecked) {\n    /* istanbul ignore next */\n    if (typeof filterTypes === \"boolean\") {\n        parentAlreadyChecked = filterTypes;\n        filterTypes = undefined;\n    } else if (filterTypes && !Array.isArray(filterTypes))\n        filterTypes = [ filterTypes ];\n\n    if (util.isString(path) && path.length) {\n        if (path === \".\")\n            return this.root;\n        path = path.split(\".\");\n    } else if (!path.length)\n        return this;\n\n    var flatPath = path.join(\".\");\n\n    // Start at root if path is absolute\n    if (path[0] === \"\")\n        return this.root.lookup(path.slice(1), filterTypes);\n\n    // Early bailout for objects with matching absolute paths\n    var found = this.root._fullyQualifiedObjects && this.root._fullyQualifiedObjects[\".\" + flatPath];\n    if (found && (!filterTypes || filterTypes.indexOf(found.constructor) > -1)) {\n        return found;\n    }\n\n    // Do a regular lookup at this namespace and below\n    found = this._lookupImpl(path, flatPath);\n    if (found && (!filterTypes || filterTypes.indexOf(found.constructor) > -1)) {\n        return found;\n    }\n\n    if (parentAlreadyChecked)\n        return null;\n\n    // If there hasn't been a match, walk up the tree and look more broadly\n    var current = this;\n    while (current.parent) {\n        found = current.parent._lookupImpl(path, flatPath);\n        if (found && (!filterTypes || filterTypes.indexOf(found.constructor) > -1)) {\n            return found;\n        }\n        current = current.parent;\n    }\n    return null;\n};\n\n/**\n * Internal helper for lookup that handles searching just at this namespace and below along with caching.\n * @param {string[]} path Path to look up\n * @param {string} flatPath Flattened version of the path to use as a cache key\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n * @private\n */\nNamespace.prototype._lookupImpl = function lookup(path, flatPath) {\n    if(Object.prototype.hasOwnProperty.call(this._lookupCache, flatPath)) {\n        return this._lookupCache[flatPath];\n    }\n\n    // Test if the first part matches any nested object, and if so, traverse if path contains more\n    var found = this.get(path[0]);\n    var exact = null;\n    if (found) {\n        if (path.length === 1) {\n            exact = found;\n        } else if (found instanceof Namespace) {\n            path = path.slice(1);\n            exact = found._lookupImpl(path, path.join(\".\"));\n        }\n\n    // Otherwise try each nested namespace\n    } else {\n        for (var i = 0; i < this.nestedArray.length; ++i)\n            if (this._nestedArray[i] instanceof Namespace && (found = this._nestedArray[i]._lookupImpl(path, flatPath)))\n                exact = found;\n    }\n\n    // Set this even when null, so that when we walk up the tree we can quickly bail on repeated checks back down.\n    this._lookupCache[flatPath] = exact;\n    return exact;\n};\n\n/**\n * Looks up the reflection object at the specified path, relative to this namespace.\n * @name NamespaceBase#lookup\n * @function\n * @param {string|string[]} path Path to look up\n * @param {boolean} [parentAlreadyChecked=false] Whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n * @variation 2\n */\n// lookup(path: string, [parentAlreadyChecked: boolean])\n\n/**\n * Looks up the {@link Type|type} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type\n * @throws {Error} If `path` does not point to a type\n */\nNamespace.prototype.lookupType = function lookupType(path) {\n    var found = this.lookup(path, [ Type ]);\n    if (!found)\n        throw Error(\"no such type: \" + path);\n    return found;\n};\n\n/**\n * Looks up the values of the {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Enum} Looked up enum\n * @throws {Error} If `path` does not point to an enum\n */\nNamespace.prototype.lookupEnum = function lookupEnum(path) {\n    var found = this.lookup(path, [ Enum ]);\n    if (!found)\n        throw Error(\"no such Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Type|type} or {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type or enum\n * @throws {Error} If `path` does not point to a type or enum\n */\nNamespace.prototype.lookupTypeOrEnum = function lookupTypeOrEnum(path) {\n    var found = this.lookup(path, [ Type, Enum ]);\n    if (!found)\n        throw Error(\"no such Type or Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Service|service} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Service} Looked up service\n * @throws {Error} If `path` does not point to a service\n */\nNamespace.prototype.lookupService = function lookupService(path) {\n    var found = this.lookup(path, [ Service ]);\n    if (!found)\n        throw Error(\"no such Service '\" + path + \"' in \" + this);\n    return found;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nNamespace._configure = function(Type_, Service_, Enum_) {\n    Type    = Type_;\n    Service = Service_;\n    Enum    = Enum_;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,2BAA2B;AAC3B,IAAI;AACJ,CAAC,CAAC,UAAU,SAAS,GAAG,OAAO,MAAM,CAAC,iBAAiB,SAAS,CAAC,EAAE,WAAW,GAAG,SAAS,EAAE,SAAS,GAAG;AAExG,IAAI,0GACA,wGACA;AAEJ,IAAI,MACA,SACA;AAEJ;;;;;;;;CAQC,GAED;;;;;;;;CAQC,GACD,UAAU,QAAQ,GAAG,SAAS,SAAS,IAAI,EAAE,IAAI;IAC7C,OAAO,IAAI,UAAU,MAAM,KAAK,OAAO,EAAE,OAAO,CAAC,KAAK,MAAM;AAChE;AAEA;;;;;;CAMC,GACD,SAAS,YAAY,KAAK,EAAE,aAAa;IACrC,IAAI,CAAC,CAAC,SAAS,MAAM,MAAM,GACvB,OAAO;IACX,IAAI,MAAM,CAAC;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAChC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC;IACzC,OAAO;AACX;AAEA,UAAU,WAAW,GAAG;AAExB;;;;;CAKC,GACD,UAAU,YAAY,GAAG,SAAS,aAAa,QAAQ,EAAE,EAAE;IACvD,IAAI,UACA;QAAA,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,EAAE,EACnC,IAAI,OAAO,QAAQ,CAAC,EAAE,KAAK,YAAY,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,MAAM,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,IAC5E,OAAO;IAAI;IACvB,OAAO;AACX;AAEA;;;;;CAKC,GACD,UAAU,cAAc,GAAG,SAAS,eAAe,QAAQ,EAAE,IAAI;IAC7D,IAAI,UACA;QAAA,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,EAAE,EACnC,IAAI,QAAQ,CAAC,EAAE,KAAK,MAChB,OAAO;IAAI;IACvB,OAAO;AACX;AAEA;;;;;;;;;;CAUC,GACD,SAAS,UAAU,IAAI,EAAE,OAAO;IAC5B,iBAAiB,IAAI,CAAC,IAAI,EAAE,MAAM;IAElC;;;KAGC,GACD,IAAI,CAAC,MAAM,GAAG,WAAW,SAAS;IAElC;;;;KAIC,GACD,IAAI,CAAC,YAAY,GAAG;IAEpB;;;;;;KAMC,GACD,IAAI,CAAC,YAAY,GAAG,CAAC;IAErB;;;;KAIC,GACD,IAAI,CAAC,gCAAgC,GAAG;IAExC;;;;KAIC,GACD,IAAI,CAAC,sBAAsB,GAAG;AAClC;AAEA,SAAS,WAAW,SAAS;IACzB,UAAU,YAAY,GAAG;IACzB,UAAU,YAAY,GAAG,CAAC;IAE1B,+DAA+D;IAC/D,IAAI,SAAS;IACb,MAAM,SAAS,OAAO,MAAM,CAAE;QAC1B,OAAO,YAAY,GAAG,CAAC;IAC3B;IACA,OAAO;AACX;AAEA;;;;;CAKC,GACD,OAAO,cAAc,CAAC,UAAU,SAAS,EAAE,eAAe;IACtD,KAAK;QACD,OAAO,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;IAC9E;AACJ;AAEA;;;;;CAKC,GAED;;;;CAIC,GAED;;;;CAIC,GAED;;;;CAIC,GACD,UAAU,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,aAAa;IACtD,OAAO,KAAK,QAAQ,CAAC;QACjB;QAAY,IAAI,CAAC,OAAO;QACxB;QAAY,YAAY,IAAI,CAAC,WAAW,EAAE;KAC7C;AACL;AAEA;;;;CAIC,GACD,UAAU,SAAS,CAAC,OAAO,GAAG,SAAS,QAAQ,UAAU;IACrD,IAAI,KAAK,IAAI;IACb,wBAAwB,GACxB,IAAI,YAAY;QACZ,IAAK,IAAI,QAAQ,OAAO,IAAI,CAAC,aAAa,IAAI,GAAG,QAAQ,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;YAC5E,SAAS,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7B,GAAG,GAAG,CACF,CAAE,OAAO,MAAM,KAAK,YAClB,KAAK,QAAQ,GACb,OAAO,MAAM,KAAK,YAClB,KAAK,QAAQ,GACb,OAAO,OAAO,KAAK,YACnB,QAAQ,QAAQ,GAChB,OAAO,EAAE,KAAK,YACd,MAAM,QAAQ,GACd,UAAU,QAAQ,AAAC,EAAE,KAAK,CAAC,EAAE,EAAE;QAEzC;IACJ;IACA,OAAO,IAAI;AACf;AAEA;;;;CAIC,GACD,UAAU,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,IAAI;IACvC,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,IAChC;AACX;AAEA;;;;;;CAMC,GACD,UAAU,SAAS,CAAC,OAAO,GAAG,SAAS,QAAQ,IAAI;IAC/C,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,YAAY,MAC5C,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM;IACnC,MAAM,MAAM,mBAAmB;AACnC;AAEA;;;;;;CAMC,GACD,UAAU,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,MAAM;IAEzC,IAAI,CAAC,CAAC,kBAAkB,SAAS,OAAO,MAAM,KAAK,aAAa,kBAAkB,QAAS,kBAAkB,SAAS,kBAAkB,QAAQ,kBAAkB,WAAW,kBAAkB,SAAS,GACpM,MAAM,UAAU;IAEpB,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,IAAI,CAAC,MAAM,GAAG,CAAC;SACd;QACD,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI;QAC/B,IAAI,MAAM;YACN,IAAI,gBAAgB,aAAa,kBAAkB,aAAa,CAAC,CAAC,gBAAgB,QAAQ,gBAAgB,OAAO,GAAG;gBAChH,wEAAwE;gBACxE,IAAI,SAAS,KAAK,WAAW;gBAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EACjC,OAAO,GAAG,CAAC,MAAM,CAAC,EAAE;gBACxB,IAAI,CAAC,MAAM,CAAC;gBACZ,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,IAAI,CAAC,MAAM,GAAG,CAAC;gBACnB,OAAO,UAAU,CAAC,KAAK,OAAO,EAAE;YAEpC,OACI,MAAM,MAAM,qBAAqB,OAAO,IAAI,GAAG,UAAU,IAAI;QACrE;IACJ;IACA,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,GAAG;IAE3B,IAAI,CAAC,CAAC,IAAI,YAAY,QAAQ,IAAI,YAAY,WAAW,IAAI,YAAY,QAAQ,IAAI,YAAY,KAAK,GAAG;QACrG,yCAAyC;QACzC,IAAI,CAAC,OAAO,QAAQ,EAAE;YAClB,0EAA0E;YAC1E,OAAO,QAAQ,GAAG,OAAO,eAAe;QAC5C;IACJ;IAEA,IAAI,CAAC,gCAAgC,GAAG;IACxC,IAAI,CAAC,sBAAsB,GAAG;IAE9B,6DAA6D;IAC7D,IAAI,SAAS,IAAI;IACjB,MAAM,SAAS,OAAO,MAAM,CAAE;QAC1B,OAAO,gCAAgC,GAAG;QAC1C,OAAO,sBAAsB,GAAG;IACpC;IAEA,OAAO,KAAK,CAAC,IAAI;IACjB,OAAO,WAAW,IAAI;AAC1B;AAEA;;;;;;CAMC,GACD,UAAU,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,MAAM;IAE/C,IAAI,CAAC,CAAC,kBAAkB,gBAAgB,GACpC,MAAM,UAAU;IACpB,IAAI,OAAO,MAAM,KAAK,IAAI,EACtB,MAAM,MAAM,SAAS,yBAAyB,IAAI;IAEtD,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC;IAC/B,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAChC,IAAI,CAAC,MAAM,GAAG;IAElB,OAAO,QAAQ,CAAC,IAAI;IACpB,OAAO,WAAW,IAAI;AAC1B;AAEA;;;;;CAKC,GACD,UAAU,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,IAAI,EAAE,IAAI;IAEnD,IAAI,KAAK,QAAQ,CAAC,OACd,OAAO,KAAK,KAAK,CAAC;SACjB,IAAI,CAAC,MAAM,OAAO,CAAC,OACpB,MAAM,UAAU;IACpB,IAAI,QAAQ,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE,KAAK,IACnC,MAAM,MAAM;IAEhB,IAAI,MAAM,IAAI;IACd,MAAO,KAAK,MAAM,GAAG,EAAG;QACpB,IAAI,OAAO,KAAK,KAAK;QACrB,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE;YAChC,MAAM,IAAI,MAAM,CAAC,KAAK;YACtB,IAAI,CAAC,CAAC,eAAe,SAAS,GAC1B,MAAM,MAAM;QACpB,OACI,IAAI,GAAG,CAAC,MAAM,IAAI,UAAU;IACpC;IACA,IAAI,MACA,IAAI,OAAO,CAAC;IAChB,OAAO;AACX;AAEA;;;CAGC,GACD,UAAU,SAAS,CAAC,UAAU,GAAG,SAAS;IACtC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,OAAO,IAAI;IAE7C,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ;IAE5C,IAAI,SAAS,IAAI,CAAC,WAAW,EAAE,IAAI;IACnC,IAAI,CAAC,OAAO;IACZ,MAAO,IAAI,OAAO,MAAM,CACpB,IAAI,MAAM,CAAC,EAAE,YAAY,WACrB,MAAM,CAAC,IAAI,CAAC,UAAU;SAEtB,MAAM,CAAC,IAAI,CAAC,OAAO;IAC3B,IAAI,CAAC,sBAAsB,GAAG;IAC9B,OAAO,IAAI;AACf;AAEA;;CAEC,GACD,UAAU,SAAS,CAAC,yBAAyB,GAAG,SAAS,0BAA0B,OAAO;IACtF,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE,OAAO,IAAI;IACvD,IAAI,CAAC,gCAAgC,GAAG;IAExC,UAAU,IAAI,CAAC,QAAQ,IAAI;IAE3B,iBAAiB,SAAS,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE;IAChE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;QACrB,OAAO,yBAAyB,CAAC;IACrC;IACA,OAAO,IAAI;AACf;AAEA;;;;;;CAMC,GACD,UAAU,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,IAAI,EAAE,WAAW,EAAE,oBAAoB;IAChF,wBAAwB,GACxB,IAAI,OAAO,gBAAgB,WAAW;QAClC,uBAAuB;QACvB,cAAc;IAClB,OAAO,IAAI,eAAe,CAAC,MAAM,OAAO,CAAC,cACrC,cAAc;QAAE;KAAa;IAEjC,IAAI,KAAK,QAAQ,CAAC,SAAS,KAAK,MAAM,EAAE;QACpC,IAAI,SAAS,KACT,OAAO,IAAI,CAAC,IAAI;QACpB,OAAO,KAAK,KAAK,CAAC;IACtB,OAAO,IAAI,CAAC,KAAK,MAAM,EACnB,OAAO,IAAI;IAEf,IAAI,WAAW,KAAK,IAAI,CAAC;IAEzB,oCAAoC;IACpC,IAAI,IAAI,CAAC,EAAE,KAAK,IACZ,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,IAAI;IAE3C,yDAAyD;IACzD,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,SAAS;IAChG,IAAI,SAAS,CAAC,CAAC,eAAe,YAAY,OAAO,CAAC,MAAM,WAAW,IAAI,CAAC,CAAC,GAAG;QACxE,OAAO;IACX;IAEA,kDAAkD;IAClD,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM;IAC/B,IAAI,SAAS,CAAC,CAAC,eAAe,YAAY,OAAO,CAAC,MAAM,WAAW,IAAI,CAAC,CAAC,GAAG;QACxE,OAAO;IACX;IAEA,IAAI,sBACA,OAAO;IAEX,uEAAuE;IACvE,IAAI,UAAU,IAAI;IAClB,MAAO,QAAQ,MAAM,CAAE;QACnB,QAAQ,QAAQ,MAAM,CAAC,WAAW,CAAC,MAAM;QACzC,IAAI,SAAS,CAAC,CAAC,eAAe,YAAY,OAAO,CAAC,MAAM,WAAW,IAAI,CAAC,CAAC,GAAG;YACxE,OAAO;QACX;QACA,UAAU,QAAQ,MAAM;IAC5B;IACA,OAAO;AACX;AAEA;;;;;;CAMC,GACD,UAAU,SAAS,CAAC,WAAW,GAAG,SAAS,OAAO,IAAI,EAAE,QAAQ;IAC5D,IAAG,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW;QAClE,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS;IACtC;IAEA,8FAA8F;IAC9F,IAAI,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;IAC5B,IAAI,QAAQ;IACZ,IAAI,OAAO;QACP,IAAI,KAAK,MAAM,KAAK,GAAG;YACnB,QAAQ;QACZ,OAAO,IAAI,iBAAiB,WAAW;YACnC,OAAO,KAAK,KAAK,CAAC;YAClB,QAAQ,MAAM,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC;QAC9C;IAEJ,sCAAsC;IACtC,OAAO;QACH,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,EAC3C,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,YAAY,aAAa,CAAC,QAAQ,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,SAAS,GACtG,QAAQ;IACpB;IAEA,8GAA8G;IAC9G,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG;IAC9B,OAAO;AACX;AAEA;;;;;;;;CAQC,GACD,wDAAwD;AAExD;;;;;;CAMC,GACD,UAAU,SAAS,CAAC,UAAU,GAAG,SAAS,WAAW,IAAI;IACrD,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM;QAAE;KAAM;IACtC,IAAI,CAAC,OACD,MAAM,MAAM,mBAAmB;IACnC,OAAO;AACX;AAEA;;;;;;CAMC,GACD,UAAU,SAAS,CAAC,UAAU,GAAG,SAAS,WAAW,IAAI;IACrD,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM;QAAE;KAAM;IACtC,IAAI,CAAC,OACD,MAAM,MAAM,mBAAmB,OAAO,UAAU,IAAI;IACxD,OAAO;AACX;AAEA;;;;;;CAMC,GACD,UAAU,SAAS,CAAC,gBAAgB,GAAG,SAAS,iBAAiB,IAAI;IACjE,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM;QAAE;QAAM;KAAM;IAC5C,IAAI,CAAC,OACD,MAAM,MAAM,2BAA2B,OAAO,UAAU,IAAI;IAChE,OAAO;AACX;AAEA;;;;;;CAMC,GACD,UAAU,SAAS,CAAC,aAAa,GAAG,SAAS,cAAc,IAAI;IAC3D,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM;QAAE;KAAS;IACzC,IAAI,CAAC,OACD,MAAM,MAAM,sBAAsB,OAAO,UAAU,IAAI;IAC3D,OAAO;AACX;AAEA,sDAAsD;AACtD,UAAU,UAAU,GAAG,SAAS,KAAK,EAAE,QAAQ,EAAE,KAAK;IAClD,OAAU;IACV,UAAU;IACV,OAAU;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/mapfield.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = MapField;\n\n// extends Field\nvar Field = require(\"./field\");\n((MapField.prototype = Object.create(Field.prototype)).constructor = MapField).className = \"MapField\";\n\nvar types   = require(\"./types\"),\n    util    = require(\"./util\");\n\n/**\n * Constructs a new map field instance.\n * @classdesc Reflected map field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} keyType Key type\n * @param {string} type Value type\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction MapField(name, id, keyType, type, options, comment) {\n    Field.call(this, name, id, type, undefined, undefined, options, comment);\n\n    /* istanbul ignore if */\n    if (!util.isString(keyType))\n        throw TypeError(\"keyType must be a string\");\n\n    /**\n     * Key type.\n     * @type {string}\n     */\n    this.keyType = keyType; // toJSON, marker\n\n    /**\n     * Resolved key type if not a basic type.\n     * @type {ReflectionObject|null}\n     */\n    this.resolvedKeyType = null;\n\n    // Overrides Field#map\n    this.map = true;\n}\n\n/**\n * Map field descriptor.\n * @interface IMapField\n * @extends {IField}\n * @property {string} keyType Key type\n */\n\n/**\n * Extension map field descriptor.\n * @interface IExtensionMapField\n * @extends IMapField\n * @property {string} extend Extended type\n */\n\n/**\n * Constructs a map field from a map field descriptor.\n * @param {string} name Field name\n * @param {IMapField} json Map field descriptor\n * @returns {MapField} Created map field\n * @throws {TypeError} If arguments are invalid\n */\nMapField.fromJSON = function fromJSON(name, json) {\n    return new MapField(name, json.id, json.keyType, json.type, json.options, json.comment);\n};\n\n/**\n * Converts this map field to a map field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMapField} Map field descriptor\n */\nMapField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"keyType\" , this.keyType,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nMapField.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n\n    // Besides a value type, map fields have a key type that may be \"any scalar type except for floating point types and bytes\"\n    if (types.mapKey[this.keyType] === undefined)\n        throw Error(\"invalid key type: \" + this.keyType);\n\n    return Field.prototype.resolve.call(this);\n};\n\n/**\n * Map field decorator (TypeScript).\n * @name MapField.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"} fieldKeyType Field key type\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"|\"bytes\"|Object|Constructor<{}>} fieldValueType Field value type\n * @returns {FieldDecorator} Decorator function\n * @template T extends { [key: string]: number | Long | string | boolean | Uint8Array | Buffer | number[] | Message<{}> }\n */\nMapField.d = function decorateMapField(fieldId, fieldKeyType, fieldValueType) {\n\n    // submessage value: decorate the submessage and use its name as the type\n    if (typeof fieldValueType === \"function\")\n        fieldValueType = util.decorateType(fieldValueType).name;\n\n    // enum reference value: create a reflected copy of the enum and keep reuseing it\n    else if (fieldValueType && typeof fieldValueType === \"object\")\n        fieldValueType = util.decorateEnum(fieldValueType).name;\n\n    return function mapFieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new MapField(fieldName, fieldId, fieldKeyType, fieldValueType));\n    };\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,gBAAgB;AAChB,IAAI;AACJ,CAAC,CAAC,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,MAAM,SAAS,CAAC,EAAE,WAAW,GAAG,QAAQ,EAAE,SAAS,GAAG;AAE3F,IAAI,0GACA;AAEJ;;;;;;;;;;;CAWC,GACD,SAAS,SAAS,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO;IACvD,MAAM,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,MAAM,WAAW,WAAW,SAAS;IAEhE,sBAAsB,GACtB,IAAI,CAAC,KAAK,QAAQ,CAAC,UACf,MAAM,UAAU;IAEpB;;;KAGC,GACD,IAAI,CAAC,OAAO,GAAG,SAAS,iBAAiB;IAEzC;;;KAGC,GACD,IAAI,CAAC,eAAe,GAAG;IAEvB,sBAAsB;IACtB,IAAI,CAAC,GAAG,GAAG;AACf;AAEA;;;;;CAKC,GAED;;;;;CAKC,GAED;;;;;;CAMC,GACD,SAAS,QAAQ,GAAG,SAAS,SAAS,IAAI,EAAE,IAAI;IAC5C,OAAO,IAAI,SAAS,MAAM,KAAK,EAAE,EAAE,KAAK,OAAO,EAAE,KAAK,IAAI,EAAE,KAAK,OAAO,EAAE,KAAK,OAAO;AAC1F;AAEA;;;;CAIC,GACD,SAAS,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,aAAa;IACrD,IAAI,eAAe,gBAAgB,QAAQ,cAAc,YAAY,IAAI;IACzE,OAAO,KAAK,QAAQ,CAAC;QACjB;QAAY,IAAI,CAAC,OAAO;QACxB;QAAY,IAAI,CAAC,IAAI;QACrB;QAAY,IAAI,CAAC,EAAE;QACnB;QAAY,IAAI,CAAC,MAAM;QACvB;QAAY,IAAI,CAAC,OAAO;QACxB;QAAY,eAAe,IAAI,CAAC,OAAO,GAAG;KAC7C;AACL;AAEA;;CAEC,GACD,SAAS,SAAS,CAAC,OAAO,GAAG,SAAS;IAClC,IAAI,IAAI,CAAC,QAAQ,EACb,OAAO,IAAI;IAEf,2HAA2H;IAC3H,IAAI,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,WAC/B,MAAM,MAAM,uBAAuB,IAAI,CAAC,OAAO;IAEnD,OAAO,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;AAC5C;AAEA;;;;;;;;;CASC,GACD,SAAS,CAAC,GAAG,SAAS,iBAAiB,OAAO,EAAE,YAAY,EAAE,cAAc;IAExE,yEAAyE;IACzE,IAAI,OAAO,mBAAmB,YAC1B,iBAAiB,KAAK,YAAY,CAAC,gBAAgB,IAAI;SAGtD,IAAI,kBAAkB,OAAO,mBAAmB,UACjD,iBAAiB,KAAK,YAAY,CAAC,gBAAgB,IAAI;IAE3D,OAAO,SAAS,kBAAkB,SAAS,EAAE,SAAS;QAClD,KAAK,YAAY,CAAC,UAAU,WAAW,EAClC,GAAG,CAAC,IAAI,SAAS,WAAW,SAAS,cAAc;IAC5D;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1014, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/method.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = Method;\n\n// extends ReflectionObject\nvar ReflectionObject = require(\"./object\");\n((Method.prototype = Object.create(ReflectionObject.prototype)).constructor = Method).className = \"Method\";\n\nvar util = require(\"./util\");\n\n/**\n * Constructs a new service method instance.\n * @classdesc Reflected service method.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Method name\n * @param {string|undefined} type Method type, usually `\"rpc\"`\n * @param {string} requestType Request message type\n * @param {string} responseType Response message type\n * @param {boolean|Object.<string,*>} [requestStream] Whether the request is streamed\n * @param {boolean|Object.<string,*>} [responseStream] Whether the response is streamed\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this method\n * @param {Object.<string,*>} [parsedOptions] Declared options, properly parsed into an object\n */\nfunction Method(name, type, requestType, responseType, requestStream, responseStream, options, comment, parsedOptions) {\n\n    /* istanbul ignore next */\n    if (util.isObject(requestStream)) {\n        options = requestStream;\n        requestStream = responseStream = undefined;\n    } else if (util.isObject(responseStream)) {\n        options = responseStream;\n        responseStream = undefined;\n    }\n\n    /* istanbul ignore if */\n    if (!(type === undefined || util.isString(type)))\n        throw TypeError(\"type must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(requestType))\n        throw TypeError(\"requestType must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(responseType))\n        throw TypeError(\"responseType must be a string\");\n\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Method type.\n     * @type {string}\n     */\n    this.type = type || \"rpc\"; // toJSON\n\n    /**\n     * Request type.\n     * @type {string}\n     */\n    this.requestType = requestType; // toJSON, marker\n\n    /**\n     * Whether requests are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.requestStream = requestStream ? true : undefined; // toJSON\n\n    /**\n     * Response type.\n     * @type {string}\n     */\n    this.responseType = responseType; // toJSON\n\n    /**\n     * Whether responses are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.responseStream = responseStream ? true : undefined; // toJSON\n\n    /**\n     * Resolved request type.\n     * @type {Type|null}\n     */\n    this.resolvedRequestType = null;\n\n    /**\n     * Resolved response type.\n     * @type {Type|null}\n     */\n    this.resolvedResponseType = null;\n\n    /**\n     * Comment for this method\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Options properly parsed into an object\n     */\n    this.parsedOptions = parsedOptions;\n}\n\n/**\n * Method descriptor.\n * @interface IMethod\n * @property {string} [type=\"rpc\"] Method type\n * @property {string} requestType Request type\n * @property {string} responseType Response type\n * @property {boolean} [requestStream=false] Whether requests are streamed\n * @property {boolean} [responseStream=false] Whether responses are streamed\n * @property {Object.<string,*>} [options] Method options\n * @property {string} comment Method comments\n * @property {Object.<string,*>} [parsedOptions] Method options properly parsed into an object\n */\n\n/**\n * Constructs a method from a method descriptor.\n * @param {string} name Method name\n * @param {IMethod} json Method descriptor\n * @returns {Method} Created method\n * @throws {TypeError} If arguments are invalid\n */\nMethod.fromJSON = function fromJSON(name, json) {\n    return new Method(name, json.type, json.requestType, json.responseType, json.requestStream, json.responseStream, json.options, json.comment, json.parsedOptions);\n};\n\n/**\n * Converts this method to a method descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMethod} Method descriptor\n */\nMethod.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"type\"           , this.type !== \"rpc\" && /* istanbul ignore next */ this.type || undefined,\n        \"requestType\"    , this.requestType,\n        \"requestStream\"  , this.requestStream,\n        \"responseType\"   , this.responseType,\n        \"responseStream\" , this.responseStream,\n        \"options\"        , this.options,\n        \"comment\"        , keepComments ? this.comment : undefined,\n        \"parsedOptions\"  , this.parsedOptions,\n    ]);\n};\n\n/**\n * @override\n */\nMethod.prototype.resolve = function resolve() {\n\n    /* istanbul ignore if */\n    if (this.resolved)\n        return this;\n\n    this.resolvedRequestType = this.parent.lookupType(this.requestType);\n    this.resolvedResponseType = this.parent.lookupType(this.responseType);\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,2BAA2B;AAC3B,IAAI;AACJ,CAAC,CAAC,OAAO,SAAS,GAAG,OAAO,MAAM,CAAC,iBAAiB,SAAS,CAAC,EAAE,WAAW,GAAG,MAAM,EAAE,SAAS,GAAG;AAElG,IAAI;AAEJ;;;;;;;;;;;;;;CAcC,GACD,SAAS,OAAO,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,aAAa;IAEjH,wBAAwB,GACxB,IAAI,KAAK,QAAQ,CAAC,gBAAgB;QAC9B,UAAU;QACV,gBAAgB,iBAAiB;IACrC,OAAO,IAAI,KAAK,QAAQ,CAAC,iBAAiB;QACtC,UAAU;QACV,iBAAiB;IACrB;IAEA,sBAAsB,GACtB,IAAI,CAAC,CAAC,SAAS,aAAa,KAAK,QAAQ,CAAC,KAAK,GAC3C,MAAM,UAAU;IAEpB,sBAAsB,GACtB,IAAI,CAAC,KAAK,QAAQ,CAAC,cACf,MAAM,UAAU;IAEpB,sBAAsB,GACtB,IAAI,CAAC,KAAK,QAAQ,CAAC,eACf,MAAM,UAAU;IAEpB,iBAAiB,IAAI,CAAC,IAAI,EAAE,MAAM;IAElC;;;KAGC,GACD,IAAI,CAAC,IAAI,GAAG,QAAQ,OAAO,SAAS;IAEpC;;;KAGC,GACD,IAAI,CAAC,WAAW,GAAG,aAAa,iBAAiB;IAEjD;;;KAGC,GACD,IAAI,CAAC,aAAa,GAAG,gBAAgB,OAAO,WAAW,SAAS;IAEhE;;;KAGC,GACD,IAAI,CAAC,YAAY,GAAG,cAAc,SAAS;IAE3C;;;KAGC,GACD,IAAI,CAAC,cAAc,GAAG,iBAAiB,OAAO,WAAW,SAAS;IAElE;;;KAGC,GACD,IAAI,CAAC,mBAAmB,GAAG;IAE3B;;;KAGC,GACD,IAAI,CAAC,oBAAoB,GAAG;IAE5B;;;KAGC,GACD,IAAI,CAAC,OAAO,GAAG;IAEf;;KAEC,GACD,IAAI,CAAC,aAAa,GAAG;AACzB;AAEA;;;;;;;;;;;CAWC,GAED;;;;;;CAMC,GACD,OAAO,QAAQ,GAAG,SAAS,SAAS,IAAI,EAAE,IAAI;IAC1C,OAAO,IAAI,OAAO,MAAM,KAAK,IAAI,EAAE,KAAK,WAAW,EAAE,KAAK,YAAY,EAAE,KAAK,aAAa,EAAE,KAAK,cAAc,EAAE,KAAK,OAAO,EAAE,KAAK,OAAO,EAAE,KAAK,aAAa;AACnK;AAEA;;;;CAIC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,aAAa;IACnD,IAAI,eAAe,gBAAgB,QAAQ,cAAc,YAAY,IAAI;IACzE,OAAO,KAAK,QAAQ,CAAC;QACjB;QAAmB,IAAI,CAAC,IAAI,KAAK,SAAS,wBAAwB,GAAG,IAAI,CAAC,IAAI,IAAI;QAClF;QAAmB,IAAI,CAAC,WAAW;QACnC;QAAmB,IAAI,CAAC,aAAa;QACrC;QAAmB,IAAI,CAAC,YAAY;QACpC;QAAmB,IAAI,CAAC,cAAc;QACtC;QAAmB,IAAI,CAAC,OAAO;QAC/B;QAAmB,eAAe,IAAI,CAAC,OAAO,GAAG;QACjD;QAAmB,IAAI,CAAC,aAAa;KACxC;AACL;AAEA;;CAEC,GACD,OAAO,SAAS,CAAC,OAAO,GAAG,SAAS;IAEhC,sBAAsB,GACtB,IAAI,IAAI,CAAC,QAAQ,EACb,OAAO,IAAI;IAEf,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW;IAClE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY;IAEpE,OAAO,iBAAiB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1140, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/rpc/service.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = Service;\n\nvar util = require(\"../util/minimal\");\n\n// Extends EventEmitter\n(Service.prototype = Object.create(util.EventEmitter.prototype)).constructor = Service;\n\n/**\n * A service method callback as used by {@link rpc.ServiceMethod|ServiceMethod}.\n *\n * Differs from {@link RPCImplCallback} in that it is an actual callback of a service method which may not return `response = null`.\n * @typedef rpc.ServiceMethodCallback\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {Error|null} error Error, if any\n * @param {TRes} [response] Response message\n * @returns {undefined}\n */\n\n/**\n * A service method part of a {@link rpc.Service} as created by {@link Service.create}.\n * @typedef rpc.ServiceMethod\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} [callback] Node-style callback called with the error, if any, and the response message\n * @returns {Promise<Message<TRes>>} Promise if `callback` has been omitted, otherwise `undefined`\n */\n\n/**\n * Constructs a new RPC service instance.\n * @classdesc An RPC service as returned by {@link Service#create}.\n * @exports rpc.Service\n * @extends util.EventEmitter\n * @constructor\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n */\nfunction Service(rpcImpl, requestDelimited, responseDelimited) {\n\n    if (typeof rpcImpl !== \"function\")\n        throw TypeError(\"rpcImpl must be a function\");\n\n    util.EventEmitter.call(this);\n\n    /**\n     * RPC implementation. Becomes `null` once the service is ended.\n     * @type {RPCImpl|null}\n     */\n    this.rpcImpl = rpcImpl;\n\n    /**\n     * Whether requests are length-delimited.\n     * @type {boolean}\n     */\n    this.requestDelimited = Boolean(requestDelimited);\n\n    /**\n     * Whether responses are length-delimited.\n     * @type {boolean}\n     */\n    this.responseDelimited = Boolean(responseDelimited);\n}\n\n/**\n * Calls a service method through {@link rpc.Service#rpcImpl|rpcImpl}.\n * @param {Method|rpc.ServiceMethod<TReq,TRes>} method Reflected or static method\n * @param {Constructor<TReq>} requestCtor Request constructor\n * @param {Constructor<TRes>} responseCtor Response constructor\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} callback Service callback\n * @returns {undefined}\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n */\nService.prototype.rpcCall = function rpcCall(method, requestCtor, responseCtor, request, callback) {\n\n    if (!request)\n        throw TypeError(\"request must be specified\");\n\n    var self = this;\n    if (!callback)\n        return util.asPromise(rpcCall, self, method, requestCtor, responseCtor, request);\n\n    if (!self.rpcImpl) {\n        setTimeout(function() { callback(Error(\"already ended\")); }, 0);\n        return undefined;\n    }\n\n    try {\n        return self.rpcImpl(\n            method,\n            requestCtor[self.requestDelimited ? \"encodeDelimited\" : \"encode\"](request).finish(),\n            function rpcCallback(err, response) {\n\n                if (err) {\n                    self.emit(\"error\", err, method);\n                    return callback(err);\n                }\n\n                if (response === null) {\n                    self.end(/* endedByRPC */ true);\n                    return undefined;\n                }\n\n                if (!(response instanceof responseCtor)) {\n                    try {\n                        response = responseCtor[self.responseDelimited ? \"decodeDelimited\" : \"decode\"](response);\n                    } catch (err) {\n                        self.emit(\"error\", err, method);\n                        return callback(err);\n                    }\n                }\n\n                self.emit(\"data\", response, method);\n                return callback(null, response);\n            }\n        );\n    } catch (err) {\n        self.emit(\"error\", err, method);\n        setTimeout(function() { callback(err); }, 0);\n        return undefined;\n    }\n};\n\n/**\n * Ends this service and emits the `end` event.\n * @param {boolean} [endedByRPC=false] Whether the service has been ended by the RPC implementation.\n * @returns {rpc.Service} `this`\n */\nService.prototype.end = function end(endedByRPC) {\n    if (this.rpcImpl) {\n        if (!endedByRPC) // signal end to rpcImpl\n            this.rpcImpl(null, null, null);\n        this.rpcImpl = null;\n        this.emit(\"end\").off();\n    }\n    return this;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,IAAI;AAEJ,uBAAuB;AACvB,CAAC,QAAQ,SAAS,GAAG,OAAO,MAAM,CAAC,KAAK,YAAY,CAAC,SAAS,CAAC,EAAE,WAAW,GAAG;AAE/E;;;;;;;;;;CAUC,GAED;;;;;;;;;CASC,GAED;;;;;;;;;CASC,GACD,SAAS,QAAQ,OAAO,EAAE,gBAAgB,EAAE,iBAAiB;IAEzD,IAAI,OAAO,YAAY,YACnB,MAAM,UAAU;IAEpB,KAAK,YAAY,CAAC,IAAI,CAAC,IAAI;IAE3B;;;KAGC,GACD,IAAI,CAAC,OAAO,GAAG;IAEf;;;KAGC,GACD,IAAI,CAAC,gBAAgB,GAAG,QAAQ;IAEhC;;;KAGC,GACD,IAAI,CAAC,iBAAiB,GAAG,QAAQ;AACrC;AAEA;;;;;;;;;;CAUC,GACD,QAAQ,SAAS,CAAC,OAAO,GAAG,SAAS,QAAQ,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ;IAE7F,IAAI,CAAC,SACD,MAAM,UAAU;IAEpB,IAAI,OAAO,IAAI;IACf,IAAI,CAAC,UACD,OAAO,KAAK,SAAS,CAAC,SAAS,MAAM,QAAQ,aAAa,cAAc;IAE5E,IAAI,CAAC,KAAK,OAAO,EAAE;QACf,WAAW;YAAa,SAAS,MAAM;QAAmB,GAAG;QAC7D,OAAO;IACX;IAEA,IAAI;QACA,OAAO,KAAK,OAAO,CACf,QACA,WAAW,CAAC,KAAK,gBAAgB,GAAG,oBAAoB,SAAS,CAAC,SAAS,MAAM,IACjF,SAAS,YAAY,GAAG,EAAE,QAAQ;YAE9B,IAAI,KAAK;gBACL,KAAK,IAAI,CAAC,SAAS,KAAK;gBACxB,OAAO,SAAS;YACpB;YAEA,IAAI,aAAa,MAAM;gBACnB,KAAK,GAAG,CAAC,cAAc,GAAG;gBAC1B,OAAO;YACX;YAEA,IAAI,CAAC,CAAC,oBAAoB,YAAY,GAAG;gBACrC,IAAI;oBACA,WAAW,YAAY,CAAC,KAAK,iBAAiB,GAAG,oBAAoB,SAAS,CAAC;gBACnF,EAAE,OAAO,KAAK;oBACV,KAAK,IAAI,CAAC,SAAS,KAAK;oBACxB,OAAO,SAAS;gBACpB;YACJ;YAEA,KAAK,IAAI,CAAC,QAAQ,UAAU;YAC5B,OAAO,SAAS,MAAM;QAC1B;IAER,EAAE,OAAO,KAAK;QACV,KAAK,IAAI,CAAC,SAAS,KAAK;QACxB,WAAW;YAAa,SAAS;QAAM,GAAG;QAC1C,OAAO;IACX;AACJ;AAEA;;;;CAIC,GACD,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,UAAU;IAC3C,IAAI,IAAI,CAAC,OAAO,EAAE;QACd,IAAI,CAAC,YACD,IAAI,CAAC,OAAO,CAAC,MAAM,MAAM;QAC7B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG;IACxB;IACA,OAAO,IAAI;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1255, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/rpc.js"], "sourcesContent": ["\"use strict\";\n\n/**\n * Streaming RPC helpers.\n * @namespace\n */\nvar rpc = exports;\n\n/**\n * RPC implementation passed to {@link Service#create} performing a service request on network level, i.e. by utilizing http requests or websockets.\n * @typedef RPCImpl\n * @type {function}\n * @param {Method|rpc.ServiceMethod<Message<{}>,Message<{}>>} method Reflected or static method being called\n * @param {Uint8Array} requestData Request data\n * @param {RPCImplCallback} callback Callback function\n * @returns {undefined}\n * @example\n * function rpcImpl(method, requestData, callback) {\n *     if (protobuf.util.lcFirst(method.name) !== \"myMethod\") // compatible with static code\n *         throw Error(\"no such method\");\n *     asynchronouslyObtainAResponse(requestData, function(err, responseData) {\n *         callback(err, responseData);\n *     });\n * }\n */\n\n/**\n * Node-style callback as used by {@link RPCImpl}.\n * @typedef RPCImplCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Uint8Array|null} [response] Response data or `null` to signal end of stream, if there hasn't been an error\n * @returns {undefined}\n */\n\nrpc.Service = require(\"./rpc/service\");\n"], "names": [], "mappings": "AAAA;AAEA;;;CAGC,GACD,IAAI,MAAM;AAEV;;;;;;;;;;;;;;;;CAgBC,GAED;;;;;;;CAOC,GAED,IAAI,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1289, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/service.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = Service;\n\n// extends Namespace\nvar Namespace = require(\"./namespace\");\n((Service.prototype = Object.create(Namespace.prototype)).constructor = Service).className = \"Service\";\n\nvar Method = require(\"./method\"),\n    util   = require(\"./util\"),\n    rpc    = require(\"./rpc\");\n\n/**\n * Constructs a new service instance.\n * @classdesc Reflected service.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Service name\n * @param {Object.<string,*>} [options] Service options\n * @throws {TypeError} If arguments are invalid\n */\nfunction Service(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Service methods.\n     * @type {Object.<string,Method>}\n     */\n    this.methods = {}; // toJSON, marker\n\n    /**\n     * Cached methods as an array.\n     * @type {Method[]|null}\n     * @private\n     */\n    this._methodsArray = null;\n}\n\n/**\n * Service descriptor.\n * @interface IService\n * @extends INamespace\n * @property {Object.<string,IMethod>} methods Method descriptors\n */\n\n/**\n * Constructs a service from a service descriptor.\n * @param {string} name Service name\n * @param {IService} json Service descriptor\n * @returns {Service} Created service\n * @throws {TypeError} If arguments are invalid\n */\nService.fromJSON = function fromJSON(name, json) {\n    var service = new Service(name, json.options);\n    /* istanbul ignore else */\n    if (json.methods)\n        for (var names = Object.keys(json.methods), i = 0; i < names.length; ++i)\n            service.add(Method.fromJSON(names[i], json.methods[names[i]]));\n    if (json.nested)\n        service.addJSON(json.nested);\n    if (json.edition)\n        service._edition = json.edition;\n    service.comment = json.comment;\n    service._defaultEdition = \"proto3\";  // For backwards-compatibility.\n    return service;\n};\n\n/**\n * Converts this service to a service descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IService} Service descriptor\n */\nService.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"edition\" , this._editionToJSON(),\n        \"options\" , inherited && inherited.options || undefined,\n        \"methods\" , Namespace.arrayToJSON(this.methodsArray, toJSONOptions) || /* istanbul ignore next */ {},\n        \"nested\"  , inherited && inherited.nested || undefined,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Methods of this service as an array for iteration.\n * @name Service#methodsArray\n * @type {Method[]}\n * @readonly\n */\nObject.defineProperty(Service.prototype, \"methodsArray\", {\n    get: function() {\n        return this._methodsArray || (this._methodsArray = util.toArray(this.methods));\n    }\n});\n\nfunction clearCache(service) {\n    service._methodsArray = null;\n    return service;\n}\n\n/**\n * @override\n */\nService.prototype.get = function get(name) {\n    return this.methods[name]\n        || Namespace.prototype.get.call(this, name);\n};\n\n/**\n * @override\n */\nService.prototype.resolveAll = function resolveAll() {\n    if (!this._needsRecursiveResolve) return this;\n\n    Namespace.prototype.resolve.call(this);\n    var methods = this.methodsArray;\n    for (var i = 0; i < methods.length; ++i)\n        methods[i].resolve();\n    return this;\n};\n\n/**\n * @override\n */\nService.prototype._resolveFeaturesRecursive = function _resolveFeaturesRecursive(edition) {\n    if (!this._needsRecursiveFeatureResolution) return this;\n\n    edition = this._edition || edition;\n\n    Namespace.prototype._resolveFeaturesRecursive.call(this, edition);\n    this.methodsArray.forEach(method => {\n        method._resolveFeaturesRecursive(edition);\n    });\n    return this;\n};\n\n/**\n * @override\n */\nService.prototype.add = function add(object) {\n\n    /* istanbul ignore if */\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Method) {\n        this.methods[object.name] = object;\n        object.parent = this;\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * @override\n */\nService.prototype.remove = function remove(object) {\n    if (object instanceof Method) {\n\n        /* istanbul ignore if */\n        if (this.methods[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.methods[object.name];\n        object.parent = null;\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Creates a runtime service using the specified rpc implementation.\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n * @returns {rpc.Service} RPC service. Useful where requests and/or responses are streamed.\n */\nService.prototype.create = function create(rpcImpl, requestDelimited, responseDelimited) {\n    var rpcService = new rpc.Service(rpcImpl, requestDelimited, responseDelimited);\n    for (var i = 0, method; i < /* initializes */ this.methodsArray.length; ++i) {\n        var methodName = util.lcFirst((method = this._methodsArray[i]).resolve().name).replace(/[^$\\w_]/g, \"\");\n        rpcService[methodName] = util.codegen([\"r\",\"c\"], util.isReserved(methodName) ? methodName + \"_\" : methodName)(\"return this.rpcCall(m,q,s,r,c)\")({\n            m: method,\n            q: method.resolvedRequestType.ctor,\n            s: method.resolvedResponseType.ctor\n        });\n    }\n    return rpcService;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,oBAAoB;AACpB,IAAI;AACJ,CAAC,CAAC,QAAQ,SAAS,GAAG,OAAO,MAAM,CAAC,UAAU,SAAS,CAAC,EAAE,WAAW,GAAG,OAAO,EAAE,SAAS,GAAG;AAE7F,IAAI,4GACA,wGACA;AAEJ;;;;;;;;CAQC,GACD,SAAS,QAAQ,IAAI,EAAE,OAAO;IAC1B,UAAU,IAAI,CAAC,IAAI,EAAE,MAAM;IAE3B;;;KAGC,GACD,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,iBAAiB;IAEpC;;;;KAIC,GACD,IAAI,CAAC,aAAa,GAAG;AACzB;AAEA;;;;;CAKC,GAED;;;;;;CAMC,GACD,QAAQ,QAAQ,GAAG,SAAS,SAAS,IAAI,EAAE,IAAI;IAC3C,IAAI,UAAU,IAAI,QAAQ,MAAM,KAAK,OAAO;IAC5C,wBAAwB,GACxB,IAAI,KAAK,OAAO,EACZ,IAAK,IAAI,QAAQ,OAAO,IAAI,CAAC,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EACnE,QAAQ,GAAG,CAAC,OAAO,QAAQ,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;IACpE,IAAI,KAAK,MAAM,EACX,QAAQ,OAAO,CAAC,KAAK,MAAM;IAC/B,IAAI,KAAK,OAAO,EACZ,QAAQ,QAAQ,GAAG,KAAK,OAAO;IACnC,QAAQ,OAAO,GAAG,KAAK,OAAO;IAC9B,QAAQ,eAAe,GAAG,UAAW,+BAA+B;IACpE,OAAO;AACX;AAEA;;;;CAIC,GACD,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,aAAa;IACpD,IAAI,YAAY,UAAU,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE;IACtD,IAAI,eAAe,gBAAgB,QAAQ,cAAc,YAAY,IAAI;IACzE,OAAO,KAAK,QAAQ,CAAC;QACjB;QAAY,IAAI,CAAC,cAAc;QAC/B;QAAY,aAAa,UAAU,OAAO,IAAI;QAC9C;QAAY,UAAU,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,kBAAkB,wBAAwB,GAAG,CAAC;QACnG;QAAY,aAAa,UAAU,MAAM,IAAI;QAC7C;QAAY,eAAe,IAAI,CAAC,OAAO,GAAG;KAC7C;AACL;AAEA;;;;;CAKC,GACD,OAAO,cAAc,CAAC,QAAQ,SAAS,EAAE,gBAAgB;IACrD,KAAK;QACD,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;IACjF;AACJ;AAEA,SAAS,WAAW,OAAO;IACvB,QAAQ,aAAa,GAAG;IACxB,OAAO;AACX;AAEA;;CAEC,GACD,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,IAAI;IACrC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,IAClB,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;AAC9C;AAEA;;CAEC,GACD,QAAQ,SAAS,CAAC,UAAU,GAAG,SAAS;IACpC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,OAAO,IAAI;IAE7C,UAAU,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;IACrC,IAAI,UAAU,IAAI,CAAC,YAAY;IAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,EAAE,EAClC,OAAO,CAAC,EAAE,CAAC,OAAO;IACtB,OAAO,IAAI;AACf;AAEA;;CAEC,GACD,QAAQ,SAAS,CAAC,yBAAyB,GAAG,SAAS,0BAA0B,OAAO;IACpF,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE,OAAO,IAAI;IAEvD,UAAU,IAAI,CAAC,QAAQ,IAAI;IAE3B,UAAU,SAAS,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE;IACzD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;QACtB,OAAO,yBAAyB,CAAC;IACrC;IACA,OAAO,IAAI;AACf;AAEA;;CAEC,GACD,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,MAAM;IAEvC,sBAAsB,GACtB,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,GACpB,MAAM,MAAM,qBAAqB,OAAO,IAAI,GAAG,UAAU,IAAI;IAEjE,IAAI,kBAAkB,QAAQ;QAC1B,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,GAAG;QAC5B,OAAO,MAAM,GAAG,IAAI;QACpB,OAAO,WAAW,IAAI;IAC1B;IACA,OAAO,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;AAC9C;AAEA;;CAEC,GACD,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,MAAM;IAC7C,IAAI,kBAAkB,QAAQ;QAE1B,sBAAsB,GACtB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,KAAK,QAC9B,MAAM,MAAM,SAAS,yBAAyB,IAAI;QAEtD,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC;QAChC,OAAO,MAAM,GAAG;QAChB,OAAO,WAAW,IAAI;IAC1B;IACA,OAAO,UAAU,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE;AACjD;AAEA;;;;;;CAMC,GACD,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,OAAO,EAAE,gBAAgB,EAAE,iBAAiB;IACnF,IAAI,aAAa,IAAI,IAAI,OAAO,CAAC,SAAS,kBAAkB;IAC5D,IAAK,IAAI,IAAI,GAAG,QAAQ,IAAI,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,EAAG;QACzE,IAAI,aAAa,KAAK,OAAO,CAAC,CAAC,SAAS,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,CAAC,YAAY;QACnG,UAAU,CAAC,WAAW,GAAG,KAAK,OAAO,CAAC;YAAC;YAAI;SAAI,EAAE,KAAK,UAAU,CAAC,cAAc,aAAa,MAAM,YAAY,kCAAkC;YAC5I,GAAG;YACH,GAAG,OAAO,mBAAmB,CAAC,IAAI;YAClC,GAAG,OAAO,oBAAoB,CAAC,IAAI;QACvC;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1442, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/message.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = Message;\n\nvar util = require(\"./util/minimal\");\n\n/**\n * Constructs a new message instance.\n * @classdesc Abstract runtime message.\n * @constructor\n * @param {Properties<T>} [properties] Properties to set\n * @template T extends object = object\n */\nfunction Message(properties) {\n    // not used internally\n    if (properties)\n        for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n            this[keys[i]] = properties[keys[i]];\n}\n\n/**\n * Reference to the reflected type.\n * @name Message.$type\n * @type {Type}\n * @readonly\n */\n\n/**\n * Reference to the reflected type.\n * @name Message#$type\n * @type {Type}\n * @readonly\n */\n\n/*eslint-disable valid-jsdoc*/\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<T>} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.create = function create(properties) {\n    return this.$type.create(properties);\n};\n\n/**\n * Encodes a message of this type.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encode = function encode(message, writer) {\n    return this.$type.encode(message, writer);\n};\n\n/**\n * Encodes a message of this type preceeded by its length as a varint.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.$type.encodeDelimited(message, writer);\n};\n\n/**\n * Decodes a message of this type.\n * @name Message.decode\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decode = function decode(reader) {\n    return this.$type.decode(reader);\n};\n\n/**\n * Decodes a message of this type preceeded by its length as a varint.\n * @name Message.decodeDelimited\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decodeDelimited = function decodeDelimited(reader) {\n    return this.$type.decodeDelimited(reader);\n};\n\n/**\n * Verifies a message of this type.\n * @name Message.verify\n * @function\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {string|null} `null` if valid, otherwise the reason why it is not\n */\nMessage.verify = function verify(message) {\n    return this.$type.verify(message);\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object\n * @returns {T} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.fromObject = function fromObject(object) {\n    return this.$type.fromObject(object);\n};\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {T} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.toObject = function toObject(message, options) {\n    return this.$type.toObject(message, options);\n};\n\n/**\n * Converts this message to JSON.\n * @returns {Object.<string,*>} JSON object\n */\nMessage.prototype.toJSON = function toJSON() {\n    return this.$type.toObject(this, util.toJSONOptions);\n};\n\n/*eslint-enable valid-jsdoc*/"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,IAAI;AAEJ;;;;;;CAMC,GACD,SAAS,QAAQ,UAAU;IACvB,sBAAsB;IACtB,IAAI,YACA,IAAK,IAAI,OAAO,OAAO,IAAI,CAAC,aAAa,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAC/D,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;AAC/C;AAEA;;;;;CAKC,GAED;;;;;CAKC,GAED,4BAA4B,GAE5B;;;;;;CAMC,GACD,QAAQ,MAAM,GAAG,SAAS,OAAO,UAAU;IACvC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAC7B;AAEA;;;;;;;CAOC,GACD,QAAQ,MAAM,GAAG,SAAS,OAAO,OAAO,EAAE,MAAM;IAC5C,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS;AACtC;AAEA;;;;;;;CAOC,GACD,QAAQ,eAAe,GAAG,SAAS,gBAAgB,OAAO,EAAE,MAAM;IAC9D,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,SAAS;AAC/C;AAEA;;;;;;;;CAQC,GACD,QAAQ,MAAM,GAAG,SAAS,OAAO,MAAM;IACnC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAC7B;AAEA;;;;;;;;CAQC,GACD,QAAQ,eAAe,GAAG,SAAS,gBAAgB,MAAM;IACrD,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;AACtC;AAEA;;;;;;CAMC,GACD,QAAQ,MAAM,GAAG,SAAS,OAAO,OAAO;IACpC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAC7B;AAEA;;;;;;CAMC,GACD,QAAQ,UAAU,GAAG,SAAS,WAAW,MAAM;IAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;AACjC;AAEA;;;;;;;CAOC,GACD,QAAQ,QAAQ,GAAG,SAAS,SAAS,OAAO,EAAE,OAAO;IACjD,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS;AACxC;AAEA;;;CAGC,GACD,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAS;IAChC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,aAAa;AACvD,GAEA,2BAA2B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/reader.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = Reader;\n\nvar util      = require(\"./util/minimal\");\n\nvar BufferReader; // cyclic\n\nvar LongBits  = util.LongBits,\n    utf8      = util.utf8;\n\n/* istanbul ignore next */\nfunction indexOutOfRange(reader, writeLength) {\n    return RangeError(\"index out of range: \" + reader.pos + \" + \" + (writeLength || 1) + \" > \" + reader.len);\n}\n\n/**\n * Constructs a new reader instance using the specified buffer.\n * @classdesc Wire format reader using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n * @param {Uint8Array} buffer Buffer to read from\n */\nfunction Reader(buffer) {\n\n    /**\n     * Read buffer.\n     * @type {Uint8Array}\n     */\n    this.buf = buffer;\n\n    /**\n     * Read buffer position.\n     * @type {number}\n     */\n    this.pos = 0;\n\n    /**\n     * Read buffer length.\n     * @type {number}\n     */\n    this.len = buffer.length;\n}\n\nvar create_array = typeof Uint8Array !== \"undefined\"\n    ? function create_typed_array(buffer) {\n        if (buffer instanceof Uint8Array || Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    }\n    /* istanbul ignore next */\n    : function create_array(buffer) {\n        if (Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    };\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup(buffer) {\n            return (Reader.create = function create_buffer(buffer) {\n                return util.Buffer.isBuffer(buffer)\n                    ? new BufferReader(buffer)\n                    /* istanbul ignore next */\n                    : create_array(buffer);\n            })(buffer);\n        }\n        /* istanbul ignore next */\n        : create_array;\n};\n\n/**\n * Creates a new reader using the specified buffer.\n * @function\n * @param {Uint8Array|Buffer} buffer Buffer to read from\n * @returns {Reader|BufferReader} A {@link BufferReader} if `buffer` is a Buffer, otherwise a {@link Reader}\n * @throws {Error} If `buffer` is not a valid buffer\n */\nReader.create = create();\n\nReader.prototype._slice = util.Array.prototype.subarray || /* istanbul ignore next */ util.Array.prototype.slice;\n\n/**\n * Reads a varint as an unsigned 32 bit value.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.uint32 = (function read_uint32_setup() {\n    var value = 4294967295; // optimizer type-hint, tends to deopt otherwise (?!)\n    return function read_uint32() {\n        value = (         this.buf[this.pos] & 127       ) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) <<  7) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 14) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 21) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] &  15) << 28) >>> 0; if (this.buf[this.pos++] < 128) return value;\n\n        /* istanbul ignore if */\n        if ((this.pos += 5) > this.len) {\n            this.pos = this.len;\n            throw indexOutOfRange(this, 10);\n        }\n        return value;\n    };\n})();\n\n/**\n * Reads a varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.int32 = function read_int32() {\n    return this.uint32() | 0;\n};\n\n/**\n * Reads a zig-zag encoded varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.sint32 = function read_sint32() {\n    var value = this.uint32();\n    return value >>> 1 ^ -(value & 1) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readLongVarint() {\n    // tends to deopt with local vars for octet etc.\n    var bits = new LongBits(0, 0);\n    var i = 0;\n    if (this.len - this.pos > 4) { // fast route (lo)\n        for (; i < 4; ++i) {\n            // 1st..4th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 5th\n        bits.lo = (bits.lo | (this.buf[this.pos] & 127) << 28) >>> 0;\n        bits.hi = (bits.hi | (this.buf[this.pos] & 127) >>  4) >>> 0;\n        if (this.buf[this.pos++] < 128)\n            return bits;\n        i = 0;\n    } else {\n        for (; i < 3; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 1st..3th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 4th\n        bits.lo = (bits.lo | (this.buf[this.pos++] & 127) << i * 7) >>> 0;\n        return bits;\n    }\n    if (this.len - this.pos > 4) { // fast route (hi)\n        for (; i < 5; ++i) {\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    } else {\n        for (; i < 5; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    }\n    /* istanbul ignore next */\n    throw Error(\"invalid varint encoding\");\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads a varint as a signed 64 bit value.\n * @name Reader#int64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as an unsigned 64 bit value.\n * @name Reader#uint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a zig-zag encoded varint as a signed 64 bit value.\n * @name Reader#sint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as a boolean.\n * @returns {boolean} Value read\n */\nReader.prototype.bool = function read_bool() {\n    return this.uint32() !== 0;\n};\n\nfunction readFixed32_end(buf, end) { // note that this uses `end`, not `pos`\n    return (buf[end - 4]\n          | buf[end - 3] << 8\n          | buf[end - 2] << 16\n          | buf[end - 1] << 24) >>> 0;\n}\n\n/**\n * Reads fixed 32 bits as an unsigned 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.fixed32 = function read_fixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4);\n};\n\n/**\n * Reads fixed 32 bits as a signed 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.sfixed32 = function read_sfixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readFixed64(/* this: Reader */) {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 8);\n\n    return new LongBits(readFixed32_end(this.buf, this.pos += 4), readFixed32_end(this.buf, this.pos += 4));\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads fixed 64 bits.\n * @name Reader#fixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads zig-zag encoded fixed 64 bits.\n * @name Reader#sfixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a float (32 bit) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.float = function read_float() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readFloatLE(this.buf, this.pos);\n    this.pos += 4;\n    return value;\n};\n\n/**\n * Reads a double (64 bit float) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.double = function read_double() {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readDoubleLE(this.buf, this.pos);\n    this.pos += 8;\n    return value;\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @returns {Uint8Array} Value read\n */\nReader.prototype.bytes = function read_bytes() {\n    var length = this.uint32(),\n        start  = this.pos,\n        end    = this.pos + length;\n\n    /* istanbul ignore if */\n    if (end > this.len)\n        throw indexOutOfRange(this, length);\n\n    this.pos += length;\n    if (Array.isArray(this.buf)) // plain array\n        return this.buf.slice(start, end);\n\n    if (start === end) { // fix for IE 10/Win8 and others' subarray returning array of size 1\n        var nativeBuffer = util.Buffer;\n        return nativeBuffer\n            ? nativeBuffer.alloc(0)\n            : new this.buf.constructor(0);\n    }\n    return this._slice.call(this.buf, start, end);\n};\n\n/**\n * Reads a string preceeded by its byte length as a varint.\n * @returns {string} Value read\n */\nReader.prototype.string = function read_string() {\n    var bytes = this.bytes();\n    return utf8.read(bytes, 0, bytes.length);\n};\n\n/**\n * Skips the specified number of bytes if specified, otherwise skips a varint.\n * @param {number} [length] Length if known, otherwise a varint is assumed\n * @returns {Reader} `this`\n */\nReader.prototype.skip = function skip(length) {\n    if (typeof length === \"number\") {\n        /* istanbul ignore if */\n        if (this.pos + length > this.len)\n            throw indexOutOfRange(this, length);\n        this.pos += length;\n    } else {\n        do {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n        } while (this.buf[this.pos++] & 128);\n    }\n    return this;\n};\n\n/**\n * Skips the next element of the specified wire type.\n * @param {number} wireType Wire type received\n * @returns {Reader} `this`\n */\nReader.prototype.skipType = function(wireType) {\n    switch (wireType) {\n        case 0:\n            this.skip();\n            break;\n        case 1:\n            this.skip(8);\n            break;\n        case 2:\n            this.skip(this.uint32());\n            break;\n        case 3:\n            while ((wireType = this.uint32() & 7) !== 4) {\n                this.skipType(wireType);\n            }\n            break;\n        case 5:\n            this.skip(4);\n            break;\n\n        /* istanbul ignore next */\n        default:\n            throw Error(\"invalid wire type \" + wireType + \" at offset \" + this.pos);\n    }\n    return this;\n};\n\nReader._configure = function(BufferReader_) {\n    BufferReader = BufferReader_;\n    Reader.create = create();\n    BufferReader._configure();\n\n    var fn = util.Long ? \"toLong\" : /* istanbul ignore next */ \"toNumber\";\n    util.merge(Reader.prototype, {\n\n        int64: function read_int64() {\n            return readLongVarint.call(this)[fn](false);\n        },\n\n        uint64: function read_uint64() {\n            return readLongVarint.call(this)[fn](true);\n        },\n\n        sint64: function read_sint64() {\n            return readLongVarint.call(this).zzDecode()[fn](false);\n        },\n\n        fixed64: function read_fixed64() {\n            return readFixed64.call(this)[fn](true);\n        },\n\n        sfixed64: function read_sfixed64() {\n            return readFixed64.call(this)[fn](false);\n        }\n\n    });\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,IAAI;AAEJ,IAAI,cAAc,SAAS;AAE3B,IAAI,WAAY,KAAK,QAAQ,EACzB,OAAY,KAAK,IAAI;AAEzB,wBAAwB,GACxB,SAAS,gBAAgB,MAAM,EAAE,WAAW;IACxC,OAAO,WAAW,yBAAyB,OAAO,GAAG,GAAG,QAAQ,CAAC,eAAe,CAAC,IAAI,QAAQ,OAAO,GAAG;AAC3G;AAEA;;;;;CAKC,GACD,SAAS,OAAO,MAAM;IAElB;;;KAGC,GACD,IAAI,CAAC,GAAG,GAAG;IAEX;;;KAGC,GACD,IAAI,CAAC,GAAG,GAAG;IAEX;;;KAGC,GACD,IAAI,CAAC,GAAG,GAAG,OAAO,MAAM;AAC5B;AAEA,IAAI,eAAe,OAAO,eAAe,cACnC,SAAS,mBAAmB,MAAM;IAChC,IAAI,kBAAkB,cAAc,MAAM,OAAO,CAAC,SAC9C,OAAO,IAAI,OAAO;IACtB,MAAM,MAAM;AAChB,IAEE,SAAS,aAAa,MAAM;IAC1B,IAAI,MAAM,OAAO,CAAC,SACd,OAAO,IAAI,OAAO;IACtB,MAAM,MAAM;AAChB;AAEJ,IAAI,SAAS,SAAS;IAClB,OAAO,KAAK,MAAM,GACZ,SAAS,oBAAoB,MAAM;QACjC,OAAO,CAAC,OAAO,MAAM,GAAG,SAAS,cAAc,MAAM;YACjD,OAAO,KAAK,MAAM,CAAC,QAAQ,CAAC,UACtB,IAAI,aAAa,UAEjB,aAAa;QACvB,CAAC,EAAE;IACP,IAEE;AACV;AAEA;;;;;;CAMC,GACD,OAAO,MAAM,GAAG;AAEhB,OAAO,SAAS,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,QAAQ,IAAI,wBAAwB,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,KAAK;AAEhH;;;;CAIC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,AAAC,SAAS;IAChC,IAAI,QAAQ,YAAY,qDAAqD;IAC7E,OAAO,SAAS;QACZ,QAAQ,CAAU,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAU,MAAM;QAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,KAAK,OAAO;QACjG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAM,CAAC,MAAM;QAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,KAAK,OAAO;QACjG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,EAAE,MAAM;QAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,KAAK,OAAO;QACjG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,EAAE,MAAM;QAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,KAAK,OAAO;QACjG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAI,EAAE,KAAK,EAAE,MAAM;QAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,KAAK,OAAO;QAEjG,sBAAsB,GACtB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE;YAC5B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG;YACnB,MAAM,gBAAgB,IAAI,EAAE;QAChC;QACA,OAAO;IACX;AACJ;AAEA;;;CAGC,GACD,OAAO,SAAS,CAAC,KAAK,GAAG,SAAS;IAC9B,OAAO,IAAI,CAAC,MAAM,KAAK;AAC3B;AAEA;;;CAGC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS;IAC/B,IAAI,QAAQ,IAAI,CAAC,MAAM;IACvB,OAAO,UAAU,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI;AACxC;AAEA,kCAAkC,GAElC,SAAS;IACL,gDAAgD;IAChD,IAAI,OAAO,IAAI,SAAS,GAAG;IAC3B,IAAI,IAAI;IACR,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG;QACzB,MAAO,IAAI,GAAG,EAAE,EAAG;YACf,WAAW;YACX,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,MAAM;YAC9D,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,KACvB,OAAO;QACf;QACA,MAAM;QACN,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,EAAE,MAAM;QAC3D,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAM,CAAC,MAAM;QAC3D,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,KACvB,OAAO;QACX,IAAI;IACR,OAAO;QACH,MAAO,IAAI,GAAG,EAAE,EAAG;YACf,sBAAsB,GACtB,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,EACpB,MAAM,gBAAgB,IAAI;YAC9B,WAAW;YACX,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,MAAM;YAC9D,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,KACvB,OAAO;QACf;QACA,MAAM;QACN,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,IAAI,CAAC,MAAM;QAChE,OAAO;IACX;IACA,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG;QACzB,MAAO,IAAI,GAAG,EAAE,EAAG;YACf,YAAY;YACZ,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,IAAI,CAAC,MAAM;YAClE,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,KACvB,OAAO;QACf;IACJ,OAAO;QACH,MAAO,IAAI,GAAG,EAAE,EAAG;YACf,sBAAsB,GACtB,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,EACpB,MAAM,gBAAgB,IAAI;YAC9B,YAAY;YACZ,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,IAAI,CAAC,MAAM;YAClE,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,KACvB,OAAO;QACf;IACJ;IACA,wBAAwB,GACxB,MAAM,MAAM;AAChB;AAEA,iCAAiC,GAEjC;;;;;CAKC,GAED;;;;;CAKC,GAED;;;;;CAKC,GAED;;;CAGC,GACD,OAAO,SAAS,CAAC,IAAI,GAAG,SAAS;IAC7B,OAAO,IAAI,CAAC,MAAM,OAAO;AAC7B;AAEA,SAAS,gBAAgB,GAAG,EAAE,GAAG;IAC7B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GACZ,GAAG,CAAC,MAAM,EAAE,IAAI,IAChB,GAAG,CAAC,MAAM,EAAE,IAAI,KAChB,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM;AACpC;AAEA;;;CAGC,GACD,OAAO,SAAS,CAAC,OAAO,GAAG,SAAS;IAEhC,sBAAsB,GACtB,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,EACvB,MAAM,gBAAgB,IAAI,EAAE;IAEhC,OAAO,gBAAgB,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI;AACjD;AAEA;;;CAGC,GACD,OAAO,SAAS,CAAC,QAAQ,GAAG,SAAS;IAEjC,sBAAsB,GACtB,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,EACvB,MAAM,gBAAgB,IAAI,EAAE;IAEhC,OAAO,gBAAgB,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,KAAK;AACtD;AAEA,kCAAkC,GAElC,SAAS;IAEL,sBAAsB,GACtB,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,EACvB,MAAM,gBAAgB,IAAI,EAAE;IAEhC,OAAO,IAAI,SAAS,gBAAgB,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,IAAI,gBAAgB,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI;AACxG;AAEA,iCAAiC,GAEjC;;;;;CAKC,GAED;;;;;CAKC,GAED;;;;CAIC,GACD,OAAO,SAAS,CAAC,KAAK,GAAG,SAAS;IAE9B,sBAAsB,GACtB,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,EACvB,MAAM,gBAAgB,IAAI,EAAE;IAEhC,IAAI,QAAQ,KAAK,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG;IACrD,IAAI,CAAC,GAAG,IAAI;IACZ,OAAO;AACX;AAEA;;;;CAIC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS;IAE/B,sBAAsB,GACtB,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,EACvB,MAAM,gBAAgB,IAAI,EAAE;IAEhC,IAAI,QAAQ,KAAK,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG;IACtD,IAAI,CAAC,GAAG,IAAI;IACZ,OAAO;AACX;AAEA;;;CAGC,GACD,OAAO,SAAS,CAAC,KAAK,GAAG,SAAS;IAC9B,IAAI,SAAS,IAAI,CAAC,MAAM,IACpB,QAAS,IAAI,CAAC,GAAG,EACjB,MAAS,IAAI,CAAC,GAAG,GAAG;IAExB,sBAAsB,GACtB,IAAI,MAAM,IAAI,CAAC,GAAG,EACd,MAAM,gBAAgB,IAAI,EAAE;IAEhC,IAAI,CAAC,GAAG,IAAI;IACZ,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,GACtB,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO;IAEjC,IAAI,UAAU,KAAK;QACf,IAAI,eAAe,KAAK,MAAM;QAC9B,OAAO,eACD,aAAa,KAAK,CAAC,KACnB,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;IACnC;IACA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO;AAC7C;AAEA;;;CAGC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS;IAC/B,IAAI,QAAQ,IAAI,CAAC,KAAK;IACtB,OAAO,KAAK,IAAI,CAAC,OAAO,GAAG,MAAM,MAAM;AAC3C;AAEA;;;;CAIC,GACD,OAAO,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK,MAAM;IACxC,IAAI,OAAO,WAAW,UAAU;QAC5B,sBAAsB,GACtB,IAAI,IAAI,CAAC,GAAG,GAAG,SAAS,IAAI,CAAC,GAAG,EAC5B,MAAM,gBAAgB,IAAI,EAAE;QAChC,IAAI,CAAC,GAAG,IAAI;IAChB,OAAO;QACH,GAAG;YACC,sBAAsB,GACtB,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,EACpB,MAAM,gBAAgB,IAAI;QAClC,QAAS,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,IAAK;IACzC;IACA,OAAO,IAAI;AACf;AAEA;;;;CAIC,GACD,OAAO,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ;IACzC,OAAQ;QACJ,KAAK;YACD,IAAI,CAAC,IAAI;YACT;QACJ,KAAK;YACD,IAAI,CAAC,IAAI,CAAC;YACV;QACJ,KAAK;YACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;YACrB;QACJ,KAAK;YACD,MAAO,CAAC,WAAW,IAAI,CAAC,MAAM,KAAK,CAAC,MAAM,EAAG;gBACzC,IAAI,CAAC,QAAQ,CAAC;YAClB;YACA;QACJ,KAAK;YACD,IAAI,CAAC,IAAI,CAAC;YACV;QAEJ,wBAAwB,GACxB;YACI,MAAM,MAAM,uBAAuB,WAAW,gBAAgB,IAAI,CAAC,GAAG;IAC9E;IACA,OAAO,IAAI;AACf;AAEA,OAAO,UAAU,GAAG,SAAS,aAAa;IACtC,eAAe;IACf,OAAO,MAAM,GAAG;IAChB,aAAa,UAAU;IAEvB,IAAI,KAAK,KAAK,IAAI,GAAG,WAAW,wBAAwB,GAAG;IAC3D,KAAK,KAAK,CAAC,OAAO,SAAS,EAAE;QAEzB,OAAO,SAAS;YACZ,OAAO,eAAe,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;QACzC;QAEA,QAAQ,SAAS;YACb,OAAO,eAAe,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;QACzC;QAEA,QAAQ,SAAS;YACb,OAAO,eAAe,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC;QACpD;QAEA,SAAS,SAAS;YACd,OAAO,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;QACtC;QAEA,UAAU,SAAS;YACf,OAAO,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;QACtC;IAEJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1847, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/writer.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = Writer;\n\nvar util      = require(\"./util/minimal\");\n\nvar BufferWriter; // cyclic\n\nvar LongBits  = util.LongBits,\n    base64    = util.base64,\n    utf8      = util.utf8;\n\n/**\n * Constructs a new writer operation instance.\n * @classdesc Scheduled writer operation.\n * @constructor\n * @param {function(*, Uint8Array, number)} fn Function to call\n * @param {number} len Value byte length\n * @param {*} val Value to write\n * @ignore\n */\nfunction Op(fn, len, val) {\n\n    /**\n     * Function to call.\n     * @type {function(Uint8Array, number, *)}\n     */\n    this.fn = fn;\n\n    /**\n     * Value byte length.\n     * @type {number}\n     */\n    this.len = len;\n\n    /**\n     * Next operation.\n     * @type {Writer.Op|undefined}\n     */\n    this.next = undefined;\n\n    /**\n     * Value to write.\n     * @type {*}\n     */\n    this.val = val; // type varies\n}\n\n/* istanbul ignore next */\nfunction noop() {} // eslint-disable-line no-empty-function\n\n/**\n * Constructs a new writer state instance.\n * @classdesc Copied writer state.\n * @memberof Writer\n * @constructor\n * @param {Writer} writer Writer to copy state from\n * @ignore\n */\nfunction State(writer) {\n\n    /**\n     * Current head.\n     * @type {Writer.Op}\n     */\n    this.head = writer.head;\n\n    /**\n     * Current tail.\n     * @type {Writer.Op}\n     */\n    this.tail = writer.tail;\n\n    /**\n     * Current buffer length.\n     * @type {number}\n     */\n    this.len = writer.len;\n\n    /**\n     * Next state.\n     * @type {State|null}\n     */\n    this.next = writer.states;\n}\n\n/**\n * Constructs a new writer instance.\n * @classdesc Wire format writer using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n */\nfunction Writer() {\n\n    /**\n     * Current length.\n     * @type {number}\n     */\n    this.len = 0;\n\n    /**\n     * Operations head.\n     * @type {Object}\n     */\n    this.head = new Op(noop, 0, 0);\n\n    /**\n     * Operations tail\n     * @type {Object}\n     */\n    this.tail = this.head;\n\n    /**\n     * Linked forked states.\n     * @type {Object|null}\n     */\n    this.states = null;\n\n    // When a value is written, the writer calculates its byte length and puts it into a linked\n    // list of operations to perform when finish() is called. This both allows us to allocate\n    // buffers of the exact required size and reduces the amount of work we have to do compared\n    // to first calculating over objects and then encoding over objects. In our case, the encoding\n    // part is just a linked list walk calling operations with already prepared values.\n}\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup() {\n            return (Writer.create = function create_buffer() {\n                return new BufferWriter();\n            })();\n        }\n        /* istanbul ignore next */\n        : function create_array() {\n            return new Writer();\n        };\n};\n\n/**\n * Creates a new writer.\n * @function\n * @returns {BufferWriter|Writer} A {@link BufferWriter} when Buffers are supported, otherwise a {@link Writer}\n */\nWriter.create = create();\n\n/**\n * Allocates a buffer of the specified size.\n * @param {number} size Buffer size\n * @returns {Uint8Array} Buffer\n */\nWriter.alloc = function alloc(size) {\n    return new util.Array(size);\n};\n\n// Use Uint8Array buffer pool in the browser, just like node does with buffers\n/* istanbul ignore else */\nif (util.Array !== Array)\n    Writer.alloc = util.pool(Writer.alloc, util.Array.prototype.subarray);\n\n/**\n * Pushes a new operation to the queue.\n * @param {function(Uint8Array, number, *)} fn Function to call\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @returns {Writer} `this`\n * @private\n */\nWriter.prototype._push = function push(fn, len, val) {\n    this.tail = this.tail.next = new Op(fn, len, val);\n    this.len += len;\n    return this;\n};\n\nfunction writeByte(val, buf, pos) {\n    buf[pos] = val & 255;\n}\n\nfunction writeVarint32(val, buf, pos) {\n    while (val > 127) {\n        buf[pos++] = val & 127 | 128;\n        val >>>= 7;\n    }\n    buf[pos] = val;\n}\n\n/**\n * Constructs a new varint writer operation instance.\n * @classdesc Scheduled varint writer operation.\n * @extends Op\n * @constructor\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @ignore\n */\nfunction VarintOp(len, val) {\n    this.len = len;\n    this.next = undefined;\n    this.val = val;\n}\n\nVarintOp.prototype = Object.create(Op.prototype);\nVarintOp.prototype.fn = writeVarint32;\n\n/**\n * Writes an unsigned 32 bit value as a varint.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.uint32 = function write_uint32(value) {\n    // here, the call to this.push has been inlined and a varint specific Op subclass is used.\n    // uint32 is by far the most frequently used operation and benefits significantly from this.\n    this.len += (this.tail = this.tail.next = new VarintOp(\n        (value = value >>> 0)\n                < 128       ? 1\n        : value < 16384     ? 2\n        : value < 2097152   ? 3\n        : value < 268435456 ? 4\n        :                     5,\n    value)).len;\n    return this;\n};\n\n/**\n * Writes a signed 32 bit value as a varint.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.int32 = function write_int32(value) {\n    return value < 0\n        ? this._push(writeVarint64, 10, LongBits.fromNumber(value)) // 10 bytes per spec\n        : this.uint32(value);\n};\n\n/**\n * Writes a 32 bit value as a varint, zig-zag encoded.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sint32 = function write_sint32(value) {\n    return this.uint32((value << 1 ^ value >> 31) >>> 0);\n};\n\nfunction writeVarint64(val, buf, pos) {\n    while (val.hi) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = (val.lo >>> 7 | val.hi << 25) >>> 0;\n        val.hi >>>= 7;\n    }\n    while (val.lo > 127) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = val.lo >>> 7;\n    }\n    buf[pos++] = val.lo;\n}\n\n/**\n * Writes an unsigned 64 bit value as a varint.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.uint64 = function write_uint64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a signed 64 bit value as a varint.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.int64 = Writer.prototype.uint64;\n\n/**\n * Writes a signed 64 bit value as a varint, zig-zag encoded.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sint64 = function write_sint64(value) {\n    var bits = LongBits.from(value).zzEncode();\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a boolish value as a varint.\n * @param {boolean} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bool = function write_bool(value) {\n    return this._push(writeByte, 1, value ? 1 : 0);\n};\n\nfunction writeFixed32(val, buf, pos) {\n    buf[pos    ] =  val         & 255;\n    buf[pos + 1] =  val >>> 8   & 255;\n    buf[pos + 2] =  val >>> 16  & 255;\n    buf[pos + 3] =  val >>> 24;\n}\n\n/**\n * Writes an unsigned 32 bit value as fixed 32 bits.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.fixed32 = function write_fixed32(value) {\n    return this._push(writeFixed32, 4, value >>> 0);\n};\n\n/**\n * Writes a signed 32 bit value as fixed 32 bits.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sfixed32 = Writer.prototype.fixed32;\n\n/**\n * Writes an unsigned 64 bit value as fixed 64 bits.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.fixed64 = function write_fixed64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeFixed32, 4, bits.lo)._push(writeFixed32, 4, bits.hi);\n};\n\n/**\n * Writes a signed 64 bit value as fixed 64 bits.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sfixed64 = Writer.prototype.fixed64;\n\n/**\n * Writes a float (32 bit).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.float = function write_float(value) {\n    return this._push(util.float.writeFloatLE, 4, value);\n};\n\n/**\n * Writes a double (64 bit float).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.double = function write_double(value) {\n    return this._push(util.float.writeDoubleLE, 8, value);\n};\n\nvar writeBytes = util.Array.prototype.set\n    ? function writeBytes_set(val, buf, pos) {\n        buf.set(val, pos); // also works for plain array values\n    }\n    /* istanbul ignore next */\n    : function writeBytes_for(val, buf, pos) {\n        for (var i = 0; i < val.length; ++i)\n            buf[pos + i] = val[i];\n    };\n\n/**\n * Writes a sequence of bytes.\n * @param {Uint8Array|string} value Buffer or base64 encoded string to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bytes = function write_bytes(value) {\n    var len = value.length >>> 0;\n    if (!len)\n        return this._push(writeByte, 1, 0);\n    if (util.isString(value)) {\n        var buf = Writer.alloc(len = base64.length(value));\n        base64.decode(value, buf, 0);\n        value = buf;\n    }\n    return this.uint32(len)._push(writeBytes, len, value);\n};\n\n/**\n * Writes a string.\n * @param {string} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.string = function write_string(value) {\n    var len = utf8.length(value);\n    return len\n        ? this.uint32(len)._push(utf8.write, len, value)\n        : this._push(writeByte, 1, 0);\n};\n\n/**\n * Forks this writer's state by pushing it to a stack.\n * Calling {@link Writer#reset|reset} or {@link Writer#ldelim|ldelim} resets the writer to the previous state.\n * @returns {Writer} `this`\n */\nWriter.prototype.fork = function fork() {\n    this.states = new State(this);\n    this.head = this.tail = new Op(noop, 0, 0);\n    this.len = 0;\n    return this;\n};\n\n/**\n * Resets this instance to the last state.\n * @returns {Writer} `this`\n */\nWriter.prototype.reset = function reset() {\n    if (this.states) {\n        this.head   = this.states.head;\n        this.tail   = this.states.tail;\n        this.len    = this.states.len;\n        this.states = this.states.next;\n    } else {\n        this.head = this.tail = new Op(noop, 0, 0);\n        this.len  = 0;\n    }\n    return this;\n};\n\n/**\n * Resets to the last state and appends the fork state's current write length as a varint followed by its operations.\n * @returns {Writer} `this`\n */\nWriter.prototype.ldelim = function ldelim() {\n    var head = this.head,\n        tail = this.tail,\n        len  = this.len;\n    this.reset().uint32(len);\n    if (len) {\n        this.tail.next = head.next; // skip noop\n        this.tail = tail;\n        this.len += len;\n    }\n    return this;\n};\n\n/**\n * Finishes the write operation.\n * @returns {Uint8Array} Finished buffer\n */\nWriter.prototype.finish = function finish() {\n    var head = this.head.next, // skip noop\n        buf  = this.constructor.alloc(this.len),\n        pos  = 0;\n    while (head) {\n        head.fn(head.val, buf, pos);\n        pos += head.len;\n        head = head.next;\n    }\n    // this.head = this.tail = null;\n    return buf;\n};\n\nWriter._configure = function(BufferWriter_) {\n    BufferWriter = BufferWriter_;\n    Writer.create = create();\n    BufferWriter._configure();\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,IAAI;AAEJ,IAAI,cAAc,SAAS;AAE3B,IAAI,WAAY,KAAK,QAAQ,EACzB,SAAY,KAAK,MAAM,EACvB,OAAY,KAAK,IAAI;AAEzB;;;;;;;;CAQC,GACD,SAAS,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG;IAEpB;;;KAGC,GACD,IAAI,CAAC,EAAE,GAAG;IAEV;;;KAGC,GACD,IAAI,CAAC,GAAG,GAAG;IAEX;;;KAGC,GACD,IAAI,CAAC,IAAI,GAAG;IAEZ;;;KAGC,GACD,IAAI,CAAC,GAAG,GAAG,KAAK,cAAc;AAClC;AAEA,wBAAwB,GACxB,SAAS,QAAQ,EAAE,wCAAwC;AAE3D;;;;;;;CAOC,GACD,SAAS,MAAM,MAAM;IAEjB;;;KAGC,GACD,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI;IAEvB;;;KAGC,GACD,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI;IAEvB;;;KAGC,GACD,IAAI,CAAC,GAAG,GAAG,OAAO,GAAG;IAErB;;;KAGC,GACD,IAAI,CAAC,IAAI,GAAG,OAAO,MAAM;AAC7B;AAEA;;;;CAIC,GACD,SAAS;IAEL;;;KAGC,GACD,IAAI,CAAC,GAAG,GAAG;IAEX;;;KAGC,GACD,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,MAAM,GAAG;IAE5B;;;KAGC,GACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;IAErB;;;KAGC,GACD,IAAI,CAAC,MAAM,GAAG;AAEd,2FAA2F;AAC3F,yFAAyF;AACzF,2FAA2F;AAC3F,8FAA8F;AAC9F,mFAAmF;AACvF;AAEA,IAAI,SAAS,SAAS;IAClB,OAAO,KAAK,MAAM,GACZ,SAAS;QACP,OAAO,CAAC,OAAO,MAAM,GAAG,SAAS;YAC7B,OAAO,IAAI;QACf,CAAC;IACL,IAEE,SAAS;QACP,OAAO,IAAI;IACf;AACR;AAEA;;;;CAIC,GACD,OAAO,MAAM,GAAG;AAEhB;;;;CAIC,GACD,OAAO,KAAK,GAAG,SAAS,MAAM,IAAI;IAC9B,OAAO,IAAI,KAAK,KAAK,CAAC;AAC1B;AAEA,8EAA8E;AAC9E,wBAAwB,GACxB,IAAI,KAAK,KAAK,KAAK,OACf,OAAO,KAAK,GAAG,KAAK,IAAI,CAAC,OAAO,KAAK,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC,QAAQ;AAExE;;;;;;;CAOC,GACD,OAAO,SAAS,CAAC,KAAK,GAAG,SAAS,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG;IAC/C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK;IAC7C,IAAI,CAAC,GAAG,IAAI;IACZ,OAAO,IAAI;AACf;AAEA,SAAS,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG;IAC5B,GAAG,CAAC,IAAI,GAAG,MAAM;AACrB;AAEA,SAAS,cAAc,GAAG,EAAE,GAAG,EAAE,GAAG;IAChC,MAAO,MAAM,IAAK;QACd,GAAG,CAAC,MAAM,GAAG,MAAM,MAAM;QACzB,SAAS;IACb;IACA,GAAG,CAAC,IAAI,GAAG;AACf;AAEA;;;;;;;;CAQC,GACD,SAAS,SAAS,GAAG,EAAE,GAAG;IACtB,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,GAAG,GAAG;AACf;AAEA,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,GAAG,SAAS;AAC/C,SAAS,SAAS,CAAC,EAAE,GAAG;AAExB;;;;CAIC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS,aAAa,KAAK;IACjD,0FAA0F;IAC1F,4FAA4F;IAC5F,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,SAC1C,CAAC,QAAQ,UAAU,CAAC,IACV,MAAY,IACpB,QAAQ,QAAY,IACpB,QAAQ,UAAY,IACpB,QAAQ,YAAY,IACA,GAC1B,MAAM,EAAE,GAAG;IACX,OAAO,IAAI;AACf;AAEA;;;;;CAKC,GACD,OAAO,SAAS,CAAC,KAAK,GAAG,SAAS,YAAY,KAAK;IAC/C,OAAO,QAAQ,IACT,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,SAAS,UAAU,CAAC,QAAQ,oBAAoB;OAC9E,IAAI,CAAC,MAAM,CAAC;AACtB;AAEA;;;;CAIC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS,aAAa,KAAK;IACjD,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,IAAI,SAAS,EAAE,MAAM;AACtD;AAEA,SAAS,cAAc,GAAG,EAAE,GAAG,EAAE,GAAG;IAChC,MAAO,IAAI,EAAE,CAAE;QACX,GAAG,CAAC,MAAM,GAAG,IAAI,EAAE,GAAG,MAAM;QAC5B,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,IAAI,EAAE,MAAM;QAC3C,IAAI,EAAE,MAAM;IAChB;IACA,MAAO,IAAI,EAAE,GAAG,IAAK;QACjB,GAAG,CAAC,MAAM,GAAG,IAAI,EAAE,GAAG,MAAM;QAC5B,IAAI,EAAE,GAAG,IAAI,EAAE,KAAK;IACxB;IACA,GAAG,CAAC,MAAM,GAAG,IAAI,EAAE;AACvB;AAEA;;;;;CAKC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS,aAAa,KAAK;IACjD,IAAI,OAAO,SAAS,IAAI,CAAC;IACzB,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,KAAK,MAAM,IAAI;AACpD;AAEA;;;;;;CAMC,GACD,OAAO,SAAS,CAAC,KAAK,GAAG,OAAO,SAAS,CAAC,MAAM;AAEhD;;;;;CAKC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS,aAAa,KAAK;IACjD,IAAI,OAAO,SAAS,IAAI,CAAC,OAAO,QAAQ;IACxC,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,KAAK,MAAM,IAAI;AACpD;AAEA;;;;CAIC,GACD,OAAO,SAAS,CAAC,IAAI,GAAG,SAAS,WAAW,KAAK;IAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,QAAQ,IAAI;AAChD;AAEA,SAAS,aAAa,GAAG,EAAE,GAAG,EAAE,GAAG;IAC/B,GAAG,CAAC,IAAQ,GAAI,MAAc;IAC9B,GAAG,CAAC,MAAM,EAAE,GAAI,QAAQ,IAAM;IAC9B,GAAG,CAAC,MAAM,EAAE,GAAI,QAAQ,KAAM;IAC9B,GAAG,CAAC,MAAM,EAAE,GAAI,QAAQ;AAC5B;AAEA;;;;CAIC,GACD,OAAO,SAAS,CAAC,OAAO,GAAG,SAAS,cAAc,KAAK;IACnD,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,UAAU;AACjD;AAEA;;;;;CAKC,GACD,OAAO,SAAS,CAAC,QAAQ,GAAG,OAAO,SAAS,CAAC,OAAO;AAEpD;;;;;CAKC,GACD,OAAO,SAAS,CAAC,OAAO,GAAG,SAAS,cAAc,KAAK;IACnD,IAAI,OAAO,SAAS,IAAI,CAAC;IACzB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,KAAK,EAAE,EAAE,KAAK,CAAC,cAAc,GAAG,KAAK,EAAE;AAC9E;AAEA;;;;;;CAMC,GACD,OAAO,SAAS,CAAC,QAAQ,GAAG,OAAO,SAAS,CAAC,OAAO;AAEpD;;;;;CAKC,GACD,OAAO,SAAS,CAAC,KAAK,GAAG,SAAS,YAAY,KAAK;IAC/C,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,YAAY,EAAE,GAAG;AAClD;AAEA;;;;;CAKC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS,aAAa,KAAK;IACjD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,aAAa,EAAE,GAAG;AACnD;AAEA,IAAI,aAAa,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,GACnC,SAAS,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG;IACnC,IAAI,GAAG,CAAC,KAAK,MAAM,oCAAoC;AAC3D,IAEE,SAAS,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG;IACnC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,EAAE,EAC9B,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE;AAC7B;AAEJ;;;;CAIC,GACD,OAAO,SAAS,CAAC,KAAK,GAAG,SAAS,YAAY,KAAK;IAC/C,IAAI,MAAM,MAAM,MAAM,KAAK;IAC3B,IAAI,CAAC,KACD,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG;IACpC,IAAI,KAAK,QAAQ,CAAC,QAAQ;QACtB,IAAI,MAAM,OAAO,KAAK,CAAC,MAAM,OAAO,MAAM,CAAC;QAC3C,OAAO,MAAM,CAAC,OAAO,KAAK;QAC1B,QAAQ;IACZ;IACA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,YAAY,KAAK;AACnD;AAEA;;;;CAIC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS,aAAa,KAAK;IACjD,IAAI,MAAM,KAAK,MAAM,CAAC;IACtB,OAAO,MACD,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,KAAK,EAAE,KAAK,SACxC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG;AACnC;AAEA;;;;CAIC,GACD,OAAO,SAAS,CAAC,IAAI,GAAG,SAAS;IAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,IAAI;IAC5B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,MAAM,GAAG;IACxC,IAAI,CAAC,GAAG,GAAG;IACX,OAAO,IAAI;AACf;AAEA;;;CAGC,GACD,OAAO,SAAS,CAAC,KAAK,GAAG,SAAS;IAC9B,IAAI,IAAI,CAAC,MAAM,EAAE;QACb,IAAI,CAAC,IAAI,GAAK,IAAI,CAAC,MAAM,CAAC,IAAI;QAC9B,IAAI,CAAC,IAAI,GAAK,IAAI,CAAC,MAAM,CAAC,IAAI;QAC9B,IAAI,CAAC,GAAG,GAAM,IAAI,CAAC,MAAM,CAAC,GAAG;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;IAClC,OAAO;QACH,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,MAAM,GAAG;QACxC,IAAI,CAAC,GAAG,GAAI;IAChB;IACA,OAAO,IAAI;AACf;AAEA;;;CAGC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS;IAC/B,IAAI,OAAO,IAAI,CAAC,IAAI,EAChB,OAAO,IAAI,CAAC,IAAI,EAChB,MAAO,IAAI,CAAC,GAAG;IACnB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;IACpB,IAAI,KAAK;QACL,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,EAAE,YAAY;QACxC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,GAAG,IAAI;IAChB;IACA,OAAO,IAAI;AACf;AAEA;;;CAGC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS;IAC/B,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EACrB,MAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GACtC,MAAO;IACX,MAAO,KAAM;QACT,KAAK,EAAE,CAAC,KAAK,GAAG,EAAE,KAAK;QACvB,OAAO,KAAK,GAAG;QACf,OAAO,KAAK,IAAI;IACpB;IACA,gCAAgC;IAChC,OAAO;AACX;AAEA,OAAO,UAAU,GAAG,SAAS,aAAa;IACtC,eAAe;IACf,OAAO,MAAM,GAAG;IAChB,aAAa,UAAU;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2200, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/decoder.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = decoder;\n\nvar Enum    = require(\"./enum\"),\n    types   = require(\"./types\"),\n    util    = require(\"./util\");\n\nfunction missing(field) {\n    return \"missing required '\" + field.name + \"'\";\n}\n\n/**\n * Generates a decoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction decoder(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"r\", \"l\", \"e\"], mtype.name + \"$decode\")\n    (\"if(!(r instanceof Reader))\")\n        (\"r=Reader.create(r)\")\n    (\"var c=l===undefined?r.len:r.pos+l,m=new this.ctor\" + (mtype.fieldsArray.filter(function(field) { return field.map; }).length ? \",k,value\" : \"\"))\n    (\"while(r.pos<c){\")\n        (\"var t=r.uint32()\")\n        (\"if(t===e)\")\n            (\"break\")\n        (\"switch(t>>>3){\");\n\n    var i = 0;\n    for (; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            type  = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            ref   = \"m\" + util.safeProp(field.name); gen\n            (\"case %i: {\", field.id);\n\n        // Map fields\n        if (field.map) { gen\n                (\"if(%s===util.emptyObject)\", ref)\n                    (\"%s={}\", ref)\n                (\"var c2 = r.uint32()+r.pos\");\n\n            if (types.defaults[field.keyType] !== undefined) gen\n                (\"k=%j\", types.defaults[field.keyType]);\n            else gen\n                (\"k=null\");\n\n            if (types.defaults[type] !== undefined) gen\n                (\"value=%j\", types.defaults[type]);\n            else gen\n                (\"value=null\");\n\n            gen\n                (\"while(r.pos<c2){\")\n                    (\"var tag2=r.uint32()\")\n                    (\"switch(tag2>>>3){\")\n                        (\"case 1: k=r.%s(); break\", field.keyType)\n                        (\"case 2:\");\n\n            if (types.basic[type] === undefined) gen\n                            (\"value=types[%i].decode(r,r.uint32())\", i); // can't be groups\n            else gen\n                            (\"value=r.%s()\", type);\n\n            gen\n                            (\"break\")\n                        (\"default:\")\n                            (\"r.skipType(tag2&7)\")\n                            (\"break\")\n                    (\"}\")\n                (\"}\");\n\n            if (types.long[field.keyType] !== undefined) gen\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=value\", ref);\n            else gen\n                (\"%s[k]=value\", ref);\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n\n                (\"if(!(%s&&%s.length))\", ref, ref)\n                    (\"%s=[]\", ref);\n\n            // Packable (always check for forward and backward compatiblity)\n            if (types.packed[type] !== undefined) gen\n                (\"if((t&7)===2){\")\n                    (\"var c2=r.uint32()+r.pos\")\n                    (\"while(r.pos<c2)\")\n                        (\"%s.push(r.%s())\", ref, type)\n                (\"}else\");\n\n            // Non-packed\n            if (types.basic[type] === undefined) gen(field.delimited\n                    ? \"%s.push(types[%i].decode(r,undefined,((t&~7)|4)))\"\n                    : \"%s.push(types[%i].decode(r,r.uint32()))\", ref, i);\n            else gen\n                    (\"%s.push(r.%s())\", ref, type);\n\n        // Non-repeated\n        } else if (types.basic[type] === undefined) gen(field.delimited\n                ? \"%s=types[%i].decode(r,undefined,((t&~7)|4))\"\n                : \"%s=types[%i].decode(r,r.uint32())\", ref, i);\n        else gen\n                (\"%s=r.%s()\", ref, type);\n        gen\n                (\"break\")\n            (\"}\");\n        // Unknown fields\n    } gen\n            (\"default:\")\n                (\"r.skipType(t&7)\")\n                (\"break\")\n\n        (\"}\")\n    (\"}\");\n\n    // Field presence\n    for (i = 0; i < mtype._fieldsArray.length; ++i) {\n        var rfield = mtype._fieldsArray[i];\n        if (rfield.required) gen\n    (\"if(!m.hasOwnProperty(%j))\", rfield.name)\n        (\"throw util.ProtocolError(%j,{instance:m})\", missing(rfield));\n    }\n\n    return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline */\n}\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,IAAI,wGACA,0GACA;AAEJ,SAAS,QAAQ,KAAK;IAClB,OAAO,uBAAuB,MAAM,IAAI,GAAG;AAC/C;AAEA;;;;CAIC,GACD,SAAS,QAAQ,KAAK;IAClB,0CAA0C,GAC1C,IAAI,MAAM,KAAK,OAAO,CAAC;QAAC;QAAK;QAAK;KAAI,EAAE,MAAM,IAAI,GAAG,WACpD,8BACI,sBACJ,sDAAsD,CAAC,MAAM,WAAW,CAAC,MAAM,CAAC,SAAS,KAAK;QAAI,OAAO,MAAM,GAAG;IAAE,GAAG,MAAM,GAAG,aAAa,EAAE,GAC/I,mBACI,oBACA,aACI,SACJ;IAEL,IAAI,IAAI;IACR,MAAO,IAAI,eAAe,GAAG,MAAM,WAAW,CAAC,MAAM,EAAE,EAAE,EAAG;QACxD,IAAI,QAAQ,MAAM,YAAY,CAAC,EAAE,CAAC,OAAO,IACrC,OAAQ,MAAM,YAAY,YAAY,OAAO,UAAU,MAAM,IAAI,EACjE,MAAQ,MAAM,KAAK,QAAQ,CAAC,MAAM,IAAI;QAAG,IACxC,cAAc,MAAM,EAAE;QAE3B,aAAa;QACb,IAAI,MAAM,GAAG,EAAE;YAAE,IACR,6BAA6B,KACzB,SAAS,KACb;YAEL,IAAI,MAAM,QAAQ,CAAC,MAAM,OAAO,CAAC,KAAK,WAAW,IAC5C,QAAQ,MAAM,QAAQ,CAAC,MAAM,OAAO,CAAC;iBACrC,IACA;YAEL,IAAI,MAAM,QAAQ,CAAC,KAAK,KAAK,WAAW,IACnC,YAAY,MAAM,QAAQ,CAAC,KAAK;iBAChC,IACA;YAEL,IACK,oBACI,uBACA,qBACI,2BAA2B,MAAM,OAAO,EACxC;YAEb,IAAI,MAAM,KAAK,CAAC,KAAK,KAAK,WAAW,IACpB,wCAAwC,IAAI,kBAAkB;iBAC1E,IACY,gBAAgB;YAEjC,IACiB,SACJ,YACI,sBACA,SACR,KACJ;YAEL,IAAI,MAAM,IAAI,CAAC,MAAM,OAAO,CAAC,KAAK,WAAW,IACxC,wDAAwD;iBACxD,IACA,eAAe;QAExB,kBAAkB;QAClB,OAAO,IAAI,MAAM,QAAQ,EAAE;YAAE,IAEpB,wBAAwB,KAAK,KACzB,SAAS;YAElB,gEAAgE;YAChE,IAAI,MAAM,MAAM,CAAC,KAAK,KAAK,WAAW,IACjC,kBACI,2BACA,mBACI,mBAAmB,KAAK,MAChC;YAEL,aAAa;YACb,IAAI,MAAM,KAAK,CAAC,KAAK,KAAK,WAAW,IAAI,MAAM,SAAS,GAC9C,sDACA,2CAA2C,KAAK;iBACrD,IACI,mBAAmB,KAAK;QAErC,eAAe;QACf,OAAO,IAAI,MAAM,KAAK,CAAC,KAAK,KAAK,WAAW,IAAI,MAAM,SAAS,GACrD,gDACA,qCAAqC,KAAK;aAC/C,IACI,aAAa,KAAK;QAC3B,IACS,SACJ;IACL,iBAAiB;IACrB;IAAE,IACO,YACI,mBACA,SAER,KACJ;IAED,iBAAiB;IACjB,IAAK,IAAI,GAAG,IAAI,MAAM,YAAY,CAAC,MAAM,EAAE,EAAE,EAAG;QAC5C,IAAI,SAAS,MAAM,YAAY,CAAC,EAAE;QAClC,IAAI,OAAO,QAAQ,EAAE,IACxB,6BAA6B,OAAO,IAAI,EACpC,6CAA6C,QAAQ;IAC1D;IAEA,OAAO,IACN;AACD,yCAAyC,GAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2262, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/verifier.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = verifier;\n\nvar Enum      = require(\"./enum\"),\n    util      = require(\"./util\");\n\nfunction invalid(field, expected) {\n    return field.name + \": \" + expected + (field.repeated && expected !== \"array\" ? \"[]\" : field.map && expected !== \"object\" ? \"{k:\"+field.keyType+\"}\" : \"\") + \" expected\";\n}\n\n/**\n * Generates a partial value verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyValue(gen, field, fieldIndex, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(%s){\", ref)\n                (\"default:\")\n                    (\"return%j\", invalid(field, \"enum value\"));\n            for (var keys = Object.keys(field.resolvedType.values), j = 0; j < keys.length; ++j) gen\n                (\"case %i:\", field.resolvedType.values[keys[j]]);\n            gen\n                    (\"break\")\n            (\"}\");\n        } else {\n            gen\n            (\"{\")\n                (\"var e=types[%i].verify(%s);\", fieldIndex, ref)\n                (\"if(e)\")\n                    (\"return%j+e\", field.name + \".\")\n            (\"}\");\n        }\n    } else {\n        switch (field.type) {\n            case \"int32\":\n            case \"uint32\":\n            case \"sint32\":\n            case \"fixed32\":\n            case \"sfixed32\": gen\n                (\"if(!util.isInteger(%s))\", ref)\n                    (\"return%j\", invalid(field, \"integer\"));\n                break;\n            case \"int64\":\n            case \"uint64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))\", ref, ref, ref, ref)\n                    (\"return%j\", invalid(field, \"integer|Long\"));\n                break;\n            case \"float\":\n            case \"double\": gen\n                (\"if(typeof %s!==\\\"number\\\")\", ref)\n                    (\"return%j\", invalid(field, \"number\"));\n                break;\n            case \"bool\": gen\n                (\"if(typeof %s!==\\\"boolean\\\")\", ref)\n                    (\"return%j\", invalid(field, \"boolean\"));\n                break;\n            case \"string\": gen\n                (\"if(!util.isString(%s))\", ref)\n                    (\"return%j\", invalid(field, \"string\"));\n                break;\n            case \"bytes\": gen\n                (\"if(!(%s&&typeof %s.length===\\\"number\\\"||util.isString(%s)))\", ref, ref, ref)\n                    (\"return%j\", invalid(field, \"buffer\"));\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a partial key verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyKey(gen, field, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    switch (field.keyType) {\n        case \"int32\":\n        case \"uint32\":\n        case \"sint32\":\n        case \"fixed32\":\n        case \"sfixed32\": gen\n            (\"if(!util.key32Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"integer key\"));\n            break;\n        case \"int64\":\n        case \"uint64\":\n        case \"sint64\":\n        case \"fixed64\":\n        case \"sfixed64\": gen\n            (\"if(!util.key64Re.test(%s))\", ref) // see comment above: x is ok, d is not\n                (\"return%j\", invalid(field, \"integer|Long key\"));\n            break;\n        case \"bool\": gen\n            (\"if(!util.key2Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"boolean key\"));\n            break;\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a verifier specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction verifier(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n\n    var gen = util.codegen([\"m\"], mtype.name + \"$verify\")\n    (\"if(typeof m!==\\\"object\\\"||m===null)\")\n        (\"return%j\", \"object expected\");\n    var oneofs = mtype.oneofsArray,\n        seenFirstField = {};\n    if (oneofs.length) gen\n    (\"var p={}\");\n\n    for (var i = 0; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            ref   = \"m\" + util.safeProp(field.name);\n\n        if (field.optional) gen\n        (\"if(%s!=null&&m.hasOwnProperty(%j)){\", ref, field.name); // !== undefined && !== null\n\n        // map fields\n        if (field.map) { gen\n            (\"if(!util.isObject(%s))\", ref)\n                (\"return%j\", invalid(field, \"object\"))\n            (\"var k=Object.keys(%s)\", ref)\n            (\"for(var i=0;i<k.length;++i){\");\n                genVerifyKey(gen, field, \"k[i]\");\n                genVerifyValue(gen, field, i, ref + \"[k[i]]\")\n            (\"}\");\n\n        // repeated fields\n        } else if (field.repeated) { gen\n            (\"if(!Array.isArray(%s))\", ref)\n                (\"return%j\", invalid(field, \"array\"))\n            (\"for(var i=0;i<%s.length;++i){\", ref);\n                genVerifyValue(gen, field, i, ref + \"[i]\")\n            (\"}\");\n\n        // required or present fields\n        } else {\n            if (field.partOf) {\n                var oneofProp = util.safeProp(field.partOf.name);\n                if (seenFirstField[field.partOf.name] === 1) gen\n            (\"if(p%s===1)\", oneofProp)\n                (\"return%j\", field.partOf.name + \": multiple values\");\n                seenFirstField[field.partOf.name] = 1;\n                gen\n            (\"p%s=1\", oneofProp);\n            }\n            genVerifyValue(gen, field, i, ref);\n        }\n        if (field.optional) gen\n        (\"}\");\n    }\n    return gen\n    (\"return null\");\n    /* eslint-enable no-unexpected-multiline */\n}"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,IAAI,wGACA;AAEJ,SAAS,QAAQ,KAAK,EAAE,QAAQ;IAC5B,OAAO,MAAM,IAAI,GAAG,OAAO,WAAW,CAAC,MAAM,QAAQ,IAAI,aAAa,UAAU,OAAO,MAAM,GAAG,IAAI,aAAa,WAAW,QAAM,MAAM,OAAO,GAAC,MAAM,EAAE,IAAI;AAChK;AAEA;;;;;;;;CAQC,GACD,SAAS,eAAe,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAC/C,0CAA0C,GAC1C,IAAI,MAAM,YAAY,EAAE;QACpB,IAAI,MAAM,YAAY,YAAY,MAAM;YAAE,IACrC,eAAe,KACX,YACI,YAAY,QAAQ,OAAO;YACpC,IAAK,IAAI,OAAO,OAAO,IAAI,CAAC,MAAM,YAAY,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG,IAChF,YAAY,MAAM,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACnD,IACS,SACR;QACL,OAAO;YACH,IACC,KACI,+BAA+B,YAAY,KAC3C,SACI,cAAc,MAAM,IAAI,GAAG,KACnC;QACL;IACJ,OAAO;QACH,OAAQ,MAAM,IAAI;YACd,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAY,IACZ,2BAA2B,KACvB,YAAY,QAAQ,OAAO;gBAChC;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAY,IACZ,mFAAmF,KAAK,KAAK,KAAK,KAC9F,YAAY,QAAQ,OAAO;gBAChC;YACJ,KAAK;YACL,KAAK;gBAAU,IACV,8BAA8B,KAC1B,YAAY,QAAQ,OAAO;gBAChC;YACJ,KAAK;gBAAQ,IACR,+BAA+B,KAC3B,YAAY,QAAQ,OAAO;gBAChC;YACJ,KAAK;gBAAU,IACV,0BAA0B,KACtB,YAAY,QAAQ,OAAO;gBAChC;YACJ,KAAK;gBAAS,IACT,+DAA+D,KAAK,KAAK,KACrE,YAAY,QAAQ,OAAO;gBAChC;QACR;IACJ;IACA,OAAO;AACP,yCAAyC,GAC7C;AAEA;;;;;;;CAOC,GACD,SAAS,aAAa,GAAG,EAAE,KAAK,EAAE,GAAG;IACjC,0CAA0C,GAC1C,OAAQ,MAAM,OAAO;QACjB,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAY,IACZ,8BAA8B,KAC1B,YAAY,QAAQ,OAAO;YAChC;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAY,IACZ,8BAA8B,KAAK,uCAAuC;aACtE,YAAY,QAAQ,OAAO;YAChC;QACJ,KAAK;YAAQ,IACR,6BAA6B,KACzB,YAAY,QAAQ,OAAO;YAChC;IACR;IACA,OAAO;AACP,yCAAyC,GAC7C;AAEA;;;;CAIC,GACD,SAAS,SAAS,KAAK;IACnB,0CAA0C,GAE1C,IAAI,MAAM,KAAK,OAAO,CAAC;QAAC;KAAI,EAAE,MAAM,IAAI,GAAG,WAC1C,uCACI,YAAY;IACjB,IAAI,SAAS,MAAM,WAAW,EAC1B,iBAAiB,CAAC;IACtB,IAAI,OAAO,MAAM,EAAE,IAClB;IAED,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,GAAG,MAAM,WAAW,CAAC,MAAM,EAAE,EAAE,EAAG;QACjE,IAAI,QAAQ,MAAM,YAAY,CAAC,EAAE,CAAC,OAAO,IACrC,MAAQ,MAAM,KAAK,QAAQ,CAAC,MAAM,IAAI;QAE1C,IAAI,MAAM,QAAQ,EAAE,IACnB,uCAAuC,KAAK,MAAM,IAAI,GAAG,4BAA4B;QAEtF,aAAa;QACb,IAAI,MAAM,GAAG,EAAE;YAAE,IACZ,0BAA0B,KACtB,YAAY,QAAQ,OAAO,WAC/B,yBAAyB,KACzB;YACG,aAAa,KAAK,OAAO;YACzB,eAAe,KAAK,OAAO,GAAG,MAAM,UACvC;QAEL,kBAAkB;QAClB,OAAO,IAAI,MAAM,QAAQ,EAAE;YAAE,IACxB,0BAA0B,KACtB,YAAY,QAAQ,OAAO,UAC/B,iCAAiC;YAC9B,eAAe,KAAK,OAAO,GAAG,MAAM,OACvC;QAEL,6BAA6B;QAC7B,OAAO;YACH,IAAI,MAAM,MAAM,EAAE;gBACd,IAAI,YAAY,KAAK,QAAQ,CAAC,MAAM,MAAM,CAAC,IAAI;gBAC/C,IAAI,cAAc,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,IAChD,eAAe,WACX,YAAY,MAAM,MAAM,CAAC,IAAI,GAAG;gBACjC,cAAc,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG;gBACpC,IACH,SAAS;YACV;YACA,eAAe,KAAK,OAAO,GAAG;QAClC;QACA,IAAI,MAAM,QAAQ,EAAE,IACnB;IACL;IACA,OAAO,IACN;AACD,yCAAyC,GAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2389, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/converter.js"], "sourcesContent": ["\"use strict\";\n/**\n * Runtime message from/to plain object converters.\n * @namespace\n */\nvar converter = exports;\n\nvar Enum = require(\"./enum\"),\n    util = require(\"./util\");\n\n/**\n * Generates a partial value fromObject conveter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_fromObject(gen, field, fieldIndex, prop) {\n    var defaultAlreadyEmitted = false;\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(d%s){\", prop);\n            for (var values = field.resolvedType.values, keys = Object.keys(values), i = 0; i < keys.length; ++i) {\n                // enum unknown values passthrough\n                if (values[keys[i]] === field.typeDefault && !defaultAlreadyEmitted) { gen\n                    (\"default:\")\n                        (\"if(typeof(d%s)===\\\"number\\\"){m%s=d%s;break}\", prop, prop, prop);\n                    if (!field.repeated) gen // fallback to default value only for\n                                             // arrays, to avoid leaving holes.\n                        (\"break\");           // for non-repeated fields, just ignore\n                    defaultAlreadyEmitted = true;\n                }\n                gen\n                (\"case%j:\", keys[i])\n                (\"case %i:\", values[keys[i]])\n                    (\"m%s=%j\", prop, values[keys[i]])\n                    (\"break\");\n            } gen\n            (\"}\");\n        } else gen\n            (\"if(typeof d%s!==\\\"object\\\")\", prop)\n                (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n            (\"m%s=types[%i].fromObject(d%s)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n                (\"m%s=Number(d%s)\", prop, prop); // also catches \"NaN\", \"Infinity\"\n                break;\n            case \"uint32\":\n            case \"fixed32\": gen\n                (\"m%s=d%s>>>0\", prop, prop);\n                break;\n            case \"int32\":\n            case \"sint32\":\n            case \"sfixed32\": gen\n                (\"m%s=d%s|0\", prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-next-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(util.Long)\")\n                    (\"(m%s=util.Long.fromValue(d%s)).unsigned=%j\", prop, prop, isUnsigned)\n                (\"else if(typeof d%s===\\\"string\\\")\", prop)\n                    (\"m%s=parseInt(d%s,10)\", prop, prop)\n                (\"else if(typeof d%s===\\\"number\\\")\", prop)\n                    (\"m%s=d%s\", prop, prop)\n                (\"else if(typeof d%s===\\\"object\\\")\", prop)\n                    (\"m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)\", prop, prop, prop, isUnsigned ? \"true\" : \"\");\n                break;\n            case \"bytes\": gen\n                (\"if(typeof d%s===\\\"string\\\")\", prop)\n                    (\"util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)\", prop, prop, prop)\n                (\"else if(d%s.length >= 0)\", prop)\n                    (\"m%s=d%s\", prop, prop);\n                break;\n            case \"string\": gen\n                (\"m%s=String(d%s)\", prop, prop);\n                break;\n            case \"bool\": gen\n                (\"m%s=Boolean(d%s)\", prop, prop);\n                break;\n            /* default: gen\n                (\"m%s=d%s\", prop, prop);\n                break; */\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a plain object to runtime message converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.fromObject = function fromObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray;\n    var gen = util.codegen([\"d\"], mtype.name + \"$fromObject\")\n    (\"if(d instanceof this.ctor)\")\n        (\"return d\");\n    if (!fields.length) return gen\n    (\"return new this.ctor\");\n    gen\n    (\"var m=new this.ctor\");\n    for (var i = 0; i < fields.length; ++i) {\n        var field  = fields[i].resolve(),\n            prop   = util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) { gen\n    (\"if(d%s){\", prop)\n        (\"if(typeof d%s!==\\\"object\\\")\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n        (\"m%s={}\", prop)\n        (\"for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[ks[i]]\")\n        (\"}\")\n    (\"}\");\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n    (\"if(d%s){\", prop)\n        (\"if(!Array.isArray(d%s))\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": array expected\")\n        (\"m%s=[]\", prop)\n        (\"for(var i=0;i<d%s.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[i]\")\n        (\"}\")\n    (\"}\");\n\n        // Non-repeated fields\n        } else {\n            if (!(field.resolvedType instanceof Enum)) gen // no need to test for null/undefined if an enum (uses switch)\n    (\"if(d%s!=null){\", prop); // !== undefined && !== null\n        genValuePartial_fromObject(gen, field, /* not sorted */ i, prop);\n            if (!(field.resolvedType instanceof Enum)) gen\n    (\"}\");\n        }\n    } return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n\n/**\n * Generates a partial value toObject converter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_toObject(gen, field, fieldIndex, prop) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) gen\n            (\"d%s=o.enums===String?(types[%i].values[m%s]===undefined?m%s:types[%i].values[m%s]):m%s\", prop, fieldIndex, prop, prop, fieldIndex, prop, prop);\n        else gen\n            (\"d%s=types[%i].toObject(m%s,o)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n            (\"d%s=o.json&&!isFinite(m%s)?String(m%s):m%s\", prop, prop, prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-next-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n            (\"if(typeof m%s===\\\"number\\\")\", prop)\n                (\"d%s=o.longs===String?String(m%s):m%s\", prop, prop, prop)\n            (\"else\") // Long-like\n                (\"d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s\", prop, prop, prop, prop, isUnsigned ? \"true\": \"\", prop);\n                break;\n            case \"bytes\": gen\n            (\"d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s\", prop, prop, prop, prop, prop);\n                break;\n            default: gen\n            (\"d%s=m%s\", prop, prop);\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a runtime message to plain object converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.toObject = function toObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray.slice().sort(util.compareFieldsById);\n    if (!fields.length)\n        return util.codegen()(\"return {}\");\n    var gen = util.codegen([\"m\", \"o\"], mtype.name + \"$toObject\")\n    (\"if(!o)\")\n        (\"o={}\")\n    (\"var d={}\");\n\n    var repeatedFields = [],\n        mapFields = [],\n        normalFields = [],\n        i = 0;\n    for (; i < fields.length; ++i)\n        if (!fields[i].partOf)\n            ( fields[i].resolve().repeated ? repeatedFields\n            : fields[i].map ? mapFields\n            : normalFields).push(fields[i]);\n\n    if (repeatedFields.length) { gen\n    (\"if(o.arrays||o.defaults){\");\n        for (i = 0; i < repeatedFields.length; ++i) gen\n        (\"d%s=[]\", util.safeProp(repeatedFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (mapFields.length) { gen\n    (\"if(o.objects||o.defaults){\");\n        for (i = 0; i < mapFields.length; ++i) gen\n        (\"d%s={}\", util.safeProp(mapFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (normalFields.length) { gen\n    (\"if(o.defaults){\");\n        for (i = 0; i < normalFields.length; ++i) {\n            var field = normalFields[i],\n                prop  = util.safeProp(field.name);\n            if (field.resolvedType instanceof Enum) gen\n        (\"d%s=o.enums===String?%j:%j\", prop, field.resolvedType.valuesById[field.typeDefault], field.typeDefault);\n            else if (field.long) gen\n        (\"if(util.Long){\")\n            (\"var n=new util.Long(%i,%i,%j)\", field.typeDefault.low, field.typeDefault.high, field.typeDefault.unsigned)\n            (\"d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n\", prop)\n        (\"}else\")\n            (\"d%s=o.longs===String?%j:%i\", prop, field.typeDefault.toString(), field.typeDefault.toNumber());\n            else if (field.bytes) {\n                var arrayDefault = \"[\" + Array.prototype.slice.call(field.typeDefault).join(\",\") + \"]\";\n                gen\n        (\"if(o.bytes===String)d%s=%j\", prop, String.fromCharCode.apply(String, field.typeDefault))\n        (\"else{\")\n            (\"d%s=%s\", prop, arrayDefault)\n            (\"if(o.bytes!==Array)d%s=util.newBuffer(d%s)\", prop, prop)\n        (\"}\");\n            } else gen\n        (\"d%s=%j\", prop, field.typeDefault); // also messages (=null)\n        } gen\n    (\"}\");\n    }\n    var hasKs2 = false;\n    for (i = 0; i < fields.length; ++i) {\n        var field = fields[i],\n            index = mtype._fieldsArray.indexOf(field),\n            prop  = util.safeProp(field.name);\n        if (field.map) {\n            if (!hasKs2) { hasKs2 = true; gen\n    (\"var ks2\");\n            } gen\n    (\"if(m%s&&(ks2=Object.keys(m%s)).length){\", prop, prop)\n        (\"d%s={}\", prop)\n        (\"for(var j=0;j<ks2.length;++j){\");\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[ks2[j]]\")\n        (\"}\");\n        } else if (field.repeated) { gen\n    (\"if(m%s&&m%s.length){\", prop, prop)\n        (\"d%s=[]\", prop)\n        (\"for(var j=0;j<m%s.length;++j){\", prop);\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[j]\")\n        (\"}\");\n        } else { gen\n    (\"if(m%s!=null&&m.hasOwnProperty(%j)){\", prop, field.name); // !== undefined && !== null\n        genValuePartial_toObject(gen, field, /* sorted */ index, prop);\n        if (field.partOf) gen\n        (\"if(o.oneofs)\")\n            (\"d%s=%j\", util.safeProp(field.partOf.name), field.name);\n        }\n        gen\n    (\"}\");\n    }\n    return gen\n    (\"return d\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n"], "names": [], "mappings": "AAAA;AACA;;;CAGC,GACD,IAAI,YAAY;AAEhB,IAAI,wGACA;AAEJ;;;;;;;;CAQC,GACD,SAAS,2BAA2B,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI;IAC5D,IAAI,wBAAwB;IAC5B,0EAA0E,GAC1E,IAAI,MAAM,YAAY,EAAE;QACpB,IAAI,MAAM,YAAY,YAAY,MAAM;YAAE,IACrC,gBAAgB;YACjB,IAAK,IAAI,SAAS,MAAM,YAAY,CAAC,MAAM,EAAE,OAAO,OAAO,IAAI,CAAC,SAAS,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;gBAClG,kCAAkC;gBAClC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,MAAM,WAAW,IAAI,CAAC,uBAAuB;oBAAE,IAClE,YACI,+CAA+C,MAAM,MAAM;oBAChE,IAAI,CAAC,MAAM,QAAQ,EAAE,IAAI,qCAAqC;qBAEzD,UAAoB,uCAAuC;oBAChE,wBAAwB;gBAC5B;gBACA,IACC,WAAW,IAAI,CAAC,EAAE,EAClB,YAAY,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EACvB,UAAU,MAAM,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAC/B;YACT;YAAE,IACD;QACL,OAAO,IACF,+BAA+B,MAC3B,uBAAuB,MAAM,QAAQ,GAAG,qBAC5C,iCAAiC,MAAM,YAAY;IAC5D,OAAO;QACH,IAAI,aAAa;QACjB,OAAQ,MAAM,IAAI;YACd,KAAK;YACL,KAAK;gBAAS,IACT,mBAAmB,MAAM,OAAO,iCAAiC;gBAClE;YACJ,KAAK;YACL,KAAK;gBAAW,IACX,eAAe,MAAM;gBACtB;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBAAY,IACZ,aAAa,MAAM;gBACpB;YACJ,KAAK;gBACD,aAAa;YACb,0CAA0C;YAC9C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAY,IACZ,iBACI,8CAA8C,MAAM,MAAM,YAC9D,oCAAoC,MAChC,wBAAwB,MAAM,MAClC,oCAAoC,MAChC,WAAW,MAAM,MACrB,oCAAoC,MAChC,gEAAgE,MAAM,MAAM,MAAM,aAAa,SAAS;gBAC7G;YACJ,KAAK;gBAAS,IACT,+BAA+B,MAC3B,yEAAyE,MAAM,MAAM,MACzF,4BAA4B,MACxB,WAAW,MAAM;gBACtB;YACJ,KAAK;gBAAU,IACV,mBAAmB,MAAM;gBAC1B;YACJ,KAAK;gBAAQ,IACR,oBAAoB,MAAM;gBAC3B;QAIR;IACJ;IACA,OAAO;AACP,yEAAyE,GAC7E;AAEA;;;;CAIC,GACD,UAAU,UAAU,GAAG,SAAS,WAAW,KAAK;IAC5C,0EAA0E,GAC1E,IAAI,SAAS,MAAM,WAAW;IAC9B,IAAI,MAAM,KAAK,OAAO,CAAC;QAAC;KAAI,EAAE,MAAM,IAAI,GAAG,eAC1C,8BACI;IACL,IAAI,CAAC,OAAO,MAAM,EAAE,OAAO,IAC1B;IACD,IACC;IACD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;QACpC,IAAI,QAAS,MAAM,CAAC,EAAE,CAAC,OAAO,IAC1B,OAAS,KAAK,QAAQ,CAAC,MAAM,IAAI;QAErC,aAAa;QACb,IAAI,MAAM,GAAG,EAAE;YAAE,IACpB,YAAY,MACR,+BAA+B,MAC3B,uBAAuB,MAAM,QAAQ,GAAG,qBAC5C,UAAU,MACV,qDAAqD;YAClD,2BAA2B,KAAK,OAAO,cAAc,GAAG,GAAG,OAAO,WACrE,KACJ;QAEG,kBAAkB;QAClB,OAAO,IAAI,MAAM,QAAQ,EAAE;YAAE,IAChC,YAAY,MACR,2BAA2B,MACvB,uBAAuB,MAAM,QAAQ,GAAG,oBAC5C,UAAU,MACV,kCAAkC;YAC/B,2BAA2B,KAAK,OAAO,cAAc,GAAG,GAAG,OAAO,OACrE,KACJ;QAEG,sBAAsB;QACtB,OAAO;YACH,IAAI,CAAC,CAAC,MAAM,YAAY,YAAY,IAAI,GAAG,IAAI,8DAA8D;aACpH,kBAAkB,OAAO,4BAA4B;YAClD,2BAA2B,KAAK,OAAO,cAAc,GAAG,GAAG;YACvD,IAAI,CAAC,CAAC,MAAM,YAAY,YAAY,IAAI,GAAG,IAClD;QACG;IACJ;IAAE,OAAO,IACR;AACD,yEAAyE,GAC7E;AAEA;;;;;;;;CAQC,GACD,SAAS,yBAAyB,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI;IAC1D,0EAA0E,GAC1E,IAAI,MAAM,YAAY,EAAE;QACpB,IAAI,MAAM,YAAY,YAAY,MAAM,IACnC,0FAA0F,MAAM,YAAY,MAAM,MAAM,YAAY,MAAM;aAC1I,IACA,iCAAiC,MAAM,YAAY;IAC5D,OAAO;QACH,IAAI,aAAa;QACjB,OAAQ,MAAM,IAAI;YACd,KAAK;YACL,KAAK;gBAAS,IACb,8CAA8C,MAAM,MAAM,MAAM;gBAC7D;YACJ,KAAK;gBACD,aAAa;YACb,0CAA0C;YAC9C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAY,IAChB,+BAA+B,MAC3B,wCAAwC,MAAM,MAAM,MACxD,QAAQ,YAAY;iBAChB,6IAA6I,MAAM,MAAM,MAAM,MAAM,aAAa,SAAQ,IAAI;gBAC/L;YACJ,KAAK;gBAAS,IACb,iHAAiH,MAAM,MAAM,MAAM,MAAM;gBACtI;YACJ;gBAAS,IACR,WAAW,MAAM;gBACd;QACR;IACJ;IACA,OAAO;AACP,yEAAyE,GAC7E;AAEA;;;;CAIC,GACD,UAAU,QAAQ,GAAG,SAAS,SAAS,KAAK;IACxC,0EAA0E,GAC1E,IAAI,SAAS,MAAM,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,iBAAiB;IAClE,IAAI,CAAC,OAAO,MAAM,EACd,OAAO,KAAK,OAAO,GAAG;IAC1B,IAAI,MAAM,KAAK,OAAO,CAAC;QAAC;QAAK;KAAI,EAAE,MAAM,IAAI,GAAG,aAC/C,UACI,QACJ;IAED,IAAI,iBAAiB,EAAE,EACnB,YAAY,EAAE,EACd,eAAe,EAAE,EACjB,IAAI;IACR,MAAO,IAAI,OAAO,MAAM,EAAE,EAAE,EACxB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EACjB,CAAE,MAAM,CAAC,EAAE,CAAC,OAAO,GAAG,QAAQ,GAAG,iBAC/B,MAAM,CAAC,EAAE,CAAC,GAAG,GAAG,YAChB,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;IAEtC,IAAI,eAAe,MAAM,EAAE;QAAE,IAC5B;QACG,IAAK,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,EAAE,EAAG,IAC3C,UAAU,KAAK,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI;QAC/C,IACH;IACD;IAEA,IAAI,UAAU,MAAM,EAAE;QAAE,IACvB;QACG,IAAK,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,EAAE,EAAG,IACtC,UAAU,KAAK,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI;QAC1C,IACH;IACD;IAEA,IAAI,aAAa,MAAM,EAAE;QAAE,IAC1B;QACG,IAAK,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,EAAE,EAAG;YACtC,IAAI,QAAQ,YAAY,CAAC,EAAE,EACvB,OAAQ,KAAK,QAAQ,CAAC,MAAM,IAAI;YACpC,IAAI,MAAM,YAAY,YAAY,MAAM,IAC3C,8BAA8B,MAAM,MAAM,YAAY,CAAC,UAAU,CAAC,MAAM,WAAW,CAAC,EAAE,MAAM,WAAW;iBAC/F,IAAI,MAAM,IAAI,EAAE,IACxB,kBACI,iCAAiC,MAAM,WAAW,CAAC,GAAG,EAAE,MAAM,WAAW,CAAC,IAAI,EAAE,MAAM,WAAW,CAAC,QAAQ,EAC1G,qEAAqE,MACzE,SACI,8BAA8B,MAAM,MAAM,WAAW,CAAC,QAAQ,IAAI,MAAM,WAAW,CAAC,QAAQ;iBACxF,IAAI,MAAM,KAAK,EAAE;gBAClB,IAAI,eAAe,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,WAAW,EAAE,IAAI,CAAC,OAAO;gBACnF,IACP,8BAA8B,MAAM,OAAO,YAAY,CAAC,KAAK,CAAC,QAAQ,MAAM,WAAW,GACvF,SACI,UAAU,MAAM,cAChB,8CAA8C,MAAM,MACxD;YACG,OAAO,IACV,UAAU,MAAM,MAAM,WAAW,GAAG,wBAAwB;QAC7D;QAAE,IACL;IACD;IACA,IAAI,SAAS;IACb,IAAK,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;QAChC,IAAI,QAAQ,MAAM,CAAC,EAAE,EACjB,QAAQ,MAAM,YAAY,CAAC,OAAO,CAAC,QACnC,OAAQ,KAAK,QAAQ,CAAC,MAAM,IAAI;QACpC,IAAI,MAAM,GAAG,EAAE;YACX,IAAI,CAAC,QAAQ;gBAAE,SAAS;gBAAM,IACrC;YACO;YAAE,IACT,2CAA2C,MAAM,MAC7C,UAAU,MACV;YACG,yBAAyB,KAAK,OAAO,UAAU,GAAG,OAAO,OAAO,YACnE;QACD,OAAO,IAAI,MAAM,QAAQ,EAAE;YAAE,IAChC,wBAAwB,MAAM,MAC1B,UAAU,MACV,kCAAkC;YAC/B,yBAAyB,KAAK,OAAO,UAAU,GAAG,OAAO,OAAO,OACnE;QACD,OAAO;YAAE,IACZ,wCAAwC,MAAM,MAAM,IAAI,GAAG,4BAA4B;YACpF,yBAAyB,KAAK,OAAO,UAAU,GAAG,OAAO;YACzD,IAAI,MAAM,MAAM,EAAE,IACjB,gBACI,UAAU,KAAK,QAAQ,CAAC,MAAM,MAAM,CAAC,IAAI,GAAG,MAAM,IAAI;QAC3D;QACA,IACH;IACD;IACA,OAAO,IACN;AACD,yEAAyE,GAC7E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2591, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/wrappers.js"], "sourcesContent": ["\"use strict\";\n\n/**\n * Wrappers for common types.\n * @type {Object.<string,IWrapper>}\n * @const\n */\nvar wrappers = exports;\n\nvar Message = require(\"./message\");\n\n/**\n * From object converter part of an {@link IWrapper}.\n * @typedef WrapperFromObjectConverter\n * @type {function}\n * @param {Object.<string,*>} object Plain object\n * @returns {Message<{}>} Message instance\n * @this Type\n */\n\n/**\n * To object converter part of an {@link IWrapper}.\n * @typedef WrapperToObjectConverter\n * @type {function}\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @this Type\n */\n\n/**\n * Common type wrapper part of {@link wrappers}.\n * @interface IWrapper\n * @property {WrapperFromObjectConverter} [fromObject] From object converter\n * @property {WrapperToObjectConverter} [toObject] To object converter\n */\n\n// Custom wrapper for Any\nwrappers[\".google.protobuf.Any\"] = {\n\n    fromObject: function(object) {\n\n        // unwrap value type if mapped\n        if (object && object[\"@type\"]) {\n             // Only use fully qualified type name after the last '/'\n            var name = object[\"@type\"].substring(object[\"@type\"].lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type) {\n                // type_url does not accept leading \".\"\n                var type_url = object[\"@type\"].charAt(0) === \".\" ?\n                    object[\"@type\"].slice(1) : object[\"@type\"];\n                // type_url prefix is optional, but path seperator is required\n                if (type_url.indexOf(\"/\") === -1) {\n                    type_url = \"/\" + type_url;\n                }\n                return this.create({\n                    type_url: type_url,\n                    value: type.encode(type.fromObject(object)).finish()\n                });\n            }\n        }\n\n        return this.fromObject(object);\n    },\n\n    toObject: function(message, options) {\n\n        // Default prefix\n        var googleApi = \"type.googleapis.com/\";\n        var prefix = \"\";\n        var name = \"\";\n\n        // decode value if requested and unmapped\n        if (options && options.json && message.type_url && message.value) {\n            // Only use fully qualified type name after the last '/'\n            name = message.type_url.substring(message.type_url.lastIndexOf(\"/\") + 1);\n            // Separate the prefix used\n            prefix = message.type_url.substring(0, message.type_url.lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type)\n                message = type.decode(message.value);\n        }\n\n        // wrap value if unmapped\n        if (!(message instanceof this.ctor) && message instanceof Message) {\n            var object = message.$type.toObject(message, options);\n            var messageName = message.$type.fullName[0] === \".\" ?\n                message.$type.fullName.slice(1) : message.$type.fullName;\n            // Default to type.googleapis.com prefix if no prefix is used\n            if (prefix === \"\") {\n                prefix = googleApi;\n            }\n            name = prefix + messageName;\n            object[\"@type\"] = name;\n            return object;\n        }\n\n        return this.toObject(message, options);\n    }\n};\n"], "names": [], "mappings": "AAAA;AAEA;;;;CAIC,GACD,IAAI,WAAW;AAEf,IAAI;AAEJ;;;;;;;CAOC,GAED;;;;;;;;CAQC,GAED;;;;;CAKC,GAED,yBAAyB;AACzB,QAAQ,CAAC,uBAAuB,GAAG;IAE/B,YAAY,SAAS,MAAM;QAEvB,8BAA8B;QAC9B,IAAI,UAAU,MAAM,CAAC,QAAQ,EAAE;YAC1B,wDAAwD;YACzD,IAAI,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO;YACxE,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;YACvB,wBAAwB,GACxB,IAAI,MAAM;gBACN,uCAAuC;gBACvC,IAAI,WAAW,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,MACzC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,QAAQ;gBAC9C,8DAA8D;gBAC9D,IAAI,SAAS,OAAO,CAAC,SAAS,CAAC,GAAG;oBAC9B,WAAW,MAAM;gBACrB;gBACA,OAAO,IAAI,CAAC,MAAM,CAAC;oBACf,UAAU;oBACV,OAAO,KAAK,MAAM,CAAC,KAAK,UAAU,CAAC,SAAS,MAAM;gBACtD;YACJ;QACJ;QAEA,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B;IAEA,UAAU,SAAS,OAAO,EAAE,OAAO;QAE/B,iBAAiB;QACjB,IAAI,YAAY;QAChB,IAAI,SAAS;QACb,IAAI,OAAO;QAEX,yCAAyC;QACzC,IAAI,WAAW,QAAQ,IAAI,IAAI,QAAQ,QAAQ,IAAI,QAAQ,KAAK,EAAE;YAC9D,wDAAwD;YACxD,OAAO,QAAQ,QAAQ,CAAC,SAAS,CAAC,QAAQ,QAAQ,CAAC,WAAW,CAAC,OAAO;YACtE,2BAA2B;YAC3B,SAAS,QAAQ,QAAQ,CAAC,SAAS,CAAC,GAAG,QAAQ,QAAQ,CAAC,WAAW,CAAC,OAAO;YAC3E,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;YACvB,wBAAwB,GACxB,IAAI,MACA,UAAU,KAAK,MAAM,CAAC,QAAQ,KAAK;QAC3C;QAEA,yBAAyB;QACzB,IAAI,CAAC,CAAC,mBAAmB,IAAI,CAAC,IAAI,KAAK,mBAAmB,SAAS;YAC/D,IAAI,SAAS,QAAQ,KAAK,CAAC,QAAQ,CAAC,SAAS;YAC7C,IAAI,cAAc,QAAQ,KAAK,CAAC,QAAQ,CAAC,EAAE,KAAK,MAC5C,QAAQ,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,QAAQ,KAAK,CAAC,QAAQ;YAC5D,6DAA6D;YAC7D,IAAI,WAAW,IAAI;gBACf,SAAS;YACb;YACA,OAAO,SAAS;YAChB,MAAM,CAAC,QAAQ,GAAG;YAClB,OAAO;QACX;QAEA,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS;IAClC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2675, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/type.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = Type;\n\n// extends Namespace\nvar Namespace = require(\"./namespace\");\n((Type.prototype = Object.create(Namespace.prototype)).constructor = Type).className = \"Type\";\n\nvar Enum      = require(\"./enum\"),\n    OneOf     = require(\"./oneof\"),\n    Field     = require(\"./field\"),\n    MapField  = require(\"./mapfield\"),\n    Service   = require(\"./service\"),\n    Message   = require(\"./message\"),\n    Reader    = require(\"./reader\"),\n    Writer    = require(\"./writer\"),\n    util      = require(\"./util\"),\n    encoder   = require(\"./encoder\"),\n    decoder   = require(\"./decoder\"),\n    verifier  = require(\"./verifier\"),\n    converter = require(\"./converter\"),\n    wrappers  = require(\"./wrappers\");\n\n/**\n * Constructs a new reflected message type instance.\n * @classdesc Reflected message type.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Message name\n * @param {Object.<string,*>} [options] Declared options\n */\nfunction Type(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Message fields.\n     * @type {Object.<string,Field>}\n     */\n    this.fields = {};  // toJSON, marker\n\n    /**\n     * Oneofs declared within this namespace, if any.\n     * @type {Object.<string,OneOf>}\n     */\n    this.oneofs = undefined; // toJSON\n\n    /**\n     * Extension ranges, if any.\n     * @type {number[][]}\n     */\n    this.extensions = undefined; // toJSON\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    /*?\n     * Whether this type is a legacy group.\n     * @type {boolean|undefined}\n     */\n    this.group = undefined; // toJSON\n\n    /**\n     * Cached fields by id.\n     * @type {Object.<number,Field>|null}\n     * @private\n     */\n    this._fieldsById = null;\n\n    /**\n     * Cached fields as an array.\n     * @type {Field[]|null}\n     * @private\n     */\n    this._fieldsArray = null;\n\n    /**\n     * Cached oneofs as an array.\n     * @type {OneOf[]|null}\n     * @private\n     */\n    this._oneofsArray = null;\n\n    /**\n     * Cached constructor.\n     * @type {Constructor<{}>}\n     * @private\n     */\n    this._ctor = null;\n}\n\nObject.defineProperties(Type.prototype, {\n\n    /**\n     * Message fields by id.\n     * @name Type#fieldsById\n     * @type {Object.<number,Field>}\n     * @readonly\n     */\n    fieldsById: {\n        get: function() {\n\n            /* istanbul ignore if */\n            if (this._fieldsById)\n                return this._fieldsById;\n\n            this._fieldsById = {};\n            for (var names = Object.keys(this.fields), i = 0; i < names.length; ++i) {\n                var field = this.fields[names[i]],\n                    id = field.id;\n\n                /* istanbul ignore if */\n                if (this._fieldsById[id])\n                    throw Error(\"duplicate id \" + id + \" in \" + this);\n\n                this._fieldsById[id] = field;\n            }\n            return this._fieldsById;\n        }\n    },\n\n    /**\n     * Fields of this message as an array for iteration.\n     * @name Type#fieldsArray\n     * @type {Field[]}\n     * @readonly\n     */\n    fieldsArray: {\n        get: function() {\n            return this._fieldsArray || (this._fieldsArray = util.toArray(this.fields));\n        }\n    },\n\n    /**\n     * Oneofs of this message as an array for iteration.\n     * @name Type#oneofsArray\n     * @type {OneOf[]}\n     * @readonly\n     */\n    oneofsArray: {\n        get: function() {\n            return this._oneofsArray || (this._oneofsArray = util.toArray(this.oneofs));\n        }\n    },\n\n    /**\n     * The registered constructor, if any registered, otherwise a generic constructor.\n     * Assigning a function replaces the internal constructor. If the function does not extend {@link Message} yet, its prototype will be setup accordingly and static methods will be populated. If it already extends {@link Message}, it will just replace the internal constructor.\n     * @name Type#ctor\n     * @type {Constructor<{}>}\n     */\n    ctor: {\n        get: function() {\n            return this._ctor || (this.ctor = Type.generateConstructor(this)());\n        },\n        set: function(ctor) {\n\n            // Ensure proper prototype\n            var prototype = ctor.prototype;\n            if (!(prototype instanceof Message)) {\n                (ctor.prototype = new Message()).constructor = ctor;\n                util.merge(ctor.prototype, prototype);\n            }\n\n            // Classes and messages reference their reflected type\n            ctor.$type = ctor.prototype.$type = this;\n\n            // Mix in static methods\n            util.merge(ctor, Message, true);\n\n            this._ctor = ctor;\n\n            // Messages have non-enumerable default values on their prototype\n            var i = 0;\n            for (; i < /* initializes */ this.fieldsArray.length; ++i)\n                this._fieldsArray[i].resolve(); // ensures a proper value\n\n            // Messages have non-enumerable getters and setters for each virtual oneof field\n            var ctorProperties = {};\n            for (i = 0; i < /* initializes */ this.oneofsArray.length; ++i)\n                ctorProperties[this._oneofsArray[i].resolve().name] = {\n                    get: util.oneOfGetter(this._oneofsArray[i].oneof),\n                    set: util.oneOfSetter(this._oneofsArray[i].oneof)\n                };\n            if (i)\n                Object.defineProperties(ctor.prototype, ctorProperties);\n        }\n    }\n});\n\n/**\n * Generates a constructor function for the specified type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nType.generateConstructor = function generateConstructor(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"p\"], mtype.name);\n    // explicitly initialize mutable object/array fields so that these aren't just inherited from the prototype\n    for (var i = 0, field; i < mtype.fieldsArray.length; ++i)\n        if ((field = mtype._fieldsArray[i]).map) gen\n            (\"this%s={}\", util.safeProp(field.name));\n        else if (field.repeated) gen\n            (\"this%s=[]\", util.safeProp(field.name));\n    return gen\n    (\"if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)\") // omit undefined or null\n        (\"this[ks[i]]=p[ks[i]]\");\n    /* eslint-enable no-unexpected-multiline */\n};\n\nfunction clearCache(type) {\n    type._fieldsById = type._fieldsArray = type._oneofsArray = null;\n    delete type.encode;\n    delete type.decode;\n    delete type.verify;\n    return type;\n}\n\n/**\n * Message type descriptor.\n * @interface IType\n * @extends INamespace\n * @property {Object.<string,IOneOf>} [oneofs] Oneof descriptors\n * @property {Object.<string,IField>} fields Field descriptors\n * @property {number[][]} [extensions] Extension ranges\n * @property {Array.<number[]|string>} [reserved] Reserved ranges\n * @property {boolean} [group=false] Whether a legacy group or not\n */\n\n/**\n * Creates a message type from a message type descriptor.\n * @param {string} name Message name\n * @param {IType} json Message type descriptor\n * @returns {Type} Created message type\n */\nType.fromJSON = function fromJSON(name, json) {\n    var type = new Type(name, json.options);\n    type.extensions = json.extensions;\n    type.reserved = json.reserved;\n    var names = Object.keys(json.fields),\n        i = 0;\n    for (; i < names.length; ++i)\n        type.add(\n            ( typeof json.fields[names[i]].keyType !== \"undefined\"\n            ? MapField.fromJSON\n            : Field.fromJSON )(names[i], json.fields[names[i]])\n        );\n    if (json.oneofs)\n        for (names = Object.keys(json.oneofs), i = 0; i < names.length; ++i)\n            type.add(OneOf.fromJSON(names[i], json.oneofs[names[i]]));\n    if (json.nested)\n        for (names = Object.keys(json.nested), i = 0; i < names.length; ++i) {\n            var nested = json.nested[names[i]];\n            type.add( // most to least likely\n                ( nested.id !== undefined\n                ? Field.fromJSON\n                : nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    if (json.extensions && json.extensions.length)\n        type.extensions = json.extensions;\n    if (json.reserved && json.reserved.length)\n        type.reserved = json.reserved;\n    if (json.group)\n        type.group = true;\n    if (json.comment)\n        type.comment = json.comment;\n    if (json.edition)\n        type._edition = json.edition;\n    type._defaultEdition = \"proto3\";  // For backwards-compatibility.\n    return type;\n};\n\n/**\n * Converts this message type to a message type descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IType} Message type descriptor\n */\nType.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"edition\"    , this._editionToJSON(),\n        \"options\"    , inherited && inherited.options || undefined,\n        \"oneofs\"     , Namespace.arrayToJSON(this.oneofsArray, toJSONOptions),\n        \"fields\"     , Namespace.arrayToJSON(this.fieldsArray.filter(function(obj) { return !obj.declaringField; }), toJSONOptions) || {},\n        \"extensions\" , this.extensions && this.extensions.length ? this.extensions : undefined,\n        \"reserved\"   , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"group\"      , this.group || undefined,\n        \"nested\"     , inherited && inherited.nested || undefined,\n        \"comment\"    , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nType.prototype.resolveAll = function resolveAll() {\n    if (!this._needsRecursiveResolve) return this;\n\n    Namespace.prototype.resolveAll.call(this);\n    var oneofs = this.oneofsArray; i = 0;\n    while (i < oneofs.length)\n        oneofs[i++].resolve();\n    var fields = this.fieldsArray, i = 0;\n    while (i < fields.length)\n        fields[i++].resolve();\n    return this;\n};\n\n/**\n * @override\n */\nType.prototype._resolveFeaturesRecursive = function _resolveFeaturesRecursive(edition) {\n    if (!this._needsRecursiveFeatureResolution) return this;\n\n    edition = this._edition || edition;\n\n    Namespace.prototype._resolveFeaturesRecursive.call(this, edition);\n    this.oneofsArray.forEach(oneof => {\n        oneof._resolveFeatures(edition);\n    });\n    this.fieldsArray.forEach(field => {\n        field._resolveFeatures(edition);\n    });\n    return this;\n};\n\n/**\n * @override\n */\nType.prototype.get = function get(name) {\n    return this.fields[name]\n        || this.oneofs && this.oneofs[name]\n        || this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Adds a nested object to this type.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name or, if a field, when there is already a field with this id\n */\nType.prototype.add = function add(object) {\n\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Field && object.extend === undefined) {\n        // NOTE: Extension fields aren't actual fields on the declaring type, but nested objects.\n        // The root object takes care of adding distinct sister-fields to the respective extended\n        // type instead.\n\n        // avoids calling the getter if not absolutely necessary because it's called quite frequently\n        if (this._fieldsById ? /* istanbul ignore next */ this._fieldsById[object.id] : this.fieldsById[object.id])\n            throw Error(\"duplicate id \" + object.id + \" in \" + this);\n        if (this.isReservedId(object.id))\n            throw Error(\"id \" + object.id + \" is reserved in \" + this);\n        if (this.isReservedName(object.name))\n            throw Error(\"name '\" + object.name + \"' is reserved in \" + this);\n\n        if (object.parent)\n            object.parent.remove(object);\n        this.fields[object.name] = object;\n        object.message = this;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n        if (!this.oneofs)\n            this.oneofs = {};\n        this.oneofs[object.name] = object;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * Removes a nested object from this type.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this type\n */\nType.prototype.remove = function remove(object) {\n    if (object instanceof Field && object.extend === undefined) {\n        // See Type#add for the reason why extension fields are excluded here.\n\n        /* istanbul ignore if */\n        if (!this.fields || this.fields[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.fields[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n\n        /* istanbul ignore if */\n        if (!this.oneofs || this.oneofs[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.oneofs[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<{}>} Message instance\n */\nType.prototype.create = function create(properties) {\n    return new this.ctor(properties);\n};\n\n/**\n * Sets up {@link Type#encode|encode}, {@link Type#decode|decode} and {@link Type#verify|verify}.\n * @returns {Type} `this`\n */\nType.prototype.setup = function setup() {\n    // Sets up everything at once so that the prototype chain does not have to be re-evaluated\n    // multiple times (V8, soft-deopt prototype-check).\n\n    var fullName = this.fullName,\n        types    = [];\n    for (var i = 0; i < /* initializes */ this.fieldsArray.length; ++i)\n        types.push(this._fieldsArray[i].resolve().resolvedType);\n\n    // Replace setup methods with type-specific generated functions\n    this.encode = encoder(this)({\n        Writer : Writer,\n        types  : types,\n        util   : util\n    });\n    this.decode = decoder(this)({\n        Reader : Reader,\n        types  : types,\n        util   : util\n    });\n    this.verify = verifier(this)({\n        types : types,\n        util  : util\n    });\n    this.fromObject = converter.fromObject(this)({\n        types : types,\n        util  : util\n    });\n    this.toObject = converter.toObject(this)({\n        types : types,\n        util  : util\n    });\n\n    // Inject custom wrappers for common types\n    var wrapper = wrappers[fullName];\n    if (wrapper) {\n        var originalThis = Object.create(this);\n        // if (wrapper.fromObject) {\n            originalThis.fromObject = this.fromObject;\n            this.fromObject = wrapper.fromObject.bind(originalThis);\n        // }\n        // if (wrapper.toObject) {\n            originalThis.toObject = this.toObject;\n            this.toObject = wrapper.toObject.bind(originalThis);\n        // }\n    }\n\n    return this;\n};\n\n/**\n * Encodes a message of this type. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encode = function encode_setup(message, writer) {\n    return this.setup().encode(message, writer); // overrides this method\n};\n\n/**\n * Encodes a message of this type preceeded by its byte length as a varint. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.encode(message, writer && writer.len ? writer.fork() : writer).ldelim();\n};\n\n/**\n * Decodes a message of this type.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @param {number} [length] Length of the message, if known beforehand\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError<{}>} If required fields are missing\n */\nType.prototype.decode = function decode_setup(reader, length) {\n    return this.setup().decode(reader, length); // overrides this method\n};\n\n/**\n * Decodes a message of this type preceeded by its byte length as a varint.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError} If required fields are missing\n */\nType.prototype.decodeDelimited = function decodeDelimited(reader) {\n    if (!(reader instanceof Reader))\n        reader = Reader.create(reader);\n    return this.decode(reader, reader.uint32());\n};\n\n/**\n * Verifies that field values are valid and that required fields are present.\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {null|string} `null` if valid, otherwise the reason why it is not\n */\nType.prototype.verify = function verify_setup(message) {\n    return this.setup().verify(message); // overrides this method\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object to convert\n * @returns {Message<{}>} Message instance\n */\nType.prototype.fromObject = function fromObject(object) {\n    return this.setup().fromObject(object);\n};\n\n/**\n * Conversion options as used by {@link Type#toObject} and {@link Message.toObject}.\n * @interface IConversionOptions\n * @property {Function} [longs] Long conversion type.\n * Valid values are `String` and `Number` (the global types).\n * Defaults to copy the present value, which is a possibly unsafe number without and a {@link Long} with a long library.\n * @property {Function} [enums] Enum value conversion type.\n * Only valid value is `String` (the global type).\n * Defaults to copy the present value, which is the numeric id.\n * @property {Function} [bytes] Bytes value conversion type.\n * Valid values are `Array` and (a base64 encoded) `String` (the global types).\n * Defaults to copy the present value, which usually is a Buffer under node and an Uint8Array in the browser.\n * @property {boolean} [defaults=false] Also sets default values on the resulting object\n * @property {boolean} [arrays=false] Sets empty arrays for missing repeated fields even if `defaults=false`\n * @property {boolean} [objects=false] Sets empty objects for missing map fields even if `defaults=false`\n * @property {boolean} [oneofs=false] Includes virtual oneof properties set to the present field's name, if any\n * @property {boolean} [json=false] Performs additional JSON compatibility conversions, i.e. NaN and Infinity to strings\n */\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n */\nType.prototype.toObject = function toObject(message, options) {\n    return this.setup().toObject(message, options);\n};\n\n/**\n * Decorator function as returned by {@link Type.d} (TypeScript).\n * @typedef TypeDecorator\n * @type {function}\n * @param {Constructor<T>} target Target constructor\n * @returns {undefined}\n * @template T extends Message<T>\n */\n\n/**\n * Type decorator (TypeScript).\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {TypeDecorator<T>} Decorator function\n * @template T extends Message<T>\n */\nType.d = function decorateType(typeName) {\n    return function typeDecorator(target) {\n        util.decorateType(target, typeName);\n    };\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,oBAAoB;AACpB,IAAI;AACJ,CAAC,CAAC,KAAK,SAAS,GAAG,OAAO,MAAM,CAAC,UAAU,SAAS,CAAC,EAAE,WAAW,GAAG,IAAI,EAAE,SAAS,GAAG;AAEvF,IAAI,wGACA,0GACA,0GACA,gHACA,8GACA,8GACA,4GACA,4GACA,wGACA,8GACA,8GACA,gHACA,kHACA;AAEJ;;;;;;;CAOC,GACD,SAAS,KAAK,IAAI,EAAE,OAAO;IACvB,UAAU,IAAI,CAAC,IAAI,EAAE,MAAM;IAE3B;;;KAGC,GACD,IAAI,CAAC,MAAM,GAAG,CAAC,GAAI,iBAAiB;IAEpC;;;KAGC,GACD,IAAI,CAAC,MAAM,GAAG,WAAW,SAAS;IAElC;;;KAGC,GACD,IAAI,CAAC,UAAU,GAAG,WAAW,SAAS;IAEtC;;;KAGC,GACD,IAAI,CAAC,QAAQ,GAAG,WAAW,SAAS;IAEpC;;;KAGC,GACD,IAAI,CAAC,KAAK,GAAG,WAAW,SAAS;IAEjC;;;;KAIC,GACD,IAAI,CAAC,WAAW,GAAG;IAEnB;;;;KAIC,GACD,IAAI,CAAC,YAAY,GAAG;IAEpB;;;;KAIC,GACD,IAAI,CAAC,YAAY,GAAG;IAEpB;;;;KAIC,GACD,IAAI,CAAC,KAAK,GAAG;AACjB;AAEA,OAAO,gBAAgB,CAAC,KAAK,SAAS,EAAE;IAEpC;;;;;KAKC,GACD,YAAY;QACR,KAAK;YAED,sBAAsB,GACtB,IAAI,IAAI,CAAC,WAAW,EAChB,OAAO,IAAI,CAAC,WAAW;YAE3B,IAAI,CAAC,WAAW,GAAG,CAAC;YACpB,IAAK,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;gBACrE,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,EAC7B,KAAK,MAAM,EAAE;gBAEjB,sBAAsB,GACtB,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,EACpB,MAAM,MAAM,kBAAkB,KAAK,SAAS,IAAI;gBAEpD,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG;YAC3B;YACA,OAAO,IAAI,CAAC,WAAW;QAC3B;IACJ;IAEA;;;;;KAKC,GACD,aAAa;QACT,KAAK;YACD,OAAO,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;QAC9E;IACJ;IAEA;;;;;KAKC,GACD,aAAa;QACT,KAAK;YACD,OAAO,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;QAC9E;IACJ;IAEA;;;;;KAKC,GACD,MAAM;QACF,KAAK;YACD,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,mBAAmB,CAAC,IAAI,GAAG;QACtE;QACA,KAAK,SAAS,IAAI;YAEd,0BAA0B;YAC1B,IAAI,YAAY,KAAK,SAAS;YAC9B,IAAI,CAAC,CAAC,qBAAqB,OAAO,GAAG;gBACjC,CAAC,KAAK,SAAS,GAAG,IAAI,SAAS,EAAE,WAAW,GAAG;gBAC/C,KAAK,KAAK,CAAC,KAAK,SAAS,EAAE;YAC/B;YAEA,sDAAsD;YACtD,KAAK,KAAK,GAAG,KAAK,SAAS,CAAC,KAAK,GAAG,IAAI;YAExC,wBAAwB;YACxB,KAAK,KAAK,CAAC,MAAM,SAAS;YAE1B,IAAI,CAAC,KAAK,GAAG;YAEb,iEAAiE;YACjE,IAAI,IAAI;YACR,MAAO,IAAI,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,EACpD,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,IAAI,yBAAyB;YAE7D,gFAAgF;YAChF,IAAI,iBAAiB,CAAC;YACtB,IAAK,IAAI,GAAG,IAAI,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,EACzD,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG;gBAClD,KAAK,KAAK,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK;gBAChD,KAAK,KAAK,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK;YACpD;YACJ,IAAI,GACA,OAAO,gBAAgB,CAAC,KAAK,SAAS,EAAE;QAChD;IACJ;AACJ;AAEA;;;;CAIC,GACD,KAAK,mBAAmB,GAAG,SAAS,oBAAoB,KAAK;IACzD,0CAA0C,GAC1C,IAAI,MAAM,KAAK,OAAO,CAAC;QAAC;KAAI,EAAE,MAAM,IAAI;IACxC,2GAA2G;IAC3G,IAAK,IAAI,IAAI,GAAG,OAAO,IAAI,MAAM,WAAW,CAAC,MAAM,EAAE,EAAE,EACnD,IAAI,CAAC,QAAQ,MAAM,YAAY,CAAC,EAAE,EAAE,GAAG,EAAE,IACpC,aAAa,KAAK,QAAQ,CAAC,MAAM,IAAI;SACrC,IAAI,MAAM,QAAQ,EAAE,IACpB,aAAa,KAAK,QAAQ,CAAC,MAAM,IAAI;IAC9C,OAAO,IACN,yEAAyE,yBAAyB;KAC9F;AACL,yCAAyC,GAC7C;AAEA,SAAS,WAAW,IAAI;IACpB,KAAK,WAAW,GAAG,KAAK,YAAY,GAAG,KAAK,YAAY,GAAG;IAC3D,OAAO,KAAK,MAAM;IAClB,OAAO,KAAK,MAAM;IAClB,OAAO,KAAK,MAAM;IAClB,OAAO;AACX;AAEA;;;;;;;;;CASC,GAED;;;;;CAKC,GACD,KAAK,QAAQ,GAAG,SAAS,SAAS,IAAI,EAAE,IAAI;IACxC,IAAI,OAAO,IAAI,KAAK,MAAM,KAAK,OAAO;IACtC,KAAK,UAAU,GAAG,KAAK,UAAU;IACjC,KAAK,QAAQ,GAAG,KAAK,QAAQ;IAC7B,IAAI,QAAQ,OAAO,IAAI,CAAC,KAAK,MAAM,GAC/B,IAAI;IACR,MAAO,IAAI,MAAM,MAAM,EAAE,EAAE,EACvB,KAAK,GAAG,CACJ,CAAE,OAAO,KAAK,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,KAAK,cACzC,SAAS,QAAQ,GACjB,MAAM,QAAQ,AAAC,EAAE,KAAK,CAAC,EAAE,EAAE,KAAK,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;IAE1D,IAAI,KAAK,MAAM,EACX,IAAK,QAAQ,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAC9D,KAAK,GAAG,CAAC,MAAM,QAAQ,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;IAC/D,IAAI,KAAK,MAAM,EACX,IAAK,QAAQ,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;QACjE,IAAI,SAAS,KAAK,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;QAClC,KAAK,GAAG,CACJ,CAAE,OAAO,EAAE,KAAK,YACd,MAAM,QAAQ,GACd,OAAO,MAAM,KAAK,YAClB,KAAK,QAAQ,GACb,OAAO,MAAM,KAAK,YAClB,KAAK,QAAQ,GACb,OAAO,OAAO,KAAK,YACnB,QAAQ,QAAQ,GAChB,UAAU,QAAQ,AAAC,EAAE,KAAK,CAAC,EAAE,EAAE;IAEzC;IACJ,IAAI,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,EACzC,KAAK,UAAU,GAAG,KAAK,UAAU;IACrC,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,EACrC,KAAK,QAAQ,GAAG,KAAK,QAAQ;IACjC,IAAI,KAAK,KAAK,EACV,KAAK,KAAK,GAAG;IACjB,IAAI,KAAK,OAAO,EACZ,KAAK,OAAO,GAAG,KAAK,OAAO;IAC/B,IAAI,KAAK,OAAO,EACZ,KAAK,QAAQ,GAAG,KAAK,OAAO;IAChC,KAAK,eAAe,GAAG,UAAW,+BAA+B;IACjE,OAAO;AACX;AAEA;;;;CAIC,GACD,KAAK,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,aAAa;IACjD,IAAI,YAAY,UAAU,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE;IACtD,IAAI,eAAe,gBAAgB,QAAQ,cAAc,YAAY,IAAI;IACzE,OAAO,KAAK,QAAQ,CAAC;QACjB;QAAe,IAAI,CAAC,cAAc;QAClC;QAAe,aAAa,UAAU,OAAO,IAAI;QACjD;QAAe,UAAU,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE;QACvD;QAAe,UAAU,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,GAAG;YAAI,OAAO,CAAC,IAAI,cAAc;QAAE,IAAI,kBAAkB,CAAC;QAChI;QAAe,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,GAAG;QAC7E;QAAe,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG;QACvE;QAAe,IAAI,CAAC,KAAK,IAAI;QAC7B;QAAe,aAAa,UAAU,MAAM,IAAI;QAChD;QAAe,eAAe,IAAI,CAAC,OAAO,GAAG;KAChD;AACL;AAEA;;CAEC,GACD,KAAK,SAAS,CAAC,UAAU,GAAG,SAAS;IACjC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,OAAO,IAAI;IAE7C,UAAU,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;IACxC,IAAI,SAAS,IAAI,CAAC,WAAW;IAAE,IAAI;IACnC,MAAO,IAAI,OAAO,MAAM,CACpB,MAAM,CAAC,IAAI,CAAC,OAAO;IACvB,IAAI,SAAS,IAAI,CAAC,WAAW,EAAE,IAAI;IACnC,MAAO,IAAI,OAAO,MAAM,CACpB,MAAM,CAAC,IAAI,CAAC,OAAO;IACvB,OAAO,IAAI;AACf;AAEA;;CAEC,GACD,KAAK,SAAS,CAAC,yBAAyB,GAAG,SAAS,0BAA0B,OAAO;IACjF,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE,OAAO,IAAI;IAEvD,UAAU,IAAI,CAAC,QAAQ,IAAI;IAE3B,UAAU,SAAS,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE;IACzD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;QACrB,MAAM,gBAAgB,CAAC;IAC3B;IACA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;QACrB,MAAM,gBAAgB,CAAC;IAC3B;IACA,OAAO,IAAI;AACf;AAEA;;CAEC,GACD,KAAK,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,IAAI;IAClC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,IACjB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,IAChC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,IAChC;AACX;AAEA;;;;;;CAMC,GACD,KAAK,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,MAAM;IAEpC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,GACpB,MAAM,MAAM,qBAAqB,OAAO,IAAI,GAAG,UAAU,IAAI;IAEjE,IAAI,kBAAkB,SAAS,OAAO,MAAM,KAAK,WAAW;QACxD,yFAAyF;QACzF,yFAAyF;QACzF,gBAAgB;QAEhB,6FAA6F;QAC7F,IAAI,IAAI,CAAC,WAAW,GAAG,wBAAwB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EACtG,MAAM,MAAM,kBAAkB,OAAO,EAAE,GAAG,SAAS,IAAI;QAC3D,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,GAC3B,MAAM,MAAM,QAAQ,OAAO,EAAE,GAAG,qBAAqB,IAAI;QAC7D,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,GAC/B,MAAM,MAAM,WAAW,OAAO,IAAI,GAAG,sBAAsB,IAAI;QAEnE,IAAI,OAAO,MAAM,EACb,OAAO,MAAM,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,GAAG;QAC3B,OAAO,OAAO,GAAG,IAAI;QACrB,OAAO,KAAK,CAAC,IAAI;QACjB,OAAO,WAAW,IAAI;IAC1B;IACA,IAAI,kBAAkB,OAAO;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,IAAI,CAAC,MAAM,GAAG,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,GAAG;QAC3B,OAAO,KAAK,CAAC,IAAI;QACjB,OAAO,WAAW,IAAI;IAC1B;IACA,OAAO,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;AAC9C;AAEA;;;;;;CAMC,GACD,KAAK,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,MAAM;IAC1C,IAAI,kBAAkB,SAAS,OAAO,MAAM,KAAK,WAAW;QACxD,sEAAsE;QAEtE,sBAAsB,GACtB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,KAAK,QAC7C,MAAM,MAAM,SAAS,yBAAyB,IAAI;QAEtD,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC;QAC/B,OAAO,MAAM,GAAG;QAChB,OAAO,QAAQ,CAAC,IAAI;QACpB,OAAO,WAAW,IAAI;IAC1B;IACA,IAAI,kBAAkB,OAAO;QAEzB,sBAAsB,GACtB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,KAAK,QAC7C,MAAM,MAAM,SAAS,yBAAyB,IAAI;QAEtD,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC;QAC/B,OAAO,MAAM,GAAG;QAChB,OAAO,QAAQ,CAAC,IAAI;QACpB,OAAO,WAAW,IAAI;IAC1B;IACA,OAAO,UAAU,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE;AACjD;AAEA;;;;CAIC,GACD,KAAK,SAAS,CAAC,YAAY,GAAG,SAAS,aAAa,EAAE;IAClD,OAAO,UAAU,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE;AACjD;AAEA;;;;CAIC,GACD,KAAK,SAAS,CAAC,cAAc,GAAG,SAAS,eAAe,IAAI;IACxD,OAAO,UAAU,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE;AACnD;AAEA;;;;CAIC,GACD,KAAK,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,UAAU;IAC9C,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC;AACzB;AAEA;;;CAGC,GACD,KAAK,SAAS,CAAC,KAAK,GAAG,SAAS;IAC5B,0FAA0F;IAC1F,mDAAmD;IAEnD,IAAI,WAAW,IAAI,CAAC,QAAQ,EACxB,QAAW,EAAE;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,EAC7D,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,GAAG,YAAY;IAE1D,+DAA+D;IAC/D,IAAI,CAAC,MAAM,GAAG,QAAQ,IAAI,EAAE;QACxB,QAAS;QACT,OAAS;QACT,MAAS;IACb;IACA,IAAI,CAAC,MAAM,GAAG,QAAQ,IAAI,EAAE;QACxB,QAAS;QACT,OAAS;QACT,MAAS;IACb;IACA,IAAI,CAAC,MAAM,GAAG,SAAS,IAAI,EAAE;QACzB,OAAQ;QACR,MAAQ;IACZ;IACA,IAAI,CAAC,UAAU,GAAG,UAAU,UAAU,CAAC,IAAI,EAAE;QACzC,OAAQ;QACR,MAAQ;IACZ;IACA,IAAI,CAAC,QAAQ,GAAG,UAAU,QAAQ,CAAC,IAAI,EAAE;QACrC,OAAQ;QACR,MAAQ;IACZ;IAEA,0CAA0C;IAC1C,IAAI,UAAU,QAAQ,CAAC,SAAS;IAChC,IAAI,SAAS;QACT,IAAI,eAAe,OAAO,MAAM,CAAC,IAAI;QACrC,4BAA4B;QACxB,aAAa,UAAU,GAAG,IAAI,CAAC,UAAU;QACzC,IAAI,CAAC,UAAU,GAAG,QAAQ,UAAU,CAAC,IAAI,CAAC;QAC9C,IAAI;QACJ,0BAA0B;QACtB,aAAa,QAAQ,GAAG,IAAI,CAAC,QAAQ;QACrC,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ,CAAC,IAAI,CAAC;IAC1C,IAAI;IACR;IAEA,OAAO,IAAI;AACf;AAEA;;;;;CAKC,GACD,KAAK,SAAS,CAAC,MAAM,GAAG,SAAS,aAAa,OAAO,EAAE,MAAM;IACzD,OAAO,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,SAAS,SAAS,wBAAwB;AACzE;AAEA;;;;;CAKC,GACD,KAAK,SAAS,CAAC,eAAe,GAAG,SAAS,gBAAgB,OAAO,EAAE,MAAM;IACrE,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,UAAU,OAAO,GAAG,GAAG,OAAO,IAAI,KAAK,QAAQ,MAAM;AACrF;AAEA;;;;;;;CAOC,GACD,KAAK,SAAS,CAAC,MAAM,GAAG,SAAS,aAAa,MAAM,EAAE,MAAM;IACxD,OAAO,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ,SAAS,wBAAwB;AACxE;AAEA;;;;;;CAMC,GACD,KAAK,SAAS,CAAC,eAAe,GAAG,SAAS,gBAAgB,MAAM;IAC5D,IAAI,CAAC,CAAC,kBAAkB,MAAM,GAC1B,SAAS,OAAO,MAAM,CAAC;IAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,OAAO,MAAM;AAC5C;AAEA;;;;CAIC,GACD,KAAK,SAAS,CAAC,MAAM,GAAG,SAAS,aAAa,OAAO;IACjD,OAAO,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,UAAU,wBAAwB;AACjE;AAEA;;;;CAIC,GACD,KAAK,SAAS,CAAC,UAAU,GAAG,SAAS,WAAW,MAAM;IAClD,OAAO,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;AACnC;AAEA;;;;;;;;;;;;;;;;;CAiBC,GAED;;;;;CAKC,GACD,KAAK,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAS,OAAO,EAAE,OAAO;IACxD,OAAO,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,SAAS;AAC1C;AAEA;;;;;;;CAOC,GAED;;;;;CAKC,GACD,KAAK,CAAC,GAAG,SAAS,aAAa,QAAQ;IACnC,OAAO,SAAS,cAAc,MAAM;QAChC,KAAK,YAAY,CAAC,QAAQ;IAC9B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3136, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/root.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = Root;\n\n// extends Namespace\nvar Namespace = require(\"./namespace\");\n((Root.prototype = Object.create(Namespace.prototype)).constructor = Root).className = \"Root\";\n\nvar Field   = require(\"./field\"),\n    Enum    = require(\"./enum\"),\n    OneOf   = require(\"./oneof\"),\n    util    = require(\"./util\");\n\nvar Type,   // cyclic\n    parse,  // might be excluded\n    common; // \"\n\n/**\n * Constructs a new root namespace instance.\n * @classdesc Root namespace wrapping all types, enums, services, sub-namespaces etc. that belong together.\n * @extends NamespaceBase\n * @constructor\n * @param {Object.<string,*>} [options] Top level options\n */\nfunction Root(options) {\n    Namespace.call(this, \"\", options);\n\n    /**\n     * Deferred extension fields.\n     * @type {Field[]}\n     */\n    this.deferred = [];\n\n    /**\n     * Resolved file names of loaded files.\n     * @type {string[]}\n     */\n    this.files = [];\n\n    /**\n     * Edition, defaults to proto2 if unspecified.\n     * @type {string}\n     * @private\n     */\n    this._edition = \"proto2\";\n\n    /**\n     * Global lookup cache of fully qualified names.\n     * @type {Object.<string,ReflectionObject>}\n     * @private\n     */\n    this._fullyQualifiedObjects = {};\n}\n\n/**\n * Loads a namespace descriptor into a root namespace.\n * @param {INamespace} json Namespace descriptor\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted\n * @returns {Root} Root namespace\n */\nRoot.fromJSON = function fromJSON(json, root) {\n    if (!root)\n        root = new Root();\n    if (json.options)\n        root.setOptions(json.options);\n    return root.addJSON(json.nested).resolveAll();\n};\n\n/**\n * Resolves the path of an imported file, relative to the importing origin.\n * This method exists so you can override it with your own logic in case your imports are scattered over multiple directories.\n * @function\n * @param {string} origin The file name of the importing file\n * @param {string} target The file name being imported\n * @returns {string|null} Resolved path to `target` or `null` to skip the file\n */\nRoot.prototype.resolvePath = util.path.resolve;\n\n/**\n * Fetch content from file path or url\n * This method exists so you can override it with your own logic.\n * @function\n * @param {string} path File path or url\n * @param {FetchCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.fetch = util.fetch;\n\n// A symbol-like function to safely signal synchronous loading\n/* istanbul ignore next */\nfunction SYNC() {} // eslint-disable-line no-empty-function\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} options Parse options\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.load = function load(filename, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = undefined;\n    }\n    var self = this;\n    if (!callback) {\n        return util.asPromise(load, self, filename, options);\n    }\n\n    var sync = callback === SYNC; // undocumented\n\n    // Finishes loading by calling the callback (exactly once)\n    function finish(err, root) {\n        /* istanbul ignore if */\n        if (!callback) {\n            return;\n        }\n        if (sync) {\n            throw err;\n        }\n        if (root) {\n            root.resolveAll();\n        }\n        var cb = callback;\n        callback = null;\n        cb(err, root);\n    }\n\n    // Bundled definition existence checking\n    function getBundledFileName(filename) {\n        var idx = filename.lastIndexOf(\"google/protobuf/\");\n        if (idx > -1) {\n            var altname = filename.substring(idx);\n            if (altname in common) return altname;\n        }\n        return null;\n    }\n\n    // Processes a single file\n    function process(filename, source) {\n        try {\n            if (util.isString(source) && source.charAt(0) === \"{\")\n                source = JSON.parse(source);\n            if (!util.isString(source))\n                self.setOptions(source.options).addJSON(source.nested);\n            else {\n                parse.filename = filename;\n                var parsed = parse(source, self, options),\n                    resolved,\n                    i = 0;\n                if (parsed.imports)\n                    for (; i < parsed.imports.length; ++i)\n                        if (resolved = getBundledFileName(parsed.imports[i]) || self.resolvePath(filename, parsed.imports[i]))\n                            fetch(resolved);\n                if (parsed.weakImports)\n                    for (i = 0; i < parsed.weakImports.length; ++i)\n                        if (resolved = getBundledFileName(parsed.weakImports[i]) || self.resolvePath(filename, parsed.weakImports[i]))\n                            fetch(resolved, true);\n            }\n        } catch (err) {\n            finish(err);\n        }\n        if (!sync && !queued) {\n            finish(null, self); // only once anyway\n        }\n    }\n\n    // Fetches a single file\n    function fetch(filename, weak) {\n        filename = getBundledFileName(filename) || filename;\n\n        // Skip if already loaded / attempted\n        if (self.files.indexOf(filename) > -1) {\n            return;\n        }\n        self.files.push(filename);\n\n        // Shortcut bundled definitions\n        if (filename in common) {\n            if (sync) {\n                process(filename, common[filename]);\n            } else {\n                ++queued;\n                setTimeout(function() {\n                    --queued;\n                    process(filename, common[filename]);\n                });\n            }\n            return;\n        }\n\n        // Otherwise fetch from disk or network\n        if (sync) {\n            var source;\n            try {\n                source = util.fs.readFileSync(filename).toString(\"utf8\");\n            } catch (err) {\n                if (!weak)\n                    finish(err);\n                return;\n            }\n            process(filename, source);\n        } else {\n            ++queued;\n            self.fetch(filename, function(err, source) {\n                --queued;\n                /* istanbul ignore if */\n                if (!callback) {\n                    return; // terminated meanwhile\n                }\n                if (err) {\n                    /* istanbul ignore else */\n                    if (!weak)\n                        finish(err);\n                    else if (!queued) // can't be covered reliably\n                        finish(null, self);\n                    return;\n                }\n                process(filename, source);\n            });\n        }\n    }\n    var queued = 0;\n\n    // Assembling the root namespace doesn't require working type\n    // references anymore, so we can load everything in parallel\n    if (util.isString(filename)) {\n        filename = [ filename ];\n    }\n    for (var i = 0, resolved; i < filename.length; ++i)\n        if (resolved = self.resolvePath(\"\", filename[i]))\n            fetch(resolved);\n    if (sync) {\n        self.resolveAll();\n        return self;\n    }\n    if (!queued) {\n        finish(null, self);\n    }\n\n    return self;\n};\n// function load(filename:string, options:IParseOptions, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and returns a promise.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Promise<Root>} Promise\n * @variation 3\n */\n// function load(filename:string, [options:IParseOptions]):Promise<Root>\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into this root namespace (node only).\n * @function Root#loadSync\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n */\nRoot.prototype.loadSync = function loadSync(filename, options) {\n    if (!util.isNode)\n        throw Error(\"not supported\");\n    return this.load(filename, options, SYNC);\n};\n\n/**\n * @override\n */\nRoot.prototype.resolveAll = function resolveAll() {\n    if (!this._needsRecursiveResolve) return this;\n\n    if (this.deferred.length)\n        throw Error(\"unresolvable extensions: \" + this.deferred.map(function(field) {\n            return \"'extend \" + field.extend + \"' in \" + field.parent.fullName;\n        }).join(\", \"));\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n// only uppercased (and thus conflict-free) children are exposed, see below\nvar exposeRe = /^[A-Z]/;\n\n/**\n * Handles a deferred declaring extension field by creating a sister field to represent it within its extended type.\n * @param {Root} root Root instance\n * @param {Field} field Declaring extension field witin the declaring type\n * @returns {boolean} `true` if successfully added to the extended type, `false` otherwise\n * @inner\n * @ignore\n */\nfunction tryHandleExtension(root, field) {\n    var extendedType = field.parent.lookup(field.extend);\n    if (extendedType) {\n        var sisterField = new Field(field.fullName, field.id, field.type, field.rule, undefined, field.options);\n        //do not allow to extend same field twice to prevent the error\n        if (extendedType.get(sisterField.name)) {\n            return true;\n        }\n        sisterField.declaringField = field;\n        field.extensionField = sisterField;\n        extendedType.add(sisterField);\n        return true;\n    }\n    return false;\n}\n\n/**\n * Called when any object is added to this root or its sub-namespaces.\n * @param {ReflectionObject} object Object added\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleAdd = function _handleAdd(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field (implies not part of a oneof) */ object.extend !== undefined && /* not already handled */ !object.extensionField)\n            if (!tryHandleExtension(this, object))\n                this.deferred.push(object);\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object.values; // expose enum values as property of its parent\n\n    } else if (!(object instanceof OneOf)) /* everything else is a namespace */ {\n\n        if (object instanceof Type) // Try to handle any deferred extensions\n            for (var i = 0; i < this.deferred.length;)\n                if (tryHandleExtension(this, this.deferred[i]))\n                    this.deferred.splice(i, 1);\n                else\n                    ++i;\n        for (var j = 0; j < /* initializes */ object.nestedArray.length; ++j) // recurse into the namespace\n            this._handleAdd(object._nestedArray[j]);\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object; // expose namespace as property of its parent\n    }\n\n    if (object instanceof Type || object instanceof Enum || object instanceof Field) {\n        // Only store types and enums for quick lookup during resolve.\n        this._fullyQualifiedObjects[object.fullName] = object;\n    }\n\n    // The above also adds uppercased (and thus conflict-free) nested types, services and enums as\n    // properties of namespaces just like static code does. This allows using a .d.ts generated for\n    // a static module with reflection-based solutions where the condition is met.\n};\n\n/**\n * Called when any object is removed from this root or its sub-namespaces.\n * @param {ReflectionObject} object Object removed\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleRemove = function _handleRemove(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field */ object.extend !== undefined) {\n            if (/* already handled */ object.extensionField) { // remove its sister field\n                object.extensionField.parent.remove(object.extensionField);\n                object.extensionField = null;\n            } else { // cancel the extension\n                var index = this.deferred.indexOf(object);\n                /* istanbul ignore else */\n                if (index > -1)\n                    this.deferred.splice(index, 1);\n            }\n        }\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose enum values\n\n    } else if (object instanceof Namespace) {\n\n        for (var i = 0; i < /* initializes */ object.nestedArray.length; ++i) // recurse into the namespace\n            this._handleRemove(object._nestedArray[i]);\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose namespaces\n\n    }\n\n    delete this._fullyQualifiedObjects[object.fullName];\n};\n\n// Sets up cyclic dependencies (called in index-light)\nRoot._configure = function(Type_, parse_, common_) {\n    Type   = Type_;\n    parse  = parse_;\n    common = common_;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,oBAAoB;AACpB,IAAI;AACJ,CAAC,CAAC,KAAK,SAAS,GAAG,OAAO,MAAM,CAAC,UAAU,SAAS,CAAC,EAAE,WAAW,GAAG,IAAI,EAAE,SAAS,GAAG;AAEvF,IAAI,0GACA,wGACA,0GACA;AAEJ,IAAI,MACA,OACA,QAAQ,IAAI;AAEhB;;;;;;CAMC,GACD,SAAS,KAAK,OAAO;IACjB,UAAU,IAAI,CAAC,IAAI,EAAE,IAAI;IAEzB;;;KAGC,GACD,IAAI,CAAC,QAAQ,GAAG,EAAE;IAElB;;;KAGC,GACD,IAAI,CAAC,KAAK,GAAG,EAAE;IAEf;;;;KAIC,GACD,IAAI,CAAC,QAAQ,GAAG;IAEhB;;;;KAIC,GACD,IAAI,CAAC,sBAAsB,GAAG,CAAC;AACnC;AAEA;;;;;CAKC,GACD,KAAK,QAAQ,GAAG,SAAS,SAAS,IAAI,EAAE,IAAI;IACxC,IAAI,CAAC,MACD,OAAO,IAAI;IACf,IAAI,KAAK,OAAO,EACZ,KAAK,UAAU,CAAC,KAAK,OAAO;IAChC,OAAO,KAAK,OAAO,CAAC,KAAK,MAAM,EAAE,UAAU;AAC/C;AAEA;;;;;;;CAOC,GACD,KAAK,SAAS,CAAC,WAAW,GAAG,KAAK,IAAI,CAAC,OAAO;AAE9C;;;;;;;CAOC,GACD,KAAK,SAAS,CAAC,KAAK,GAAG,KAAK,KAAK;AAEjC,8DAA8D;AAC9D,wBAAwB,GACxB,SAAS,QAAQ,EAAE,wCAAwC;AAE3D;;;;;;CAMC,GACD,KAAK,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK,QAAQ,EAAE,OAAO,EAAE,QAAQ;IAC3D,IAAI,OAAO,YAAY,YAAY;QAC/B,WAAW;QACX,UAAU;IACd;IACA,IAAI,OAAO,IAAI;IACf,IAAI,CAAC,UAAU;QACX,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM,UAAU;IAChD;IAEA,IAAI,OAAO,aAAa,MAAM,eAAe;IAE7C,0DAA0D;IAC1D,SAAS,OAAO,GAAG,EAAE,IAAI;QACrB,sBAAsB,GACtB,IAAI,CAAC,UAAU;YACX;QACJ;QACA,IAAI,MAAM;YACN,MAAM;QACV;QACA,IAAI,MAAM;YACN,KAAK,UAAU;QACnB;QACA,IAAI,KAAK;QACT,WAAW;QACX,GAAG,KAAK;IACZ;IAEA,wCAAwC;IACxC,SAAS,mBAAmB,QAAQ;QAChC,IAAI,MAAM,SAAS,WAAW,CAAC;QAC/B,IAAI,MAAM,CAAC,GAAG;YACV,IAAI,UAAU,SAAS,SAAS,CAAC;YACjC,IAAI,WAAW,QAAQ,OAAO;QAClC;QACA,OAAO;IACX;IAEA,0BAA0B;IAC1B,SAAS,QAAQ,QAAQ,EAAE,MAAM;QAC7B,IAAI;YACA,IAAI,KAAK,QAAQ,CAAC,WAAW,OAAO,MAAM,CAAC,OAAO,KAC9C,SAAS,KAAK,KAAK,CAAC;YACxB,IAAI,CAAC,KAAK,QAAQ,CAAC,SACf,KAAK,UAAU,CAAC,OAAO,OAAO,EAAE,OAAO,CAAC,OAAO,MAAM;iBACpD;gBACD,MAAM,QAAQ,GAAG;gBACjB,IAAI,SAAS,MAAM,QAAQ,MAAM,UAC7B,UACA,IAAI;gBACR,IAAI,OAAO,OAAO,EACd;oBAAA,MAAO,IAAI,OAAO,OAAO,CAAC,MAAM,EAAE,EAAE,EAChC,IAAI,WAAW,mBAAmB,OAAO,OAAO,CAAC,EAAE,KAAK,KAAK,WAAW,CAAC,UAAU,OAAO,OAAO,CAAC,EAAE,GAChG,MAAM;gBAAS;gBAC3B,IAAI,OAAO,WAAW,EAClB;oBAAA,IAAK,IAAI,GAAG,IAAI,OAAO,WAAW,CAAC,MAAM,EAAE,EAAE,EACzC,IAAI,WAAW,mBAAmB,OAAO,WAAW,CAAC,EAAE,KAAK,KAAK,WAAW,CAAC,UAAU,OAAO,WAAW,CAAC,EAAE,GACxG,MAAM,UAAU;gBAAK;YACrC;QACJ,EAAE,OAAO,KAAK;YACV,OAAO;QACX;QACA,IAAI,CAAC,QAAQ,CAAC,QAAQ;YAClB,OAAO,MAAM,OAAO,mBAAmB;QAC3C;IACJ;IAEA,wBAAwB;IACxB,SAAS,MAAM,QAAQ,EAAE,IAAI;QACzB,WAAW,mBAAmB,aAAa;QAE3C,qCAAqC;QACrC,IAAI,KAAK,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG;YACnC;QACJ;QACA,KAAK,KAAK,CAAC,IAAI,CAAC;QAEhB,+BAA+B;QAC/B,IAAI,YAAY,QAAQ;YACpB,IAAI,MAAM;gBACN,QAAQ,UAAU,MAAM,CAAC,SAAS;YACtC,OAAO;gBACH,EAAE;gBACF,WAAW;oBACP,EAAE;oBACF,QAAQ,UAAU,MAAM,CAAC,SAAS;gBACtC;YACJ;YACA;QACJ;QAEA,uCAAuC;QACvC,IAAI,MAAM;YACN,IAAI;YACJ,IAAI;gBACA,SAAS,KAAK,EAAE,CAAC,YAAY,CAAC,UAAU,QAAQ,CAAC;YACrD,EAAE,OAAO,KAAK;gBACV,IAAI,CAAC,MACD,OAAO;gBACX;YACJ;YACA,QAAQ,UAAU;QACtB,OAAO;YACH,EAAE;YACF,KAAK,KAAK,CAAC,UAAU,SAAS,GAAG,EAAE,MAAM;gBACrC,EAAE;gBACF,sBAAsB,GACtB,IAAI,CAAC,UAAU;oBACX,QAAQ,uBAAuB;gBACnC;gBACA,IAAI,KAAK;oBACL,wBAAwB,GACxB,IAAI,CAAC,MACD,OAAO;yBACN,IAAI,CAAC,QACN,OAAO,MAAM;oBACjB;gBACJ;gBACA,QAAQ,UAAU;YACtB;QACJ;IACJ;IACA,IAAI,SAAS;IAEb,6DAA6D;IAC7D,4DAA4D;IAC5D,IAAI,KAAK,QAAQ,CAAC,WAAW;QACzB,WAAW;YAAE;SAAU;IAC3B;IACA,IAAK,IAAI,IAAI,GAAG,UAAU,IAAI,SAAS,MAAM,EAAE,EAAE,EAC7C,IAAI,WAAW,KAAK,WAAW,CAAC,IAAI,QAAQ,CAAC,EAAE,GAC3C,MAAM;IACd,IAAI,MAAM;QACN,KAAK,UAAU;QACf,OAAO;IACX;IACA,IAAI,CAAC,QAAQ;QACT,OAAO,MAAM;IACjB;IAEA,OAAO;AACX;AACA,yFAAyF;AAEzF;;;;;;;CAOC,GACD,kEAAkE;AAElE;;;;;;;CAOC,GACD,wEAAwE;AAExE;;;;;;;CAOC,GACD,KAAK,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAS,QAAQ,EAAE,OAAO;IACzD,IAAI,CAAC,KAAK,MAAM,EACZ,MAAM,MAAM;IAChB,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,SAAS;AACxC;AAEA;;CAEC,GACD,KAAK,SAAS,CAAC,UAAU,GAAG,SAAS;IACjC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,OAAO,IAAI;IAE7C,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EACpB,MAAM,MAAM,8BAA8B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,KAAK;QACtE,OAAO,aAAa,MAAM,MAAM,GAAG,UAAU,MAAM,MAAM,CAAC,QAAQ;IACtE,GAAG,IAAI,CAAC;IACZ,OAAO,UAAU,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;AACnD;AAEA,2EAA2E;AAC3E,IAAI,WAAW;AAEf;;;;;;;CAOC,GACD,SAAS,mBAAmB,IAAI,EAAE,KAAK;IACnC,IAAI,eAAe,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,MAAM;IACnD,IAAI,cAAc;QACd,IAAI,cAAc,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,EAAE,EAAE,MAAM,IAAI,EAAE,MAAM,IAAI,EAAE,WAAW,MAAM,OAAO;QACtG,8DAA8D;QAC9D,IAAI,aAAa,GAAG,CAAC,YAAY,IAAI,GAAG;YACpC,OAAO;QACX;QACA,YAAY,cAAc,GAAG;QAC7B,MAAM,cAAc,GAAG;QACvB,aAAa,GAAG,CAAC;QACjB,OAAO;IACX;IACA,OAAO;AACX;AAEA;;;;;CAKC,GACD,KAAK,SAAS,CAAC,UAAU,GAAG,SAAS,WAAW,MAAM;IAClD,IAAI,kBAAkB,OAAO;QAEzB,IAAI,oDAAoD,GAAG,OAAO,MAAM,KAAK,aAAa,uBAAuB,GAAG,CAAC,OAAO,cAAc,EACtI;YAAA,IAAI,CAAC,mBAAmB,IAAI,EAAE,SAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAAO;IAEtC,OAAO,IAAI,kBAAkB,MAAM;QAE/B,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,GACzB,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,MAAM,EAAE,+CAA+C;IAEnG,OAAO,IAAI,CAAC,CAAC,kBAAkB,KAAK,GAAG,kCAAkC,GAAG;QAExE,IAAI,kBAAkB,MAClB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EACpC,IAAI,mBAAmB,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,GACzC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG;aAExB,EAAE;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,GAAG,OAAO,WAAW,CAAC,MAAM,EAAE,EAAE,EAC/D,IAAI,CAAC,UAAU,CAAC,OAAO,YAAY,CAAC,EAAE;QAC1C,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,GACzB,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,GAAG,QAAQ,6CAA6C;IAC1F;IAEA,IAAI,kBAAkB,QAAQ,kBAAkB,QAAQ,kBAAkB,OAAO;QAC7E,8DAA8D;QAC9D,IAAI,CAAC,sBAAsB,CAAC,OAAO,QAAQ,CAAC,GAAG;IACnD;AAEA,8FAA8F;AAC9F,+FAA+F;AAC/F,8EAA8E;AAClF;AAEA;;;;;CAKC,GACD,KAAK,SAAS,CAAC,aAAa,GAAG,SAAS,cAAc,MAAM;IACxD,IAAI,kBAAkB,OAAO;QAEzB,IAAI,sBAAsB,GAAG,OAAO,MAAM,KAAK,WAAW;YACtD,IAAI,mBAAmB,GAAG,OAAO,cAAc,EAAE;gBAC7C,OAAO,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,cAAc;gBACzD,OAAO,cAAc,GAAG;YAC5B,OAAO;gBACH,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAClC,wBAAwB,GACxB,IAAI,QAAQ,CAAC,GACT,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO;YACpC;QACJ;IAEJ,OAAO,IAAI,kBAAkB,MAAM;QAE/B,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,GACzB,OAAO,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE,uBAAuB;IAElE,OAAO,IAAI,kBAAkB,WAAW;QAEpC,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,GAAG,OAAO,WAAW,CAAC,MAAM,EAAE,EAAE,EAC/D,IAAI,CAAC,aAAa,CAAC,OAAO,YAAY,CAAC,EAAE;QAE7C,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,GACzB,OAAO,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE,sBAAsB;IAEjE;IAEA,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAO,QAAQ,CAAC;AACvD;AAEA,sDAAsD;AACtD,KAAK,UAAU,GAAG,SAAS,KAAK,EAAE,MAAM,EAAE,OAAO;IAC7C,OAAS;IACT,QAAS;IACT,SAAS;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3447, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/util.js"], "sourcesContent": ["\"use strict\";\n\n/**\n * Various utility functions.\n * @namespace\n */\nvar util = module.exports = require(\"./util/minimal\");\n\nvar roots = require(\"./roots\");\n\nvar Type, // cyclic\n    Enum;\n\nutil.codegen = require(\"@protobufjs/codegen\");\nutil.fetch   = require(\"@protobufjs/fetch\");\nutil.path    = require(\"@protobufjs/path\");\n\n/**\n * Node's fs module if available.\n * @type {Object.<string,*>}\n */\nutil.fs = util.inquire(\"fs\");\n\n/**\n * Converts an object's values to an array.\n * @param {Object.<string,*>} object Object to convert\n * @returns {Array.<*>} Converted array\n */\nutil.toArray = function toArray(object) {\n    if (object) {\n        var keys  = Object.keys(object),\n            array = new Array(keys.length),\n            index = 0;\n        while (index < keys.length)\n            array[index] = object[keys[index++]];\n        return array;\n    }\n    return [];\n};\n\n/**\n * Converts an array of keys immediately followed by their respective value to an object, omitting undefined values.\n * @param {Array.<*>} array Array to convert\n * @returns {Object.<string,*>} Converted object\n */\nutil.toObject = function toObject(array) {\n    var object = {},\n        index  = 0;\n    while (index < array.length) {\n        var key = array[index++],\n            val = array[index++];\n        if (val !== undefined)\n            object[key] = val;\n    }\n    return object;\n};\n\nvar safePropBackslashRe = /\\\\/g,\n    safePropQuoteRe     = /\"/g;\n\n/**\n * Tests whether the specified name is a reserved word in JS.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nutil.isReserved = function isReserved(name) {\n    return /^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(name);\n};\n\n/**\n * Returns a safe property accessor for the specified property name.\n * @param {string} prop Property name\n * @returns {string} Safe accessor\n */\nutil.safeProp = function safeProp(prop) {\n    if (!/^[$\\w_]+$/.test(prop) || util.isReserved(prop))\n        return \"[\\\"\" + prop.replace(safePropBackslashRe, \"\\\\\\\\\").replace(safePropQuoteRe, \"\\\\\\\"\") + \"\\\"]\";\n    return \".\" + prop;\n};\n\n/**\n * Converts the first character of a string to upper case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.ucFirst = function ucFirst(str) {\n    return str.charAt(0).toUpperCase() + str.substring(1);\n};\n\nvar camelCaseRe = /_([a-z])/g;\n\n/**\n * Converts a string to camel case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.camelCase = function camelCase(str) {\n    return str.substring(0, 1)\n         + str.substring(1)\n               .replace(camelCaseRe, function($0, $1) { return $1.toUpperCase(); });\n};\n\n/**\n * Compares reflected fields by id.\n * @param {Field} a First field\n * @param {Field} b Second field\n * @returns {number} Comparison value\n */\nutil.compareFieldsById = function compareFieldsById(a, b) {\n    return a.id - b.id;\n};\n\n/**\n * Decorator helper for types (TypeScript).\n * @param {Constructor<T>} ctor Constructor function\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {Type} Reflected type\n * @template T extends Message<T>\n * @property {Root} root Decorators root\n */\nutil.decorateType = function decorateType(ctor, typeName) {\n\n    /* istanbul ignore if */\n    if (ctor.$type) {\n        if (typeName && ctor.$type.name !== typeName) {\n            util.decorateRoot.remove(ctor.$type);\n            ctor.$type.name = typeName;\n            util.decorateRoot.add(ctor.$type);\n        }\n        return ctor.$type;\n    }\n\n    /* istanbul ignore next */\n    if (!Type)\n        Type = require(\"./type\");\n\n    var type = new Type(typeName || ctor.name);\n    util.decorateRoot.add(type);\n    type.ctor = ctor; // sets up .encode, .decode etc.\n    Object.defineProperty(ctor, \"$type\", { value: type, enumerable: false });\n    Object.defineProperty(ctor.prototype, \"$type\", { value: type, enumerable: false });\n    return type;\n};\n\nvar decorateEnumIndex = 0;\n\n/**\n * Decorator helper for enums (TypeScript).\n * @param {Object} object Enum object\n * @returns {Enum} Reflected enum\n */\nutil.decorateEnum = function decorateEnum(object) {\n\n    /* istanbul ignore if */\n    if (object.$type)\n        return object.$type;\n\n    /* istanbul ignore next */\n    if (!Enum)\n        Enum = require(\"./enum\");\n\n    var enm = new Enum(\"Enum\" + decorateEnumIndex++, object);\n    util.decorateRoot.add(enm);\n    Object.defineProperty(object, \"$type\", { value: enm, enumerable: false });\n    return enm;\n};\n\n\n/**\n * Sets the value of a property by property path. If a value already exists, it is turned to an array\n * @param {Object.<string,*>} dst Destination object\n * @param {string} path dot '.' delimited path of the property to set\n * @param {Object} value the value to set\n * @param {boolean|undefined} [ifNotSet] Sets the option only if it isn't currently set\n * @returns {Object.<string,*>} Destination object\n */\nutil.setProperty = function setProperty(dst, path, value, ifNotSet) {\n    function setProp(dst, path, value) {\n        var part = path.shift();\n        if (part === \"__proto__\" || part === \"prototype\") {\n          return dst;\n        }\n        if (path.length > 0) {\n            dst[part] = setProp(dst[part] || {}, path, value);\n        } else {\n            var prevValue = dst[part];\n            if (prevValue && ifNotSet)\n                return dst;\n            if (prevValue)\n                value = [].concat(prevValue).concat(value);\n            dst[part] = value;\n        }\n        return dst;\n    }\n\n    if (typeof dst !== \"object\")\n        throw TypeError(\"dst must be an object\");\n    if (!path)\n        throw TypeError(\"path must be specified\");\n\n    path = path.split(\".\");\n    return setProp(dst, path, value);\n};\n\n/**\n * Decorator root (TypeScript).\n * @name util.decorateRoot\n * @type {Root}\n * @readonly\n */\nObject.defineProperty(util, \"decorateRoot\", {\n    get: function() {\n        return roots[\"decorated\"] || (roots[\"decorated\"] = new (require(\"./root\"))());\n    }\n});\n"], "names": [], "mappings": "AAAA;AAEA;;;CAGC,GACD,IAAI,OAAO,OAAO,OAAO;AAEzB,IAAI;AAEJ,IAAI,MACA;AAEJ,KAAK,OAAO;AACZ,KAAK,KAAK;AACV,KAAK,IAAI;AAET;;;CAGC,GACD,KAAK,EAAE,GAAG,KAAK,OAAO,CAAC;AAEvB;;;;CAIC,GACD,KAAK,OAAO,GAAG,SAAS,QAAQ,MAAM;IAClC,IAAI,QAAQ;QACR,IAAI,OAAQ,OAAO,IAAI,CAAC,SACpB,QAAQ,IAAI,MAAM,KAAK,MAAM,GAC7B,QAAQ;QACZ,MAAO,QAAQ,KAAK,MAAM,CACtB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;QACxC,OAAO;IACX;IACA,OAAO,EAAE;AACb;AAEA;;;;CAIC,GACD,KAAK,QAAQ,GAAG,SAAS,SAAS,KAAK;IACnC,IAAI,SAAS,CAAC,GACV,QAAS;IACb,MAAO,QAAQ,MAAM,MAAM,CAAE;QACzB,IAAI,MAAM,KAAK,CAAC,QAAQ,EACpB,MAAM,KAAK,CAAC,QAAQ;QACxB,IAAI,QAAQ,WACR,MAAM,CAAC,IAAI,GAAG;IACtB;IACA,OAAO;AACX;AAEA,IAAI,sBAAsB,OACtB,kBAAsB;AAE1B;;;;CAIC,GACD,KAAK,UAAU,GAAG,SAAS,WAAW,IAAI;IACtC,OAAO,uTAAuT,IAAI,CAAC;AACvU;AAEA;;;;CAIC,GACD,KAAK,QAAQ,GAAG,SAAS,SAAS,IAAI;IAClC,IAAI,CAAC,YAAY,IAAI,CAAC,SAAS,KAAK,UAAU,CAAC,OAC3C,OAAO,QAAQ,KAAK,OAAO,CAAC,qBAAqB,QAAQ,OAAO,CAAC,iBAAiB,UAAU;IAChG,OAAO,MAAM;AACjB;AAEA;;;;CAIC,GACD,KAAK,OAAO,GAAG,SAAS,QAAQ,GAAG;IAC/B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,SAAS,CAAC;AACvD;AAEA,IAAI,cAAc;AAElB;;;;CAIC,GACD,KAAK,SAAS,GAAG,SAAS,UAAU,GAAG;IACnC,OAAO,IAAI,SAAS,CAAC,GAAG,KACjB,IAAI,SAAS,CAAC,GACT,OAAO,CAAC,aAAa,SAAS,EAAE,EAAE,EAAE;QAAI,OAAO,GAAG,WAAW;IAAI;AACjF;AAEA;;;;;CAKC,GACD,KAAK,iBAAiB,GAAG,SAAS,kBAAkB,CAAC,EAAE,CAAC;IACpD,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE;AACtB;AAEA;;;;;;;CAOC,GACD,KAAK,YAAY,GAAG,SAAS,aAAa,IAAI,EAAE,QAAQ;IAEpD,sBAAsB,GACtB,IAAI,KAAK,KAAK,EAAE;QACZ,IAAI,YAAY,KAAK,KAAK,CAAC,IAAI,KAAK,UAAU;YAC1C,KAAK,YAAY,CAAC,MAAM,CAAC,KAAK,KAAK;YACnC,KAAK,KAAK,CAAC,IAAI,GAAG;YAClB,KAAK,YAAY,CAAC,GAAG,CAAC,KAAK,KAAK;QACpC;QACA,OAAO,KAAK,KAAK;IACrB;IAEA,wBAAwB,GACxB,IAAI,CAAC,MACD;IAEJ,IAAI,OAAO,IAAI,KAAK,YAAY,KAAK,IAAI;IACzC,KAAK,YAAY,CAAC,GAAG,CAAC;IACtB,KAAK,IAAI,GAAG,MAAM,gCAAgC;IAClD,OAAO,cAAc,CAAC,MAAM,SAAS;QAAE,OAAO;QAAM,YAAY;IAAM;IACtE,OAAO,cAAc,CAAC,KAAK,SAAS,EAAE,SAAS;QAAE,OAAO;QAAM,YAAY;IAAM;IAChF,OAAO;AACX;AAEA,IAAI,oBAAoB;AAExB;;;;CAIC,GACD,KAAK,YAAY,GAAG,SAAS,aAAa,MAAM;IAE5C,sBAAsB,GACtB,IAAI,OAAO,KAAK,EACZ,OAAO,OAAO,KAAK;IAEvB,wBAAwB,GACxB,IAAI,CAAC,MACD;IAEJ,IAAI,MAAM,IAAI,KAAK,SAAS,qBAAqB;IACjD,KAAK,YAAY,CAAC,GAAG,CAAC;IACtB,OAAO,cAAc,CAAC,QAAQ,SAAS;QAAE,OAAO;QAAK,YAAY;IAAM;IACvE,OAAO;AACX;AAGA;;;;;;;CAOC,GACD,KAAK,WAAW,GAAG,SAAS,YAAY,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ;IAC9D,SAAS,QAAQ,GAAG,EAAE,IAAI,EAAE,KAAK;QAC7B,IAAI,OAAO,KAAK,KAAK;QACrB,IAAI,SAAS,eAAe,SAAS,aAAa;YAChD,OAAO;QACT;QACA,IAAI,KAAK,MAAM,GAAG,GAAG;YACjB,GAAG,CAAC,KAAK,GAAG,QAAQ,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,MAAM;QAC/C,OAAO;YACH,IAAI,YAAY,GAAG,CAAC,KAAK;YACzB,IAAI,aAAa,UACb,OAAO;YACX,IAAI,WACA,QAAQ,EAAE,CAAC,MAAM,CAAC,WAAW,MAAM,CAAC;YACxC,GAAG,CAAC,KAAK,GAAG;QAChB;QACA,OAAO;IACX;IAEA,IAAI,OAAO,QAAQ,UACf,MAAM,UAAU;IACpB,IAAI,CAAC,MACD,MAAM,UAAU;IAEpB,OAAO,KAAK,KAAK,CAAC;IAClB,OAAO,QAAQ,KAAK,MAAM;AAC9B;AAEA;;;;;CAKC,GACD,OAAO,cAAc,CAAC,MAAM,gBAAgB;IACxC,KAAK;QACD,OAAO,KAAK,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,gGAAkB,GAAG;IAChF;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3615, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/types.js"], "sourcesContent": ["\"use strict\";\n\n/**\n * Common type constants.\n * @namespace\n */\nvar types = exports;\n\nvar util = require(\"./util\");\n\nvar s = [\n    \"double\",   // 0\n    \"float\",    // 1\n    \"int32\",    // 2\n    \"uint32\",   // 3\n    \"sint32\",   // 4\n    \"fixed32\",  // 5\n    \"sfixed32\", // 6\n    \"int64\",    // 7\n    \"uint64\",   // 8\n    \"sint64\",   // 9\n    \"fixed64\",  // 10\n    \"sfixed64\", // 11\n    \"bool\",     // 12\n    \"string\",   // 13\n    \"bytes\"     // 14\n];\n\nfunction bake(values, offset) {\n    var i = 0, o = {};\n    offset |= 0;\n    while (i < values.length) o[s[i + offset]] = values[i++];\n    return o;\n}\n\n/**\n * Basic type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n * @property {number} bytes=2 Ldelim wire type\n */\ntypes.basic = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2,\n    /* bytes    */ 2\n]);\n\n/**\n * Basic type defaults.\n * @type {Object.<string,*>}\n * @const\n * @property {number} double=0 Double default\n * @property {number} float=0 Float default\n * @property {number} int32=0 Int32 default\n * @property {number} uint32=0 Uint32 default\n * @property {number} sint32=0 Sint32 default\n * @property {number} fixed32=0 Fixed32 default\n * @property {number} sfixed32=0 Sfixed32 default\n * @property {number} int64=0 Int64 default\n * @property {number} uint64=0 Uint64 default\n * @property {number} sint64=0 Sint32 default\n * @property {number} fixed64=0 Fixed64 default\n * @property {number} sfixed64=0 Sfixed64 default\n * @property {boolean} bool=false Bool default\n * @property {string} string=\"\" String default\n * @property {Array.<number>} bytes=Array(0) Bytes default\n * @property {null} message=null Message default\n */\ntypes.defaults = bake([\n    /* double   */ 0,\n    /* float    */ 0,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 0,\n    /* sfixed32 */ 0,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 0,\n    /* sfixed64 */ 0,\n    /* bool     */ false,\n    /* string   */ \"\",\n    /* bytes    */ util.emptyArray,\n    /* message  */ null\n]);\n\n/**\n * Basic long type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n */\ntypes.long = bake([\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1\n], 7);\n\n/**\n * Allowed types for map keys with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n */\ntypes.mapKey = bake([\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2\n], 2);\n\n/**\n * Allowed types for packed repeated fields with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n */\ntypes.packed = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0\n]);\n"], "names": [], "mappings": "AAAA;AAEA;;;CAGC,GACD,IAAI,QAAQ;AAEZ,IAAI;AAEJ,IAAI,IAAI;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAY,KAAK;CACpB;AAED,SAAS,KAAK,MAAM,EAAE,MAAM;IACxB,IAAI,IAAI,GAAG,IAAI,CAAC;IAChB,UAAU;IACV,MAAO,IAAI,OAAO,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,GAAG,MAAM,CAAC,IAAI;IACxD,OAAO;AACX;AAEA;;;;;;;;;;;;;;;;;;;CAmBC,GACD,MAAM,KAAK,GAAG,KAAK;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;CAClB;AAED;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,MAAM,QAAQ,GAAG,KAAK;IAClB,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG,KAAK,UAAU;IAC9B,YAAY,GAAG;CAClB;AAED;;;;;;;;;CASC,GACD,MAAM,IAAI,GAAG,KAAK;IACd,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;CAClB,EAAE;AAEH;;;;;;;;;;;;;;;;CAgBC,GACD,MAAM,MAAM,GAAG,KAAK;IAChB,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;CAClB,EAAE;AAEH;;;;;;;;;;;;;;;;;CAiBC,GACD,MAAM,MAAM,GAAG,KAAK;IAChB,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;IACf,YAAY,GAAG;CAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3801, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/field.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = Field;\n\n// extends ReflectionObject\nvar ReflectionObject = require(\"./object\");\n((Field.prototype = Object.create(ReflectionObject.prototype)).constructor = Field).className = \"Field\";\n\nvar Enum  = require(\"./enum\"),\n    types = require(\"./types\"),\n    util  = require(\"./util\");\n\nvar Type; // cyclic\n\nvar ruleRe = /^required|optional|repeated$/;\n\n/**\n * Constructs a new message field instance. Note that {@link MapField|map fields} have their own class.\n * @name Field\n * @classdesc Reflected message field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a field from a field descriptor.\n * @param {string} name Field name\n * @param {IField} json Field descriptor\n * @returns {Field} Created field\n * @throws {TypeError} If arguments are invalid\n */\nField.fromJSON = function fromJSON(name, json) {\n    var field = new Field(name, json.id, json.type, json.rule, json.extend, json.options, json.comment);\n    if (json.edition)\n        field._edition = json.edition;\n    field._defaultEdition = \"proto3\";  // For backwards-compatibility.\n    return field;\n};\n\n/**\n * Not an actual constructor. Use {@link Field} instead.\n * @classdesc Base class of all reflected message fields. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports FieldBase\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction Field(name, id, type, rule, extend, options, comment) {\n\n    if (util.isObject(rule)) {\n        comment = extend;\n        options = rule;\n        rule = extend = undefined;\n    } else if (util.isObject(extend)) {\n        comment = options;\n        options = extend;\n        extend = undefined;\n    }\n\n    ReflectionObject.call(this, name, options);\n\n    if (!util.isInteger(id) || id < 0)\n        throw TypeError(\"id must be a non-negative integer\");\n\n    if (!util.isString(type))\n        throw TypeError(\"type must be a string\");\n\n    if (rule !== undefined && !ruleRe.test(rule = rule.toString().toLowerCase()))\n        throw TypeError(\"rule must be a string rule\");\n\n    if (extend !== undefined && !util.isString(extend))\n        throw TypeError(\"extend must be a string\");\n\n    /**\n     * Field rule, if any.\n     * @type {string|undefined}\n     */\n    if (rule === \"proto3_optional\") {\n        rule = \"optional\";\n    }\n    this.rule = rule && rule !== \"optional\" ? rule : undefined; // toJSON\n\n    /**\n     * Field type.\n     * @type {string}\n     */\n    this.type = type; // toJSON\n\n    /**\n     * Unique field id.\n     * @type {number}\n     */\n    this.id = id; // toJSON, marker\n\n    /**\n     * Extended type if different from parent.\n     * @type {string|undefined}\n     */\n    this.extend = extend || undefined; // toJSON\n\n    /**\n     * Whether this field is repeated.\n     * @type {boolean}\n     */\n    this.repeated = rule === \"repeated\";\n\n    /**\n     * Whether this field is a map or not.\n     * @type {boolean}\n     */\n    this.map = false;\n\n    /**\n     * Message this field belongs to.\n     * @type {Type|null}\n     */\n    this.message = null;\n\n    /**\n     * OneOf this field belongs to, if any,\n     * @type {OneOf|null}\n     */\n    this.partOf = null;\n\n    /**\n     * The field type's default value.\n     * @type {*}\n     */\n    this.typeDefault = null;\n\n    /**\n     * The field's default value on prototypes.\n     * @type {*}\n     */\n    this.defaultValue = null;\n\n    /**\n     * Whether this field's value should be treated as a long.\n     * @type {boolean}\n     */\n    this.long = util.Long ? types.long[type] !== undefined : /* istanbul ignore next */ false;\n\n    /**\n     * Whether this field's value is a buffer.\n     * @type {boolean}\n     */\n    this.bytes = type === \"bytes\";\n\n    /**\n     * Resolved type if not a basic type.\n     * @type {Type|Enum|null}\n     */\n    this.resolvedType = null;\n\n    /**\n     * Sister-field within the extended type if a declaring extension field.\n     * @type {Field|null}\n     */\n    this.extensionField = null;\n\n    /**\n     * Sister-field within the declaring namespace if an extended field.\n     * @type {Field|null}\n     */\n    this.declaringField = null;\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Determines whether this field is required.\n * @name Field#required\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"required\", {\n    get: function() {\n        return this._features.field_presence === \"LEGACY_REQUIRED\";\n    }\n});\n\n/**\n * Determines whether this field is not required.\n * @name Field#optional\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"optional\", {\n    get: function() {\n        return !this.required;\n    }\n});\n\n/**\n * Determines whether this field uses tag-delimited encoding.  In proto2 this\n * corresponded to group syntax.\n * @name Field#delimited\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"delimited\", {\n    get: function() {\n        return this.resolvedType instanceof Type &&\n            this._features.message_encoding === \"DELIMITED\";\n    }\n});\n\n/**\n * Determines whether this field is packed. Only relevant when repeated.\n * @name Field#packed\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"packed\", {\n    get: function() {\n        return this._features.repeated_field_encoding === \"PACKED\";\n    }\n});\n\n/**\n * Determines whether this field tracks presence.\n * @name Field#hasPresence\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"hasPresence\", {\n    get: function() {\n        if (this.repeated || this.map) {\n            return false;\n        }\n        return this.partOf || // oneofs\n            this.declaringField || this.extensionField || // extensions\n            this._features.field_presence !== \"IMPLICIT\";\n    }\n});\n\n/**\n * @override\n */\nField.prototype.setOption = function setOption(name, value, ifNotSet) {\n    return ReflectionObject.prototype.setOption.call(this, name, value, ifNotSet);\n};\n\n/**\n * Field descriptor.\n * @interface IField\n * @property {string} [rule=\"optional\"] Field rule\n * @property {string} type Field type\n * @property {number} id Field id\n * @property {Object.<string,*>} [options] Field options\n */\n\n/**\n * Extension field descriptor.\n * @interface IExtensionField\n * @extends IField\n * @property {string} extend Extended type\n */\n\n/**\n * Converts this field to a field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IField} Field descriptor\n */\nField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"edition\" , this._editionToJSON(),\n        \"rule\"    , this.rule !== \"optional\" && this.rule || undefined,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Resolves this field's type references.\n * @returns {Field} `this`\n * @throws {Error} If any reference cannot be resolved\n */\nField.prototype.resolve = function resolve() {\n\n    if (this.resolved)\n        return this;\n\n    if ((this.typeDefault = types.defaults[this.type]) === undefined) { // if not a basic type, resolve it\n        this.resolvedType = (this.declaringField ? this.declaringField.parent : this.parent).lookupTypeOrEnum(this.type);\n        if (this.resolvedType instanceof Type)\n            this.typeDefault = null;\n        else // instanceof Enum\n            this.typeDefault = this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]; // first defined\n    } else if (this.options && this.options.proto3_optional) {\n        // proto3 scalar value marked optional; should default to null\n        this.typeDefault = null;\n    }\n\n    // use explicitly set default value if present\n    if (this.options && this.options[\"default\"] != null) {\n        this.typeDefault = this.options[\"default\"];\n        if (this.resolvedType instanceof Enum && typeof this.typeDefault === \"string\")\n            this.typeDefault = this.resolvedType.values[this.typeDefault];\n    }\n\n    // remove unnecessary options\n    if (this.options) {\n        if (this.options.packed !== undefined && this.resolvedType && !(this.resolvedType instanceof Enum))\n            delete this.options.packed;\n        if (!Object.keys(this.options).length)\n            this.options = undefined;\n    }\n\n    // convert to internal data type if necesssary\n    if (this.long) {\n        this.typeDefault = util.Long.fromNumber(this.typeDefault, this.type.charAt(0) === \"u\");\n\n        /* istanbul ignore else */\n        if (Object.freeze)\n            Object.freeze(this.typeDefault); // long instances are meant to be immutable anyway (i.e. use small int cache that even requires it)\n\n    } else if (this.bytes && typeof this.typeDefault === \"string\") {\n        var buf;\n        if (util.base64.test(this.typeDefault))\n            util.base64.decode(this.typeDefault, buf = util.newBuffer(util.base64.length(this.typeDefault)), 0);\n        else\n            util.utf8.write(this.typeDefault, buf = util.newBuffer(util.utf8.length(this.typeDefault)), 0);\n        this.typeDefault = buf;\n    }\n\n    // take special care of maps and repeated fields\n    if (this.map)\n        this.defaultValue = util.emptyObject;\n    else if (this.repeated)\n        this.defaultValue = util.emptyArray;\n    else\n        this.defaultValue = this.typeDefault;\n\n    // ensure proper value on prototype\n    if (this.parent instanceof Type)\n        this.parent.ctor.prototype[this.name] = this.defaultValue;\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n\n/**\n * Infers field features from legacy syntax that may have been specified differently.\n * in older editions.\n * @param {string|undefined} edition The edition this proto is on, or undefined if pre-editions\n * @returns {object} The feature values to override\n */\nField.prototype._inferLegacyProtoFeatures = function _inferLegacyProtoFeatures(edition) {\n    if (edition !== \"proto2\" && edition !== \"proto3\") {\n        return {};\n    }\n\n    var features = {};\n\n    if (this.rule === \"required\") {\n        features.field_presence = \"LEGACY_REQUIRED\";\n    }\n    if (this.parent && types.defaults[this.type] === undefined) {\n        // We can't use resolvedType because types may not have been resolved yet.  However,\n        // legacy groups are always in the same scope as the field so we don't have to do a\n        // full scan of the tree.\n        var type = this.parent.get(this.type.split(\".\").pop());\n        if (type && type instanceof Type && type.group) {\n            features.message_encoding = \"DELIMITED\";\n        }\n    }\n    if (this.getOption(\"packed\") === true) {\n        features.repeated_field_encoding = \"PACKED\";\n    } else if (this.getOption(\"packed\") === false) {\n        features.repeated_field_encoding = \"EXPANDED\";\n    }\n    return features;\n};\n\n/**\n * @override\n */\nField.prototype._resolveFeatures = function _resolveFeatures(edition) {\n    return ReflectionObject.prototype._resolveFeatures.call(this, this._edition || edition);\n};\n\n/**\n * Decorator function as returned by {@link Field.d} and {@link MapField.d} (TypeScript).\n * @typedef FieldDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} fieldName Field name\n * @returns {undefined}\n */\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"string\"|\"bool\"|\"bytes\"|Object} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @param {T} [defaultValue] Default value\n * @returns {FieldDecorator} Decorator function\n * @template T extends number | number[] | Long | Long[] | string | string[] | boolean | boolean[] | Uint8Array | Uint8Array[] | Buffer | Buffer[]\n */\nField.d = function decorateField(fieldId, fieldType, fieldRule, defaultValue) {\n\n    // submessage: decorate the submessage and use its name as the type\n    if (typeof fieldType === \"function\")\n        fieldType = util.decorateType(fieldType).name;\n\n    // enum reference: create a reflected copy of the enum and keep reuseing it\n    else if (fieldType && typeof fieldType === \"object\")\n        fieldType = util.decorateEnum(fieldType).name;\n\n    return function fieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new Field(fieldName, fieldId, fieldType, fieldRule, { \"default\": defaultValue }));\n    };\n};\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {Constructor<T>|string} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @returns {FieldDecorator} Decorator function\n * @template T extends Message<T>\n * @variation 2\n */\n// like Field.d but without a default value\n\n// Sets up cyclic dependencies (called in index-light)\nField._configure = function configure(Type_) {\n    Type = Type_;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,2BAA2B;AAC3B,IAAI;AACJ,CAAC,CAAC,MAAM,SAAS,GAAG,OAAO,MAAM,CAAC,iBAAiB,SAAS,CAAC,EAAE,WAAW,GAAG,KAAK,EAAE,SAAS,GAAG;AAEhG,IAAI,wGACA,0GACA;AAEJ,IAAI,MAAM,SAAS;AAEnB,IAAI,SAAS;AAEb;;;;;;;;;;;;CAYC,GAED;;;;;;CAMC,GACD,MAAM,QAAQ,GAAG,SAAS,SAAS,IAAI,EAAE,IAAI;IACzC,IAAI,QAAQ,IAAI,MAAM,MAAM,KAAK,EAAE,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,MAAM,EAAE,KAAK,OAAO,EAAE,KAAK,OAAO;IAClG,IAAI,KAAK,OAAO,EACZ,MAAM,QAAQ,GAAG,KAAK,OAAO;IACjC,MAAM,eAAe,GAAG,UAAW,+BAA+B;IAClE,OAAO;AACX;AAEA;;;;;;;;;;;;;CAaC,GACD,SAAS,MAAM,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO;IAEzD,IAAI,KAAK,QAAQ,CAAC,OAAO;QACrB,UAAU;QACV,UAAU;QACV,OAAO,SAAS;IACpB,OAAO,IAAI,KAAK,QAAQ,CAAC,SAAS;QAC9B,UAAU;QACV,UAAU;QACV,SAAS;IACb;IAEA,iBAAiB,IAAI,CAAC,IAAI,EAAE,MAAM;IAElC,IAAI,CAAC,KAAK,SAAS,CAAC,OAAO,KAAK,GAC5B,MAAM,UAAU;IAEpB,IAAI,CAAC,KAAK,QAAQ,CAAC,OACf,MAAM,UAAU;IAEpB,IAAI,SAAS,aAAa,CAAC,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,GAAG,WAAW,KACrE,MAAM,UAAU;IAEpB,IAAI,WAAW,aAAa,CAAC,KAAK,QAAQ,CAAC,SACvC,MAAM,UAAU;IAEpB;;;KAGC,GACD,IAAI,SAAS,mBAAmB;QAC5B,OAAO;IACX;IACA,IAAI,CAAC,IAAI,GAAG,QAAQ,SAAS,aAAa,OAAO,WAAW,SAAS;IAErE;;;KAGC,GACD,IAAI,CAAC,IAAI,GAAG,MAAM,SAAS;IAE3B;;;KAGC,GACD,IAAI,CAAC,EAAE,GAAG,IAAI,iBAAiB;IAE/B;;;KAGC,GACD,IAAI,CAAC,MAAM,GAAG,UAAU,WAAW,SAAS;IAE5C;;;KAGC,GACD,IAAI,CAAC,QAAQ,GAAG,SAAS;IAEzB;;;KAGC,GACD,IAAI,CAAC,GAAG,GAAG;IAEX;;;KAGC,GACD,IAAI,CAAC,OAAO,GAAG;IAEf;;;KAGC,GACD,IAAI,CAAC,MAAM,GAAG;IAEd;;;KAGC,GACD,IAAI,CAAC,WAAW,GAAG;IAEnB;;;KAGC,GACD,IAAI,CAAC,YAAY,GAAG;IAEpB;;;KAGC,GACD,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,KAAK,YAAY,wBAAwB,GAAG;IAEpF;;;KAGC,GACD,IAAI,CAAC,KAAK,GAAG,SAAS;IAEtB;;;KAGC,GACD,IAAI,CAAC,YAAY,GAAG;IAEpB;;;KAGC,GACD,IAAI,CAAC,cAAc,GAAG;IAEtB;;;KAGC,GACD,IAAI,CAAC,cAAc,GAAG;IAEtB;;;KAGC,GACD,IAAI,CAAC,OAAO,GAAG;AACnB;AAEA;;;;;CAKC,GACD,OAAO,cAAc,CAAC,MAAM,SAAS,EAAE,YAAY;IAC/C,KAAK;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,KAAK;IAC7C;AACJ;AAEA;;;;;CAKC,GACD,OAAO,cAAc,CAAC,MAAM,SAAS,EAAE,YAAY;IAC/C,KAAK;QACD,OAAO,CAAC,IAAI,CAAC,QAAQ;IACzB;AACJ;AAEA;;;;;;CAMC,GACD,OAAO,cAAc,CAAC,MAAM,SAAS,EAAE,aAAa;IAChD,KAAK;QACD,OAAO,IAAI,CAAC,YAAY,YAAY,QAChC,IAAI,CAAC,SAAS,CAAC,gBAAgB,KAAK;IAC5C;AACJ;AAEA;;;;;CAKC,GACD,OAAO,cAAc,CAAC,MAAM,SAAS,EAAE,UAAU;IAC7C,KAAK;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,uBAAuB,KAAK;IACtD;AACJ;AAEA;;;;;CAKC,GACD,OAAO,cAAc,CAAC,MAAM,SAAS,EAAE,eAAe;IAClD,KAAK;QACD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE;YAC3B,OAAO;QACX;QACA,OAAO,IAAI,CAAC,MAAM,IAAI,SAAS;QAC3B,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,IAAI,aAAa;QAC3D,IAAI,CAAC,SAAS,CAAC,cAAc,KAAK;IAC1C;AACJ;AAEA;;CAEC,GACD,MAAM,SAAS,CAAC,SAAS,GAAG,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,QAAQ;IAChE,OAAO,iBAAiB,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,OAAO;AACxE;AAEA;;;;;;;CAOC,GAED;;;;;CAKC,GAED;;;;CAIC,GACD,MAAM,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,aAAa;IAClD,IAAI,eAAe,gBAAgB,QAAQ,cAAc,YAAY,IAAI;IACzE,OAAO,KAAK,QAAQ,CAAC;QACjB;QAAY,IAAI,CAAC,cAAc;QAC/B;QAAY,IAAI,CAAC,IAAI,KAAK,cAAc,IAAI,CAAC,IAAI,IAAI;QACrD;QAAY,IAAI,CAAC,IAAI;QACrB;QAAY,IAAI,CAAC,EAAE;QACnB;QAAY,IAAI,CAAC,MAAM;QACvB;QAAY,IAAI,CAAC,OAAO;QACxB;QAAY,eAAe,IAAI,CAAC,OAAO,GAAG;KAC7C;AACL;AAEA;;;;CAIC,GACD,MAAM,SAAS,CAAC,OAAO,GAAG,SAAS;IAE/B,IAAI,IAAI,CAAC,QAAQ,EACb,OAAO,IAAI;IAEf,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,WAAW;QAC9D,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,IAAI,CAAC,IAAI;QAC/G,IAAI,IAAI,CAAC,YAAY,YAAY,MAC7B,IAAI,CAAC,WAAW,GAAG;aAEnB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,gBAAgB;IAC/G,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;QACrD,8DAA8D;QAC9D,IAAI,CAAC,WAAW,GAAG;IACvB;IAEA,8CAA8C;IAC9C,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,MAAM;QACjD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU;QAC1C,IAAI,IAAI,CAAC,YAAY,YAAY,QAAQ,OAAO,IAAI,CAAC,WAAW,KAAK,UACjE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;IACrE;IAEA,6BAA6B;IAC7B,IAAI,IAAI,CAAC,OAAO,EAAE;QACd,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,aAAa,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,YAAY,IAAI,GAC7F,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;QAC9B,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EACjC,IAAI,CAAC,OAAO,GAAG;IACvB;IAEA,8CAA8C;IAC9C,IAAI,IAAI,CAAC,IAAI,EAAE;QACX,IAAI,CAAC,WAAW,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO;QAElF,wBAAwB,GACxB,IAAI,OAAO,MAAM,EACb,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,mGAAmG;IAE5I,OAAO,IAAI,IAAI,CAAC,KAAK,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,UAAU;QAC3D,IAAI;QACJ,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,GACjC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,KAAK,SAAS,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,IAAI;aAEjG,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,KAAK,SAAS,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,IAAI;QAChG,IAAI,CAAC,WAAW,GAAG;IACvB;IAEA,gDAAgD;IAChD,IAAI,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,YAAY,GAAG,KAAK,WAAW;SACnC,IAAI,IAAI,CAAC,QAAQ,EAClB,IAAI,CAAC,YAAY,GAAG,KAAK,UAAU;SAEnC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW;IAExC,mCAAmC;IACnC,IAAI,IAAI,CAAC,MAAM,YAAY,MACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY;IAE7D,OAAO,iBAAiB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;AACvD;AAEA;;;;;CAKC,GACD,MAAM,SAAS,CAAC,yBAAyB,GAAG,SAAS,0BAA0B,OAAO;IAClF,IAAI,YAAY,YAAY,YAAY,UAAU;QAC9C,OAAO,CAAC;IACZ;IAEA,IAAI,WAAW,CAAC;IAEhB,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY;QAC1B,SAAS,cAAc,GAAG;IAC9B;IACA,IAAI,IAAI,CAAC,MAAM,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,WAAW;QACxD,oFAAoF;QACpF,mFAAmF;QACnF,yBAAyB;QACzB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;QACnD,IAAI,QAAQ,gBAAgB,QAAQ,KAAK,KAAK,EAAE;YAC5C,SAAS,gBAAgB,GAAG;QAChC;IACJ;IACA,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,MAAM;QACnC,SAAS,uBAAuB,GAAG;IACvC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,OAAO;QAC3C,SAAS,uBAAuB,GAAG;IACvC;IACA,OAAO;AACX;AAEA;;CAEC,GACD,MAAM,SAAS,CAAC,gBAAgB,GAAG,SAAS,iBAAiB,OAAO;IAChE,OAAO,iBAAiB,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,IAAI;AACnF;AAEA;;;;;;;CAOC,GAED;;;;;;;;;;CAUC,GACD,MAAM,CAAC,GAAG,SAAS,cAAc,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY;IAExE,mEAAmE;IACnE,IAAI,OAAO,cAAc,YACrB,YAAY,KAAK,YAAY,CAAC,WAAW,IAAI;SAG5C,IAAI,aAAa,OAAO,cAAc,UACvC,YAAY,KAAK,YAAY,CAAC,WAAW,IAAI;IAEjD,OAAO,SAAS,eAAe,SAAS,EAAE,SAAS;QAC/C,KAAK,YAAY,CAAC,UAAU,WAAW,EAClC,GAAG,CAAC,IAAI,MAAM,WAAW,SAAS,WAAW,WAAW;YAAE,WAAW;QAAa;IAC3F;AACJ;AAEA;;;;;;;;;;CAUC,GACD,2CAA2C;AAE3C,sDAAsD;AACtD,MAAM,UAAU,GAAG,SAAS,UAAU,KAAK;IACvC,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4148, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/oneof.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = OneOf;\n\n// extends ReflectionObject\nvar ReflectionObject = require(\"./object\");\n((OneOf.prototype = Object.create(ReflectionObject.prototype)).constructor = OneOf).className = \"OneOf\";\n\nvar Field = require(\"./field\"),\n    util  = require(\"./util\");\n\n/**\n * Constructs a new oneof instance.\n * @classdesc Reflected oneof.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Oneof name\n * @param {string[]|Object.<string,*>} [fieldNames] Field names\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction OneOf(name, fieldNames, options, comment) {\n    if (!Array.isArray(fieldNames)) {\n        options = fieldNames;\n        fieldNames = undefined;\n    }\n    ReflectionObject.call(this, name, options);\n\n    /* istanbul ignore if */\n    if (!(fieldNames === undefined || Array.isArray(fieldNames)))\n        throw TypeError(\"fieldNames must be an Array\");\n\n    /**\n     * Field names that belong to this oneof.\n     * @type {string[]}\n     */\n    this.oneof = fieldNames || []; // toJSON, marker\n\n    /**\n     * Fields that belong to this oneof as an array for iteration.\n     * @type {Field[]}\n     * @readonly\n     */\n    this.fieldsArray = []; // declared readonly for conformance, possibly not yet added to parent\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Oneof descriptor.\n * @interface IOneOf\n * @property {Array.<string>} oneof Oneof field names\n * @property {Object.<string,*>} [options] Oneof options\n */\n\n/**\n * Constructs a oneof from a oneof descriptor.\n * @param {string} name Oneof name\n * @param {IOneOf} json Oneof descriptor\n * @returns {OneOf} Created oneof\n * @throws {TypeError} If arguments are invalid\n */\nOneOf.fromJSON = function fromJSON(name, json) {\n    return new OneOf(name, json.oneof, json.options, json.comment);\n};\n\n/**\n * Converts this oneof to a oneof descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IOneOf} Oneof descriptor\n */\nOneOf.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , this.options,\n        \"oneof\"   , this.oneof,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Adds the fields of the specified oneof to the parent if not already done so.\n * @param {OneOf} oneof The oneof\n * @returns {undefined}\n * @inner\n * @ignore\n */\nfunction addFieldsToParent(oneof) {\n    if (oneof.parent)\n        for (var i = 0; i < oneof.fieldsArray.length; ++i)\n            if (!oneof.fieldsArray[i].parent)\n                oneof.parent.add(oneof.fieldsArray[i]);\n}\n\n/**\n * Adds a field to this oneof and removes it from its current parent, if any.\n * @param {Field} field Field to add\n * @returns {OneOf} `this`\n */\nOneOf.prototype.add = function add(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    if (field.parent && field.parent !== this.parent)\n        field.parent.remove(field);\n    this.oneof.push(field.name);\n    this.fieldsArray.push(field);\n    field.partOf = this; // field.parent remains null\n    addFieldsToParent(this);\n    return this;\n};\n\n/**\n * Removes a field from this oneof and puts it back to the oneof's parent.\n * @param {Field} field Field to remove\n * @returns {OneOf} `this`\n */\nOneOf.prototype.remove = function remove(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    var index = this.fieldsArray.indexOf(field);\n\n    /* istanbul ignore if */\n    if (index < 0)\n        throw Error(field + \" is not a member of \" + this);\n\n    this.fieldsArray.splice(index, 1);\n    index = this.oneof.indexOf(field.name);\n\n    /* istanbul ignore else */\n    if (index > -1) // theoretical\n        this.oneof.splice(index, 1);\n\n    field.partOf = null;\n    return this;\n};\n\n/**\n * @override\n */\nOneOf.prototype.onAdd = function onAdd(parent) {\n    ReflectionObject.prototype.onAdd.call(this, parent);\n    var self = this;\n    // Collect present fields\n    for (var i = 0; i < this.oneof.length; ++i) {\n        var field = parent.get(this.oneof[i]);\n        if (field && !field.partOf) {\n            field.partOf = self;\n            self.fieldsArray.push(field);\n        }\n    }\n    // Add not yet present fields\n    addFieldsToParent(this);\n};\n\n/**\n * @override\n */\nOneOf.prototype.onRemove = function onRemove(parent) {\n    for (var i = 0, field; i < this.fieldsArray.length; ++i)\n        if ((field = this.fieldsArray[i]).parent)\n            field.parent.remove(field);\n    ReflectionObject.prototype.onRemove.call(this, parent);\n};\n\n/**\n * Determines whether this field corresponds to a synthetic oneof created for\n * a proto3 optional field.  No behavioral logic should depend on this, but it\n * can be relevant for reflection.\n * @name OneOf#isProto3Optional\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(OneOf.prototype, \"isProto3Optional\", {\n    get: function() {\n        if (this.fieldsArray == null || this.fieldsArray.length !== 1) {\n            return false;\n        }\n\n        var field = this.fieldsArray[0];\n        return field.options != null && field.options[\"proto3_optional\"] === true;\n    }\n});\n\n/**\n * Decorator function as returned by {@link OneOf.d} (TypeScript).\n * @typedef OneOfDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} oneofName OneOf name\n * @returns {undefined}\n */\n\n/**\n * OneOf decorator (TypeScript).\n * @function\n * @param {...string} fieldNames Field names\n * @returns {OneOfDecorator} Decorator function\n * @template T extends string\n */\nOneOf.d = function decorateOneOf() {\n    var fieldNames = new Array(arguments.length),\n        index = 0;\n    while (index < arguments.length)\n        fieldNames[index] = arguments[index++];\n    return function oneOfDecorator(prototype, oneofName) {\n        util.decorateType(prototype.constructor)\n            .add(new OneOf(oneofName, fieldNames));\n        Object.defineProperty(prototype, oneofName, {\n            get: util.oneOfGetter(fieldNames),\n            set: util.oneOfSetter(fieldNames)\n        });\n    };\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,2BAA2B;AAC3B,IAAI;AACJ,CAAC,CAAC,MAAM,SAAS,GAAG,OAAO,MAAM,CAAC,iBAAiB,SAAS,CAAC,EAAE,WAAW,GAAG,KAAK,EAAE,SAAS,GAAG;AAEhG,IAAI,0GACA;AAEJ;;;;;;;;;CASC,GACD,SAAS,MAAM,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO;IAC7C,IAAI,CAAC,MAAM,OAAO,CAAC,aAAa;QAC5B,UAAU;QACV,aAAa;IACjB;IACA,iBAAiB,IAAI,CAAC,IAAI,EAAE,MAAM;IAElC,sBAAsB,GACtB,IAAI,CAAC,CAAC,eAAe,aAAa,MAAM,OAAO,CAAC,WAAW,GACvD,MAAM,UAAU;IAEpB;;;KAGC,GACD,IAAI,CAAC,KAAK,GAAG,cAAc,EAAE,EAAE,iBAAiB;IAEhD;;;;KAIC,GACD,IAAI,CAAC,WAAW,GAAG,EAAE,EAAE,sEAAsE;IAE7F;;;KAGC,GACD,IAAI,CAAC,OAAO,GAAG;AACnB;AAEA;;;;;CAKC,GAED;;;;;;CAMC,GACD,MAAM,QAAQ,GAAG,SAAS,SAAS,IAAI,EAAE,IAAI;IACzC,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK,EAAE,KAAK,OAAO,EAAE,KAAK,OAAO;AACjE;AAEA;;;;CAIC,GACD,MAAM,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,aAAa;IAClD,IAAI,eAAe,gBAAgB,QAAQ,cAAc,YAAY,IAAI;IACzE,OAAO,KAAK,QAAQ,CAAC;QACjB;QAAY,IAAI,CAAC,OAAO;QACxB;QAAY,IAAI,CAAC,KAAK;QACtB;QAAY,eAAe,IAAI,CAAC,OAAO,GAAG;KAC7C;AACL;AAEA;;;;;;CAMC,GACD,SAAS,kBAAkB,KAAK;IAC5B,IAAI,MAAM,MAAM,EACZ;QAAA,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,WAAW,CAAC,MAAM,EAAE,EAAE,EAC5C,IAAI,CAAC,MAAM,WAAW,CAAC,EAAE,CAAC,MAAM,EAC5B,MAAM,MAAM,CAAC,GAAG,CAAC,MAAM,WAAW,CAAC,EAAE;IAAC;AACtD;AAEA;;;;CAIC,GACD,MAAM,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,KAAK;IAEpC,sBAAsB,GACtB,IAAI,CAAC,CAAC,iBAAiB,KAAK,GACxB,MAAM,UAAU;IAEpB,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI,CAAC,MAAM,EAC5C,MAAM,MAAM,CAAC,MAAM,CAAC;IACxB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,IAAI;IAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IACtB,MAAM,MAAM,GAAG,IAAI,EAAE,4BAA4B;IACjD,kBAAkB,IAAI;IACtB,OAAO,IAAI;AACf;AAEA;;;;CAIC,GACD,MAAM,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,KAAK;IAE1C,sBAAsB,GACtB,IAAI,CAAC,CAAC,iBAAiB,KAAK,GACxB,MAAM,UAAU;IAEpB,IAAI,QAAQ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;IAErC,sBAAsB,GACtB,IAAI,QAAQ,GACR,MAAM,MAAM,QAAQ,yBAAyB,IAAI;IAErD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO;IAC/B,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,IAAI;IAErC,wBAAwB,GACxB,IAAI,QAAQ,CAAC,GACT,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;IAE7B,MAAM,MAAM,GAAG;IACf,OAAO,IAAI;AACf;AAEA;;CAEC,GACD,MAAM,SAAS,CAAC,KAAK,GAAG,SAAS,MAAM,MAAM;IACzC,iBAAiB,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE;IAC5C,IAAI,OAAO,IAAI;IACf,yBAAyB;IACzB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,EAAG;QACxC,IAAI,QAAQ,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QACpC,IAAI,SAAS,CAAC,MAAM,MAAM,EAAE;YACxB,MAAM,MAAM,GAAG;YACf,KAAK,WAAW,CAAC,IAAI,CAAC;QAC1B;IACJ;IACA,6BAA6B;IAC7B,kBAAkB,IAAI;AAC1B;AAEA;;CAEC,GACD,MAAM,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAS,MAAM;IAC/C,IAAK,IAAI,IAAI,GAAG,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,EAClD,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM,EACpC,MAAM,MAAM,CAAC,MAAM,CAAC;IAC5B,iBAAiB,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE;AACnD;AAEA;;;;;;;CAOC,GACD,OAAO,cAAc,CAAC,MAAM,SAAS,EAAE,oBAAoB;IACvD,KAAK;QACD,IAAI,IAAI,CAAC,WAAW,IAAI,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,GAAG;YAC3D,OAAO;QACX;QAEA,IAAI,QAAQ,IAAI,CAAC,WAAW,CAAC,EAAE;QAC/B,OAAO,MAAM,OAAO,IAAI,QAAQ,MAAM,OAAO,CAAC,kBAAkB,KAAK;IACzE;AACJ;AAEA;;;;;;;CAOC,GAED;;;;;;CAMC,GACD,MAAM,CAAC,GAAG,SAAS;IACf,IAAI,aAAa,IAAI,MAAM,UAAU,MAAM,GACvC,QAAQ;IACZ,MAAO,QAAQ,UAAU,MAAM,CAC3B,UAAU,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ;IAC1C,OAAO,SAAS,eAAe,SAAS,EAAE,SAAS;QAC/C,KAAK,YAAY,CAAC,UAAU,WAAW,EAClC,GAAG,CAAC,IAAI,MAAM,WAAW;QAC9B,OAAO,cAAc,CAAC,WAAW,WAAW;YACxC,KAAK,KAAK,WAAW,CAAC;YACtB,KAAK,KAAK,WAAW,CAAC;QAC1B;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4318, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/object.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = ReflectionObject;\n\nReflectionObject.className = \"ReflectionObject\";\n\nconst OneOf = require(\"./oneof\");\nvar util = require(\"./util\");\n\nvar Root; // cyclic\n\n/* eslint-disable no-warning-comments */\n// TODO: Replace with embedded proto.\nvar editions2023Defaults = {enum_type: \"OPEN\", field_presence: \"EXPLICIT\", json_format: \"ALLOW\", message_encoding: \"LENGTH_PREFIXED\", repeated_field_encoding: \"PACKED\", utf8_validation: \"VERIFY\"};\nvar proto2Defaults = {enum_type: \"CLOSED\", field_presence: \"EXPLICIT\", json_format: \"LEGACY_BEST_EFFORT\", message_encoding: \"LENGTH_PREFIXED\", repeated_field_encoding: \"EXPANDED\", utf8_validation: \"NONE\"};\nvar proto3Defaults = {enum_type: \"OPEN\", field_presence: \"IMPLIC<PERSON>\", json_format: \"ALLOW\", message_encoding: \"LENGTH_PREFIXED\", repeated_field_encoding: \"PACKED\", utf8_validation: \"VERIFY\"};\n\n/**\n * Constructs a new reflection object instance.\n * @classdesc Base class of all reflection objects.\n * @constructor\n * @param {string} name Object name\n * @param {Object.<string,*>} [options] Declared options\n * @abstract\n */\nfunction ReflectionObject(name, options) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (options && !util.isObject(options))\n        throw TypeError(\"options must be an object\");\n\n    /**\n     * Options.\n     * @type {Object.<string,*>|undefined}\n     */\n    this.options = options; // toJSON\n\n    /**\n     * Parsed Options.\n     * @type {Array.<Object.<string,*>>|undefined}\n     */\n    this.parsedOptions = null;\n\n    /**\n     * Unique name within its namespace.\n     * @type {string}\n     */\n    this.name = name;\n\n    /**\n     * The edition specified for this object.  Only relevant for top-level objects.\n     * @type {string}\n     * @private\n     */\n    this._edition = null;\n\n    /**\n     * The default edition to use for this object if none is specified.  For legacy reasons,\n     * this is proto2 except in the JSON parsing case where it was proto3.\n     * @type {string}\n     * @private\n     */\n    this._defaultEdition = \"proto2\";\n\n    /**\n     * Resolved Features.\n     * @type {object}\n     * @private\n     */\n    this._features = {};\n\n    /**\n     * Whether or not features have been resolved.\n     * @type {boolean}\n     * @private\n     */\n    this._featuresResolved = false;\n\n    /**\n     * Parent namespace.\n     * @type {Namespace|null}\n     */\n    this.parent = null;\n\n    /**\n     * Whether already resolved or not.\n     * @type {boolean}\n     */\n    this.resolved = false;\n\n    /**\n     * Comment text, if any.\n     * @type {string|null}\n     */\n    this.comment = null;\n\n    /**\n     * Defining file name.\n     * @type {string|null}\n     */\n    this.filename = null;\n}\n\nObject.defineProperties(ReflectionObject.prototype, {\n\n    /**\n     * Reference to the root namespace.\n     * @name ReflectionObject#root\n     * @type {Root}\n     * @readonly\n     */\n    root: {\n        get: function() {\n            var ptr = this;\n            while (ptr.parent !== null)\n                ptr = ptr.parent;\n            return ptr;\n        }\n    },\n\n    /**\n     * Full name including leading dot.\n     * @name ReflectionObject#fullName\n     * @type {string}\n     * @readonly\n     */\n    fullName: {\n        get: function() {\n            var path = [ this.name ],\n                ptr = this.parent;\n            while (ptr) {\n                path.unshift(ptr.name);\n                ptr = ptr.parent;\n            }\n            return path.join(\".\");\n        }\n    }\n});\n\n/**\n * Converts this reflection object to its descriptor representation.\n * @returns {Object.<string,*>} Descriptor\n * @abstract\n */\nReflectionObject.prototype.toJSON = /* istanbul ignore next */ function toJSON() {\n    throw Error(); // not implemented, shouldn't happen\n};\n\n/**\n * Called when this object is added to a parent.\n * @param {ReflectionObject} parent Parent added to\n * @returns {undefined}\n */\nReflectionObject.prototype.onAdd = function onAdd(parent) {\n    if (this.parent && this.parent !== parent)\n        this.parent.remove(this);\n    this.parent = parent;\n    this.resolved = false;\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleAdd(this);\n};\n\n/**\n * Called when this object is removed from a parent.\n * @param {ReflectionObject} parent Parent removed from\n * @returns {undefined}\n */\nReflectionObject.prototype.onRemove = function onRemove(parent) {\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleRemove(this);\n    this.parent = null;\n    this.resolved = false;\n};\n\n/**\n * Resolves this objects type references.\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n    if (this.root instanceof Root)\n        this.resolved = true; // only if part of a root\n    return this;\n};\n\n/**\n * Resolves this objects editions features.\n * @param {string} edition The edition we're currently resolving for.\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype._resolveFeaturesRecursive = function _resolveFeaturesRecursive(edition) {\n    return this._resolveFeatures(this._edition || edition);\n};\n\n/**\n * Resolves child features from parent features\n * @param {string} edition The edition we're currently resolving for.\n * @returns {undefined}\n */\nReflectionObject.prototype._resolveFeatures = function _resolveFeatures(edition) {\n    if (this._featuresResolved) {\n        return;\n    }\n\n    var defaults = {};\n\n    /* istanbul ignore if */\n    if (!edition) {\n        throw new Error(\"Unknown edition for \" + this.fullName);\n    }\n\n    var protoFeatures = Object.assign(this.options ? Object.assign({},  this.options.features) : {},\n        this._inferLegacyProtoFeatures(edition));\n\n    if (this._edition) {\n        // For a namespace marked with a specific edition, reset defaults.\n        /* istanbul ignore else */\n        if (edition === \"proto2\") {\n            defaults = Object.assign({}, proto2Defaults);\n        } else if (edition === \"proto3\") {\n            defaults = Object.assign({}, proto3Defaults);\n        } else if (edition === \"2023\") {\n            defaults = Object.assign({}, editions2023Defaults);\n        } else {\n            throw new Error(\"Unknown edition: \" + edition);\n        }\n        this._features = Object.assign(defaults, protoFeatures || {});\n        this._featuresResolved = true;\n        return;\n    }\n\n    // fields in Oneofs aren't actually children of them, so we have to\n    // special-case it\n    /* istanbul ignore else */\n    if (this.partOf instanceof OneOf) {\n        var lexicalParentFeaturesCopy = Object.assign({}, this.partOf._features);\n        this._features = Object.assign(lexicalParentFeaturesCopy, protoFeatures || {});\n    } else if (this.declaringField) {\n        // Skip feature resolution of sister fields.\n    } else if (this.parent) {\n        var parentFeaturesCopy = Object.assign({}, this.parent._features);\n        this._features = Object.assign(parentFeaturesCopy, protoFeatures || {});\n    } else {\n        throw new Error(\"Unable to find a parent for \" + this.fullName);\n    }\n    if (this.extensionField) {\n        // Sister fields should have the same features as their extensions.\n        this.extensionField._features = this._features;\n    }\n    this._featuresResolved = true;\n};\n\n/**\n * Infers features from legacy syntax that may have been specified differently.\n * in older editions.\n * @param {string|undefined} edition The edition this proto is on, or undefined if pre-editions\n * @returns {object} The feature values to override\n */\nReflectionObject.prototype._inferLegacyProtoFeatures = function _inferLegacyProtoFeatures(/*edition*/) {\n    return {};\n};\n\n/**\n * Gets an option value.\n * @param {string} name Option name\n * @returns {*} Option value or `undefined` if not set\n */\nReflectionObject.prototype.getOption = function getOption(name) {\n    if (this.options)\n        return this.options[name];\n    return undefined;\n};\n\n/**\n * Sets an option.\n * @param {string} name Option name\n * @param {*} value Option value\n * @param {boolean|undefined} [ifNotSet] Sets the option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (!this.options)\n        this.options = {};\n    if (/^features\\./.test(name)) {\n        util.setProperty(this.options, name, value, ifNotSet);\n    } else if (!ifNotSet || this.options[name] === undefined) {\n        if (this.getOption(name) !== value) this.resolved = false;\n        this.options[name] = value;\n    }\n\n    return this;\n};\n\n/**\n * Sets a parsed option.\n * @param {string} name parsed Option name\n * @param {*} value Option value\n * @param {string} propName dot '.' delimited full path of property within the option to set. if undefined\\empty, will add a new option with that value\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setParsedOption = function setParsedOption(name, value, propName) {\n    if (!this.parsedOptions) {\n        this.parsedOptions = [];\n    }\n    var parsedOptions = this.parsedOptions;\n    if (propName) {\n        // If setting a sub property of an option then try to merge it\n        // with an existing option\n        var opt = parsedOptions.find(function (opt) {\n            return Object.prototype.hasOwnProperty.call(opt, name);\n        });\n        if (opt) {\n            // If we found an existing option - just merge the property value\n            // (If it's a feature, will just write over)\n            var newValue = opt[name];\n            util.setProperty(newValue, propName, value);\n        } else {\n            // otherwise, create a new option, set its property and add it to the list\n            opt = {};\n            opt[name] = util.setProperty({}, propName, value);\n            parsedOptions.push(opt);\n        }\n    } else {\n        // Always create a new option when setting the value of the option itself\n        var newOpt = {};\n        newOpt[name] = value;\n        parsedOptions.push(newOpt);\n    }\n\n    return this;\n};\n\n/**\n * Sets multiple options.\n * @param {Object.<string,*>} options Options to set\n * @param {boolean} [ifNotSet] Sets an option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOptions = function setOptions(options, ifNotSet) {\n    if (options)\n        for (var keys = Object.keys(options), i = 0; i < keys.length; ++i)\n            this.setOption(keys[i], options[keys[i]], ifNotSet);\n    return this;\n};\n\n/**\n * Converts this instance to its string representation.\n * @returns {string} Class name[, space, full name]\n */\nReflectionObject.prototype.toString = function toString() {\n    var className = this.constructor.className,\n        fullName  = this.fullName;\n    if (fullName.length)\n        return className + \" \" + fullName;\n    return className;\n};\n\n/**\n * Converts the edition this object is pinned to for JSON format.\n * @returns {string|undefined} The edition string for JSON representation\n */\nReflectionObject.prototype._editionToJSON = function _editionToJSON() {\n    if (!this._edition || this._edition === \"proto3\") {\n        // Avoid emitting proto3 since we need to default to it for backwards\n        // compatibility anyway.\n        return undefined;\n    }\n    return this._edition;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nReflectionObject._configure = function(Root_) {\n    Root = Root_;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,iBAAiB,SAAS,GAAG;AAE7B,MAAM;AACN,IAAI;AAEJ,IAAI,MAAM,SAAS;AAEnB,sCAAsC,GACtC,qCAAqC;AACrC,IAAI,uBAAuB;IAAC,WAAW;IAAQ,gBAAgB;IAAY,aAAa;IAAS,kBAAkB;IAAmB,yBAAyB;IAAU,iBAAiB;AAAQ;AAClM,IAAI,iBAAiB;IAAC,WAAW;IAAU,gBAAgB;IAAY,aAAa;IAAsB,kBAAkB;IAAmB,yBAAyB;IAAY,iBAAiB;AAAM;AAC3M,IAAI,iBAAiB;IAAC,WAAW;IAAQ,gBAAgB;IAAY,aAAa;IAAS,kBAAkB;IAAmB,yBAAyB;IAAU,iBAAiB;AAAQ;AAE5L;;;;;;;CAOC,GACD,SAAS,iBAAiB,IAAI,EAAE,OAAO;IAEnC,IAAI,CAAC,KAAK,QAAQ,CAAC,OACf,MAAM,UAAU;IAEpB,IAAI,WAAW,CAAC,KAAK,QAAQ,CAAC,UAC1B,MAAM,UAAU;IAEpB;;;KAGC,GACD,IAAI,CAAC,OAAO,GAAG,SAAS,SAAS;IAEjC;;;KAGC,GACD,IAAI,CAAC,aAAa,GAAG;IAErB;;;KAGC,GACD,IAAI,CAAC,IAAI,GAAG;IAEZ;;;;KAIC,GACD,IAAI,CAAC,QAAQ,GAAG;IAEhB;;;;;KAKC,GACD,IAAI,CAAC,eAAe,GAAG;IAEvB;;;;KAIC,GACD,IAAI,CAAC,SAAS,GAAG,CAAC;IAElB;;;;KAIC,GACD,IAAI,CAAC,iBAAiB,GAAG;IAEzB;;;KAGC,GACD,IAAI,CAAC,MAAM,GAAG;IAEd;;;KAGC,GACD,IAAI,CAAC,QAAQ,GAAG;IAEhB;;;KAGC,GACD,IAAI,CAAC,OAAO,GAAG;IAEf;;;KAGC,GACD,IAAI,CAAC,QAAQ,GAAG;AACpB;AAEA,OAAO,gBAAgB,CAAC,iBAAiB,SAAS,EAAE;IAEhD;;;;;KAKC,GACD,MAAM;QACF,KAAK;YACD,IAAI,MAAM,IAAI;YACd,MAAO,IAAI,MAAM,KAAK,KAClB,MAAM,IAAI,MAAM;YACpB,OAAO;QACX;IACJ;IAEA;;;;;KAKC,GACD,UAAU;QACN,KAAK;YACD,IAAI,OAAO;gBAAE,IAAI,CAAC,IAAI;aAAE,EACpB,MAAM,IAAI,CAAC,MAAM;YACrB,MAAO,IAAK;gBACR,KAAK,OAAO,CAAC,IAAI,IAAI;gBACrB,MAAM,IAAI,MAAM;YACpB;YACA,OAAO,KAAK,IAAI,CAAC;QACrB;IACJ;AACJ;AAEA;;;;CAIC,GACD,iBAAiB,SAAS,CAAC,MAAM,GAAG,wBAAwB,GAAG,SAAS;IACpE,MAAM,SAAS,oCAAoC;AACvD;AAEA;;;;CAIC,GACD,iBAAiB,SAAS,CAAC,KAAK,GAAG,SAAS,MAAM,MAAM;IACpD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,QAC/B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI;IAC3B,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,OAAO,OAAO,IAAI;IACtB,IAAI,gBAAgB,MAChB,KAAK,UAAU,CAAC,IAAI;AAC5B;AAEA;;;;CAIC,GACD,iBAAiB,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAS,MAAM;IAC1D,IAAI,OAAO,OAAO,IAAI;IACtB,IAAI,gBAAgB,MAChB,KAAK,aAAa,CAAC,IAAI;IAC3B,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,QAAQ,GAAG;AACpB;AAEA;;;CAGC,GACD,iBAAiB,SAAS,CAAC,OAAO,GAAG,SAAS;IAC1C,IAAI,IAAI,CAAC,QAAQ,EACb,OAAO,IAAI;IACf,IAAI,IAAI,CAAC,IAAI,YAAY,MACrB,IAAI,CAAC,QAAQ,GAAG,MAAM,yBAAyB;IACnD,OAAO,IAAI;AACf;AAEA;;;;CAIC,GACD,iBAAiB,SAAS,CAAC,yBAAyB,GAAG,SAAS,0BAA0B,OAAO;IAC7F,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,IAAI;AAClD;AAEA;;;;CAIC,GACD,iBAAiB,SAAS,CAAC,gBAAgB,GAAG,SAAS,iBAAiB,OAAO;IAC3E,IAAI,IAAI,CAAC,iBAAiB,EAAE;QACxB;IACJ;IAEA,IAAI,WAAW,CAAC;IAEhB,sBAAsB,GACtB,IAAI,CAAC,SAAS;QACV,MAAM,IAAI,MAAM,yBAAyB,IAAI,CAAC,QAAQ;IAC1D;IAEA,IAAI,gBAAgB,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,OAAO,MAAM,CAAC,CAAC,GAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,GAC1F,IAAI,CAAC,yBAAyB,CAAC;IAEnC,IAAI,IAAI,CAAC,QAAQ,EAAE;QACf,kEAAkE;QAClE,wBAAwB,GACxB,IAAI,YAAY,UAAU;YACtB,WAAW,OAAO,MAAM,CAAC,CAAC,GAAG;QACjC,OAAO,IAAI,YAAY,UAAU;YAC7B,WAAW,OAAO,MAAM,CAAC,CAAC,GAAG;QACjC,OAAO,IAAI,YAAY,QAAQ;YAC3B,WAAW,OAAO,MAAM,CAAC,CAAC,GAAG;QACjC,OAAO;YACH,MAAM,IAAI,MAAM,sBAAsB;QAC1C;QACA,IAAI,CAAC,SAAS,GAAG,OAAO,MAAM,CAAC,UAAU,iBAAiB,CAAC;QAC3D,IAAI,CAAC,iBAAiB,GAAG;QACzB;IACJ;IAEA,mEAAmE;IACnE,kBAAkB;IAClB,wBAAwB,GACxB,IAAI,IAAI,CAAC,MAAM,YAAY,OAAO;QAC9B,IAAI,4BAA4B,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS;QACvE,IAAI,CAAC,SAAS,GAAG,OAAO,MAAM,CAAC,2BAA2B,iBAAiB,CAAC;IAChF,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE;IAC5B,4CAA4C;IAChD,OAAO,IAAI,IAAI,CAAC,MAAM,EAAE;QACpB,IAAI,qBAAqB,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS;QAChE,IAAI,CAAC,SAAS,GAAG,OAAO,MAAM,CAAC,oBAAoB,iBAAiB,CAAC;IACzE,OAAO;QACH,MAAM,IAAI,MAAM,iCAAiC,IAAI,CAAC,QAAQ;IAClE;IACA,IAAI,IAAI,CAAC,cAAc,EAAE;QACrB,mEAAmE;QACnE,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS;IAClD;IACA,IAAI,CAAC,iBAAiB,GAAG;AAC7B;AAEA;;;;;CAKC,GACD,iBAAiB,SAAS,CAAC,yBAAyB,GAAG,SAAS;IAC5D,OAAO,CAAC;AACZ;AAEA;;;;CAIC,GACD,iBAAiB,SAAS,CAAC,SAAS,GAAG,SAAS,UAAU,IAAI;IAC1D,IAAI,IAAI,CAAC,OAAO,EACZ,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK;IAC7B,OAAO;AACX;AAEA;;;;;;CAMC,GACD,iBAAiB,SAAS,CAAC,SAAS,GAAG,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,QAAQ;IAC3E,IAAI,CAAC,IAAI,CAAC,OAAO,EACb,IAAI,CAAC,OAAO,GAAG,CAAC;IACpB,IAAI,cAAc,IAAI,CAAC,OAAO;QAC1B,KAAK,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,OAAO;IAChD,OAAO,IAAI,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;QACtD,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,OAAO,IAAI,CAAC,QAAQ,GAAG;QACpD,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG;IACzB;IAEA,OAAO,IAAI;AACf;AAEA;;;;;;CAMC,GACD,iBAAiB,SAAS,CAAC,eAAe,GAAG,SAAS,gBAAgB,IAAI,EAAE,KAAK,EAAE,QAAQ;IACvF,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;QACrB,IAAI,CAAC,aAAa,GAAG,EAAE;IAC3B;IACA,IAAI,gBAAgB,IAAI,CAAC,aAAa;IACtC,IAAI,UAAU;QACV,8DAA8D;QAC9D,0BAA0B;QAC1B,IAAI,MAAM,cAAc,IAAI,CAAC,SAAU,GAAG;YACtC,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK;QACrD;QACA,IAAI,KAAK;YACL,iEAAiE;YACjE,4CAA4C;YAC5C,IAAI,WAAW,GAAG,CAAC,KAAK;YACxB,KAAK,WAAW,CAAC,UAAU,UAAU;QACzC,OAAO;YACH,0EAA0E;YAC1E,MAAM,CAAC;YACP,GAAG,CAAC,KAAK,GAAG,KAAK,WAAW,CAAC,CAAC,GAAG,UAAU;YAC3C,cAAc,IAAI,CAAC;QACvB;IACJ,OAAO;QACH,yEAAyE;QACzE,IAAI,SAAS,CAAC;QACd,MAAM,CAAC,KAAK,GAAG;QACf,cAAc,IAAI,CAAC;IACvB;IAEA,OAAO,IAAI;AACf;AAEA;;;;;CAKC,GACD,iBAAiB,SAAS,CAAC,UAAU,GAAG,SAAS,WAAW,OAAO,EAAE,QAAQ;IACzE,IAAI,SACA,IAAK,IAAI,OAAO,OAAO,IAAI,CAAC,UAAU,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAC5D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;IAClD,OAAO,IAAI;AACf;AAEA;;;CAGC,GACD,iBAAiB,SAAS,CAAC,QAAQ,GAAG,SAAS;IAC3C,IAAI,YAAY,IAAI,CAAC,WAAW,CAAC,SAAS,EACtC,WAAY,IAAI,CAAC,QAAQ;IAC7B,IAAI,SAAS,MAAM,EACf,OAAO,YAAY,MAAM;IAC7B,OAAO;AACX;AAEA;;;CAGC,GACD,iBAAiB,SAAS,CAAC,cAAc,GAAG,SAAS;IACjD,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,UAAU;QAC9C,qEAAqE;QACrE,wBAAwB;QACxB,OAAO;IACX;IACA,OAAO,IAAI,CAAC,QAAQ;AACxB;AAEA,sDAAsD;AACtD,iBAAiB,UAAU,GAAG,SAAS,KAAK;IACxC,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4635, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/enum.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = Enum;\n\n// extends ReflectionObject\nvar ReflectionObject = require(\"./object\");\n((Enum.prototype = Object.create(ReflectionObject.prototype)).constructor = Enum).className = \"Enum\";\n\nvar Namespace = require(\"./namespace\"),\n    util = require(\"./util\");\n\n/**\n * Constructs a new enum instance.\n * @classdesc Reflected enum.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {Object.<string,number>} [values] Enum values as an object, by name\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this enum\n * @param {Object.<string,string>} [comments] The value comments for this enum\n * @param {Object.<string,Object<string,*>>|undefined} [valuesOptions] The value options for this enum\n */\nfunction Enum(name, values, options, comment, comments, valuesOptions) {\n    ReflectionObject.call(this, name, options);\n\n    if (values && typeof values !== \"object\")\n        throw TypeError(\"values must be an object\");\n\n    /**\n     * Enum values by id.\n     * @type {Object.<number,string>}\n     */\n    this.valuesById = {};\n\n    /**\n     * Enum values by name.\n     * @type {Object.<string,number>}\n     */\n    this.values = Object.create(this.valuesById); // toJSON, marker\n\n    /**\n     * Enum comment text.\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Value comment texts, if any.\n     * @type {Object.<string,string>}\n     */\n    this.comments = comments || {};\n\n    /**\n     * Values options, if any\n     * @type {Object<string, Object<string, *>>|undefined}\n     */\n    this.valuesOptions = valuesOptions;\n\n    /**\n     * Resolved values features, if any\n     * @type {Object<string, Object<string, *>>|undefined}\n     */\n    this._valuesFeatures = {};\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    // Note that values inherit valuesById on their prototype which makes them a TypeScript-\n    // compatible enum. This is used by pbts to write actual enum definitions that work for\n    // static and reflection code alike instead of emitting generic object definitions.\n\n    if (values)\n        for (var keys = Object.keys(values), i = 0; i < keys.length; ++i)\n            if (typeof values[keys[i]] === \"number\") // use forward entries only\n                this.valuesById[ this.values[keys[i]] = values[keys[i]] ] = keys[i];\n}\n\n/**\n * @override\n */\nEnum.prototype._resolveFeatures = function _resolveFeatures(edition) {\n    edition = this._edition || edition;\n    ReflectionObject.prototype._resolveFeatures.call(this, edition);\n\n    Object.keys(this.values).forEach(key => {\n        var parentFeaturesCopy = Object.assign({}, this._features);\n        this._valuesFeatures[key] = Object.assign(parentFeaturesCopy, this.valuesOptions && this.valuesOptions[key] && this.valuesOptions[key].features);\n    });\n\n    return this;\n};\n\n/**\n * Enum descriptor.\n * @interface IEnum\n * @property {Object.<string,number>} values Enum values\n * @property {Object.<string,*>} [options] Enum options\n */\n\n/**\n * Constructs an enum from an enum descriptor.\n * @param {string} name Enum name\n * @param {IEnum} json Enum descriptor\n * @returns {Enum} Created enum\n * @throws {TypeError} If arguments are invalid\n */\nEnum.fromJSON = function fromJSON(name, json) {\n    var enm = new Enum(name, json.values, json.options, json.comment, json.comments);\n    enm.reserved = json.reserved;\n    if (json.edition)\n        enm._edition = json.edition;\n    enm._defaultEdition = \"proto3\";  // For backwards-compatibility.\n    return enm;\n};\n\n/**\n * Converts this enum to an enum descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IEnum} Enum descriptor\n */\nEnum.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"edition\"       , this._editionToJSON(),\n        \"options\"       , this.options,\n        \"valuesOptions\" , this.valuesOptions,\n        \"values\"        , this.values,\n        \"reserved\"      , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"comment\"       , keepComments ? this.comment : undefined,\n        \"comments\"      , keepComments ? this.comments : undefined\n    ]);\n};\n\n/**\n * Adds a value to this enum.\n * @param {string} name Value name\n * @param {number} id Value id\n * @param {string} [comment] Comment, if any\n * @param {Object.<string, *>|undefined} [options] Options, if any\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a value with this name or id\n */\nEnum.prototype.add = function add(name, id, comment, options) {\n    // utilized by the parser but not by .fromJSON\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (!util.isInteger(id))\n        throw TypeError(\"id must be an integer\");\n\n    if (this.values[name] !== undefined)\n        throw Error(\"duplicate name '\" + name + \"' in \" + this);\n\n    if (this.isReservedId(id))\n        throw Error(\"id \" + id + \" is reserved in \" + this);\n\n    if (this.isReservedName(name))\n        throw Error(\"name '\" + name + \"' is reserved in \" + this);\n\n    if (this.valuesById[id] !== undefined) {\n        if (!(this.options && this.options.allow_alias))\n            throw Error(\"duplicate id \" + id + \" in \" + this);\n        this.values[name] = id;\n    } else\n        this.valuesById[this.values[name] = id] = name;\n\n    if (options) {\n        if (this.valuesOptions === undefined)\n            this.valuesOptions = {};\n        this.valuesOptions[name] = options || null;\n    }\n\n    this.comments[name] = comment || null;\n    return this;\n};\n\n/**\n * Removes a value from this enum\n * @param {string} name Value name\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `name` is not a name of this enum\n */\nEnum.prototype.remove = function remove(name) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    var val = this.values[name];\n    if (val == null)\n        throw Error(\"name '\" + name + \"' does not exist in \" + this);\n\n    delete this.valuesById[val];\n    delete this.values[name];\n    delete this.comments[name];\n    if (this.valuesOptions)\n        delete this.valuesOptions[name];\n\n    return this;\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,2BAA2B;AAC3B,IAAI;AACJ,CAAC,CAAC,KAAK,SAAS,GAAG,OAAO,MAAM,CAAC,iBAAiB,SAAS,CAAC,EAAE,WAAW,GAAG,IAAI,EAAE,SAAS,GAAG;AAE9F,IAAI,kHACA;AAEJ;;;;;;;;;;;CAWC,GACD,SAAS,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa;IACjE,iBAAiB,IAAI,CAAC,IAAI,EAAE,MAAM;IAElC,IAAI,UAAU,OAAO,WAAW,UAC5B,MAAM,UAAU;IAEpB;;;KAGC,GACD,IAAI,CAAC,UAAU,GAAG,CAAC;IAEnB;;;KAGC,GACD,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,iBAAiB;IAE/D;;;KAGC,GACD,IAAI,CAAC,OAAO,GAAG;IAEf;;;KAGC,GACD,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC;IAE7B;;;KAGC,GACD,IAAI,CAAC,aAAa,GAAG;IAErB;;;KAGC,GACD,IAAI,CAAC,eAAe,GAAG,CAAC;IAExB;;;KAGC,GACD,IAAI,CAAC,QAAQ,GAAG,WAAW,SAAS;IAEpC,wFAAwF;IACxF,uFAAuF;IACvF,mFAAmF;IAEnF,IAAI,QACA;QAAA,IAAK,IAAI,OAAO,OAAO,IAAI,CAAC,SAAS,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAC3D,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,UAC3B,IAAI,CAAC,UAAU,CAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAE,GAAG,IAAI,CAAC,EAAE;IAAA;AACnF;AAEA;;CAEC,GACD,KAAK,SAAS,CAAC,gBAAgB,GAAG,SAAS,iBAAiB,OAAO;IAC/D,UAAU,IAAI,CAAC,QAAQ,IAAI;IAC3B,iBAAiB,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE;IAEvD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QAC7B,IAAI,qBAAqB,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS;QACzD,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC,oBAAoB,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ;IACnJ;IAEA,OAAO,IAAI;AACf;AAEA;;;;;CAKC,GAED;;;;;;CAMC,GACD,KAAK,QAAQ,GAAG,SAAS,SAAS,IAAI,EAAE,IAAI;IACxC,IAAI,MAAM,IAAI,KAAK,MAAM,KAAK,MAAM,EAAE,KAAK,OAAO,EAAE,KAAK,OAAO,EAAE,KAAK,QAAQ;IAC/E,IAAI,QAAQ,GAAG,KAAK,QAAQ;IAC5B,IAAI,KAAK,OAAO,EACZ,IAAI,QAAQ,GAAG,KAAK,OAAO;IAC/B,IAAI,eAAe,GAAG,UAAW,+BAA+B;IAChE,OAAO;AACX;AAEA;;;;CAIC,GACD,KAAK,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,aAAa;IACjD,IAAI,eAAe,gBAAgB,QAAQ,cAAc,YAAY,IAAI;IACzE,OAAO,KAAK,QAAQ,CAAC;QACjB;QAAkB,IAAI,CAAC,cAAc;QACrC;QAAkB,IAAI,CAAC,OAAO;QAC9B;QAAkB,IAAI,CAAC,aAAa;QACpC;QAAkB,IAAI,CAAC,MAAM;QAC7B;QAAkB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG;QAC1E;QAAkB,eAAe,IAAI,CAAC,OAAO,GAAG;QAChD;QAAkB,eAAe,IAAI,CAAC,QAAQ,GAAG;KACpD;AACL;AAEA;;;;;;;;;CASC,GACD,KAAK,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO;IACxD,8CAA8C;IAE9C,IAAI,CAAC,KAAK,QAAQ,CAAC,OACf,MAAM,UAAU;IAEpB,IAAI,CAAC,KAAK,SAAS,CAAC,KAChB,MAAM,UAAU;IAEpB,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,WACtB,MAAM,MAAM,qBAAqB,OAAO,UAAU,IAAI;IAE1D,IAAI,IAAI,CAAC,YAAY,CAAC,KAClB,MAAM,MAAM,QAAQ,KAAK,qBAAqB,IAAI;IAEtD,IAAI,IAAI,CAAC,cAAc,CAAC,OACpB,MAAM,MAAM,WAAW,OAAO,sBAAsB,IAAI;IAE5D,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,WAAW;QACnC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,GAC1C,MAAM,MAAM,kBAAkB,KAAK,SAAS,IAAI;QACpD,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;IACxB,OACI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG;IAE9C,IAAI,SAAS;QACT,IAAI,IAAI,CAAC,aAAa,KAAK,WACvB,IAAI,CAAC,aAAa,GAAG,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,WAAW;IAC1C;IAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW;IACjC,OAAO,IAAI;AACf;AAEA;;;;;;CAMC,GACD,KAAK,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,IAAI;IAExC,IAAI,CAAC,KAAK,QAAQ,CAAC,OACf,MAAM,UAAU;IAEpB,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK;IAC3B,IAAI,OAAO,MACP,MAAM,MAAM,WAAW,OAAO,yBAAyB,IAAI;IAE/D,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI;IAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;IACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK;IAC1B,IAAI,IAAI,CAAC,aAAa,EAClB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK;IAEnC,OAAO,IAAI;AACf;AAEA;;;;CAIC,GACD,KAAK,SAAS,CAAC,YAAY,GAAG,SAAS,aAAa,EAAE;IAClD,OAAO,UAAU,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE;AACjD;AAEA;;;;CAIC,GACD,KAAK,SAAS,CAAC,cAAc,GAAG,SAAS,eAAe,IAAI;IACxD,OAAO,UAAU,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE;AACnD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4804, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/encoder.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = encoder;\n\nvar Enum     = require(\"./enum\"),\n    types    = require(\"./types\"),\n    util     = require(\"./util\");\n\n/**\n * Generates a partial message type encoder.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genTypePartial(gen, field, fieldIndex, ref) {\n    return field.delimited\n        ? gen(\"types[%i].encode(%s,w.uint32(%i)).uint32(%i)\", fieldIndex, ref, (field.id << 3 | 3) >>> 0, (field.id << 3 | 4) >>> 0)\n        : gen(\"types[%i].encode(%s,w.uint32(%i).fork()).ldelim()\", fieldIndex, ref, (field.id << 3 | 2) >>> 0);\n}\n\n/**\n * Generates an encoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction encoder(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var gen = util.codegen([\"m\", \"w\"], mtype.name + \"$encode\")\n    (\"if(!w)\")\n        (\"w=Writer.create()\");\n\n    var i, ref;\n\n    // \"when a message is serialized its known fields should be written sequentially by field number\"\n    var fields = /* initializes */ mtype.fieldsArray.slice().sort(util.compareFieldsById);\n\n    for (var i = 0; i < fields.length; ++i) {\n        var field    = fields[i].resolve(),\n            index    = mtype._fieldsArray.indexOf(field),\n            type     = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            wireType = types.basic[type];\n            ref      = \"m\" + util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) {\n            gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j)){\", ref, field.name) // !== undefined && !== null\n        (\"for(var ks=Object.keys(%s),i=0;i<ks.length;++i){\", ref)\n            (\"w.uint32(%i).fork().uint32(%i).%s(ks[i])\", (field.id << 3 | 2) >>> 0, 8 | types.mapKey[field.keyType], field.keyType);\n            if (wireType === undefined) gen\n            (\"types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()\", index, ref); // can't be groups\n            else gen\n            (\".uint32(%i).%s(%s[ks[i]]).ldelim()\", 16 | wireType, type, ref);\n            gen\n        (\"}\")\n    (\"}\");\n\n            // Repeated fields\n        } else if (field.repeated) { gen\n    (\"if(%s!=null&&%s.length){\", ref, ref); // !== undefined && !== null\n\n            // Packed repeated\n            if (field.packed && types.packed[type] !== undefined) { gen\n\n        (\"w.uint32(%i).fork()\", (field.id << 3 | 2) >>> 0)\n        (\"for(var i=0;i<%s.length;++i)\", ref)\n            (\"w.%s(%s[i])\", type, ref)\n        (\"w.ldelim()\");\n\n            // Non-packed\n            } else { gen\n\n        (\"for(var i=0;i<%s.length;++i)\", ref);\n                if (wireType === undefined)\n            genTypePartial(gen, field, index, ref + \"[i]\");\n                else gen\n            (\"w.uint32(%i).%s(%s[i])\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n            } gen\n    (\"}\");\n\n        // Non-repeated\n        } else {\n            if (field.optional) gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j))\", ref, field.name); // !== undefined && !== null\n\n            if (wireType === undefined)\n        genTypePartial(gen, field, index, ref);\n            else gen\n        (\"w.uint32(%i).%s(%s)\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n        }\n    }\n\n    return gen\n    (\"return w\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,IAAI,wGACA,0GACA;AAEJ;;;;;;;;CAQC,GACD,SAAS,eAAe,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAC/C,OAAO,MAAM,SAAS,GAChB,IAAI,gDAAgD,YAAY,KAAK,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,KACxH,IAAI,qDAAqD,YAAY,KAAK,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM;AAC5G;AAEA;;;;CAIC,GACD,SAAS,QAAQ,KAAK;IAClB,0EAA0E,GAC1E,IAAI,MAAM,KAAK,OAAO,CAAC;QAAC;QAAK;KAAI,EAAE,MAAM,IAAI,GAAG,WAC/C,UACI;IAEL,IAAI,GAAG;IAEP,iGAAiG;IACjG,IAAI,SAAS,eAAe,GAAG,MAAM,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,iBAAiB;IAEpF,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;QACpC,IAAI,QAAW,MAAM,CAAC,EAAE,CAAC,OAAO,IAC5B,QAAW,MAAM,YAAY,CAAC,OAAO,CAAC,QACtC,OAAW,MAAM,YAAY,YAAY,OAAO,UAAU,MAAM,IAAI,EACpE,WAAW,MAAM,KAAK,CAAC,KAAK;QAC5B,MAAW,MAAM,KAAK,QAAQ,CAAC,MAAM,IAAI;QAE7C,aAAa;QACb,IAAI,MAAM,GAAG,EAAE;YACX,IACP,mDAAmD,KAAK,MAAM,IAAI,EAAE,4BAA4B;aAC5F,oDAAoD,KAChD,4CAA4C,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,MAAM,CAAC,MAAM,OAAO,CAAC,EAAE,MAAM,OAAO;YACtH,IAAI,aAAa,WAAW,IAC3B,qEAAqE,OAAO,MAAM,kBAAkB;iBAChG,IACJ,sCAAsC,KAAK,UAAU,MAAM;YAC5D,IACH,KACJ;QAEO,kBAAkB;QACtB,OAAO,IAAI,MAAM,QAAQ,EAAE;YAAE,IAChC,4BAA4B,KAAK,MAAM,4BAA4B;YAE5D,kBAAkB;YAClB,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,KAAK,KAAK,WAAW;gBAAE,IAE3D,uBAAuB,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,GAC/C,gCAAgC,KAC5B,eAAe,MAAM,KACzB;YAEG,aAAa;YACb,OAAO;gBAAE,IAEZ,gCAAgC;gBACzB,IAAI,aAAa,WACrB,eAAe,KAAK,OAAO,OAAO,MAAM;qBAC/B,IACR,0BAA0B,CAAC,MAAM,EAAE,IAAI,IAAI,QAAQ,MAAM,GAAG,MAAM;YAEnE;YAAE,IACT;QAEG,eAAe;QACf,OAAO;YACH,IAAI,MAAM,QAAQ,EAAE,IAC3B,kDAAkD,KAAK,MAAM,IAAI,GAAG,4BAA4B;YAEzF,IAAI,aAAa,WACrB,eAAe,KAAK,OAAO,OAAO;iBACzB,IACR,uBAAuB,CAAC,MAAM,EAAE,IAAI,IAAI,QAAQ,MAAM,GAAG,MAAM;QAEhE;IACJ;IAEA,OAAO,IACN;AACD,yEAAyE,GAC7E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4867, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/writer_buffer.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = <PERSON><PERSON>erWriter;\n\n// extends Writer\nvar Writer = require(\"./writer\");\n(BufferWriter.prototype = Object.create(Writer.prototype)).constructor = BufferWriter;\n\nvar util = require(\"./util/minimal\");\n\n/**\n * Constructs a new buffer writer instance.\n * @classdesc Wire format writer using node buffers.\n * @extends Writer\n * @constructor\n */\nfunction BufferWriter() {\n    Writer.call(this);\n}\n\nBufferWriter._configure = function () {\n    /**\n     * Allocates a buffer of the specified size.\n     * @function\n     * @param {number} size Buffer size\n     * @returns {Buffer} Buffer\n     */\n    BufferWriter.alloc = util._Buffer_allocUnsafe;\n\n    BufferWriter.writeBytesBuffer = util.Buffer && util.Buffer.prototype instanceof Uint8Array && util.Buffer.prototype.set.name === \"set\"\n        ? function writeBytesBuffer_set(val, buf, pos) {\n          buf.set(val, pos); // faster than copy (requires node >= 4 where <PERSON><PERSON><PERSON> extend Uint8Array and set is properly inherited)\n          // also works for plain array values\n        }\n        /* istanbul ignore next */\n        : function writeBytesBuffer_copy(val, buf, pos) {\n          if (val.copy) // Buffer values\n            val.copy(buf, pos, 0, val.length);\n          else for (var i = 0; i < val.length;) // plain array values\n            buf[pos++] = val[i++];\n        };\n};\n\n\n/**\n * @override\n */\nBufferWriter.prototype.bytes = function write_bytes_buffer(value) {\n    if (util.isString(value))\n        value = util._Buffer_from(value, \"base64\");\n    var len = value.length >>> 0;\n    this.uint32(len);\n    if (len)\n        this._push(BufferWriter.writeBytesBuffer, len, value);\n    return this;\n};\n\nfunction writeStringBuffer(val, buf, pos) {\n    if (val.length < 40) // plain js is faster for short strings (probably due to redundant assertions)\n        util.utf8.write(val, buf, pos);\n    else if (buf.utf8Write)\n        buf.utf8Write(val, pos);\n    else\n        buf.write(val, pos);\n}\n\n/**\n * @override\n */\nBufferWriter.prototype.string = function write_string_buffer(value) {\n    var len = util.Buffer.byteLength(value);\n    this.uint32(len);\n    if (len)\n        this._push(writeStringBuffer, len, value);\n    return this;\n};\n\n\n/**\n * Finishes the write operation.\n * @name BufferWriter#finish\n * @function\n * @returns {Buffer} Finished buffer\n */\n\nBufferWriter._configure();\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,iBAAiB;AACjB,IAAI;AACJ,CAAC,aAAa,SAAS,GAAG,OAAO,MAAM,CAAC,OAAO,SAAS,CAAC,EAAE,WAAW,GAAG;AAEzE,IAAI;AAEJ;;;;;CAKC,GACD,SAAS;IACL,OAAO,IAAI,CAAC,IAAI;AACpB;AAEA,aAAa,UAAU,GAAG;IACtB;;;;;KAKC,GACD,aAAa,KAAK,GAAG,KAAK,mBAAmB;IAE7C,aAAa,gBAAgB,GAAG,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,SAAS,YAAY,cAAc,KAAK,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,KAAK,QAC3H,SAAS,qBAAqB,GAAG,EAAE,GAAG,EAAE,GAAG;QAC3C,IAAI,GAAG,CAAC,KAAK,MAAM,sGAAsG;IACzH,oCAAoC;IACtC,IAEE,SAAS,sBAAsB,GAAG,EAAE,GAAG,EAAE,GAAG;QAC5C,IAAI,IAAI,IAAI,EACV,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,MAAM;aAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EACjC,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IACzB;AACR;AAGA;;CAEC,GACD,aAAa,SAAS,CAAC,KAAK,GAAG,SAAS,mBAAmB,KAAK;IAC5D,IAAI,KAAK,QAAQ,CAAC,QACd,QAAQ,KAAK,YAAY,CAAC,OAAO;IACrC,IAAI,MAAM,MAAM,MAAM,KAAK;IAC3B,IAAI,CAAC,MAAM,CAAC;IACZ,IAAI,KACA,IAAI,CAAC,KAAK,CAAC,aAAa,gBAAgB,EAAE,KAAK;IACnD,OAAO,IAAI;AACf;AAEA,SAAS,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG;IACpC,IAAI,IAAI,MAAM,GAAG,IACb,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK;SACzB,IAAI,IAAI,SAAS,EAClB,IAAI,SAAS,CAAC,KAAK;SAEnB,IAAI,KAAK,CAAC,KAAK;AACvB;AAEA;;CAEC,GACD,aAAa,SAAS,CAAC,MAAM,GAAG,SAAS,oBAAoB,KAAK;IAC9D,IAAI,MAAM,KAAK,MAAM,CAAC,UAAU,CAAC;IACjC,IAAI,CAAC,MAAM,CAAC;IACZ,IAAI,KACA,IAAI,CAAC,KAAK,CAAC,mBAAmB,KAAK;IACvC,OAAO,IAAI;AACf;AAGA;;;;;CAKC,GAED,aAAa,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4929, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/reader_buffer.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = BufferReader;\n\n// extends Reader\nvar Reader = require(\"./reader\");\n(BufferReader.prototype = Object.create(Reader.prototype)).constructor = BufferReader;\n\nvar util = require(\"./util/minimal\");\n\n/**\n * Constructs a new buffer reader instance.\n * @classdesc Wire format reader using node buffers.\n * @extends Reader\n * @constructor\n * @param {Buffer} buffer Buffer to read from\n */\nfunction BufferReader(buffer) {\n    Reader.call(this, buffer);\n\n    /**\n     * Read buffer.\n     * @name BufferReader#buf\n     * @type {Buffer}\n     */\n}\n\nBufferReader._configure = function () {\n    /* istanbul ignore else */\n    if (util.Buffer)\n        BufferReader.prototype._slice = util.Buffer.prototype.slice;\n};\n\n\n/**\n * @override\n */\nBufferReader.prototype.string = function read_string_buffer() {\n    var len = this.uint32(); // modifies pos\n    return this.buf.utf8Slice\n        ? this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + len, this.len))\n        : this.buf.toString(\"utf-8\", this.pos, this.pos = Math.min(this.pos + len, this.len));\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @name BufferReader#bytes\n * @function\n * @returns {Buffer} Value read\n */\n\nBufferReader._configure();\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,iBAAiB;AACjB,IAAI;AACJ,CAAC,aAAa,SAAS,GAAG,OAAO,MAAM,CAAC,OAAO,SAAS,CAAC,EAAE,WAAW,GAAG;AAEzE,IAAI;AAEJ;;;;;;CAMC,GACD,SAAS,aAAa,MAAM;IACxB,OAAO,IAAI,CAAC,IAAI,EAAE;AAElB;;;;KAIC,GACL;AAEA,aAAa,UAAU,GAAG;IACtB,wBAAwB,GACxB,IAAI,KAAK,MAAM,EACX,aAAa,SAAS,CAAC,MAAM,GAAG,KAAK,MAAM,CAAC,SAAS,CAAC,KAAK;AACnE;AAGA;;CAEC,GACD,aAAa,SAAS,CAAC,MAAM,GAAG,SAAS;IACrC,IAAI,MAAM,IAAI,CAAC,MAAM,IAAI,eAAe;IACxC,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,GACnB,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,GAAG,KACzE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,GAAG;AAC3F;AAEA;;;;;CAKC,GAED,aAAa,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4968, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/index-minimal.js"], "sourcesContent": ["\"use strict\";\nvar protobuf = exports;\n\n/**\n * Build type, one of `\"full\"`, `\"light\"` or `\"minimal\"`.\n * @name build\n * @type {string}\n * @const\n */\nprotobuf.build = \"minimal\";\n\n// Serialization\nprotobuf.Writer       = require(\"./writer\");\nprotobuf.BufferWriter = require(\"./writer_buffer\");\nprotobuf.Reader       = require(\"./reader\");\nprotobuf.BufferReader = require(\"./reader_buffer\");\n\n// Utility\nprotobuf.util         = require(\"./util/minimal\");\nprotobuf.rpc          = require(\"./rpc\");\nprotobuf.roots        = require(\"./roots\");\nprotobuf.configure    = configure;\n\n/* istanbul ignore next */\n/**\n * Reconfigures the library according to the environment.\n * @returns {undefined}\n */\nfunction configure() {\n    protobuf.util._configure();\n    protobuf.Writer._configure(protobuf.BufferWriter);\n    protobuf.Reader._configure(protobuf.BufferReader);\n}\n\n// Set up buffer utility according to the environment\nconfigure();\n"], "names": [], "mappings": "AAAA;AACA,IAAI,WAAW;AAEf;;;;;CAKC,GACD,SAAS,KAAK,GAAG;AAEjB,gBAAgB;AAChB,SAAS,MAAM;AACf,SAAS,YAAY;AACrB,SAAS,MAAM;AACf,SAAS,YAAY;AAErB,UAAU;AACV,SAAS,IAAI;AACb,SAAS,GAAG;AACZ,SAAS,KAAK;AACd,SAAS,SAAS,GAAM;AAExB,wBAAwB,GACxB;;;CAGC,GACD,SAAS;IACL,SAAS,IAAI,CAAC,UAAU;IACxB,SAAS,MAAM,CAAC,UAAU,CAAC,SAAS,YAAY;IAChD,SAAS,MAAM,CAAC,UAAU,CAAC,SAAS,YAAY;AACpD;AAEA,qDAAqD;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5001, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/index-light.js"], "sourcesContent": ["\"use strict\";\nvar protobuf = module.exports = require(\"./index-minimal\");\n\nprotobuf.build = \"light\";\n\n/**\n * A node-style callback as used by {@link load} and {@link Root#load}.\n * @typedef LoadCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Root} [root] Root, if there hasn't been an error\n * @returns {undefined}\n */\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} root Root namespace, defaults to create a new one if omitted.\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n */\nfunction load(filename, root, callback) {\n    if (typeof root === \"function\") {\n        callback = root;\n        root = new protobuf.Root();\n    } else if (!root)\n        root = new protobuf.Root();\n    return root.load(filename, callback);\n}\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and returns a promise.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Promise<Root>} Promise\n * @see {@link Root#load}\n * @variation 3\n */\n// function load(filename:string, [root:Root]):Promise<Root>\n\nprotobuf.load = load;\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into a common root namespace (node only).\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n * @see {@link Root#loadSync}\n */\nfunction loadSync(filename, root) {\n    if (!root)\n        root = new protobuf.Root();\n    return root.loadSync(filename);\n}\n\nprotobuf.loadSync = loadSync;\n\n// Serialization\nprotobuf.encoder          = require(\"./encoder\");\nprotobuf.decoder          = require(\"./decoder\");\nprotobuf.verifier         = require(\"./verifier\");\nprotobuf.converter        = require(\"./converter\");\n\n// Reflection\nprotobuf.ReflectionObject = require(\"./object\");\nprotobuf.Namespace        = require(\"./namespace\");\nprotobuf.Root             = require(\"./root\");\nprotobuf.Enum             = require(\"./enum\");\nprotobuf.Type             = require(\"./type\");\nprotobuf.Field            = require(\"./field\");\nprotobuf.OneOf            = require(\"./oneof\");\nprotobuf.MapField         = require(\"./mapfield\");\nprotobuf.Service          = require(\"./service\");\nprotobuf.Method           = require(\"./method\");\n\n// Runtime\nprotobuf.Message          = require(\"./message\");\nprotobuf.wrappers         = require(\"./wrappers\");\n\n// Utility\nprotobuf.types            = require(\"./types\");\nprotobuf.util             = require(\"./util\");\n\n// Set up possibly cyclic reflection dependencies\nprotobuf.ReflectionObject._configure(protobuf.Root);\nprotobuf.Namespace._configure(protobuf.Type, protobuf.Service, protobuf.Enum);\nprotobuf.Root._configure(protobuf.Type);\nprotobuf.Field._configure(protobuf.Type);\n"], "names": [], "mappings": "AAAA;AACA,IAAI,WAAW,OAAO,OAAO;AAE7B,SAAS,KAAK,GAAG;AAEjB;;;;;;;CAOC,GAED;;;;;;;CAOC,GACD,SAAS,KAAK,QAAQ,EAAE,IAAI,EAAE,QAAQ;IAClC,IAAI,OAAO,SAAS,YAAY;QAC5B,WAAW;QACX,OAAO,IAAI,SAAS,IAAI;IAC5B,OAAO,IAAI,CAAC,MACR,OAAO,IAAI,SAAS,IAAI;IAC5B,OAAO,KAAK,IAAI,CAAC,UAAU;AAC/B;AAEA;;;;;;;;;CASC,GACD,kEAAkE;AAElE;;;;;;;;;CASC,GACD,4DAA4D;AAE5D,SAAS,IAAI,GAAG;AAEhB;;;;;;;CAOC,GACD,SAAS,SAAS,QAAQ,EAAE,IAAI;IAC5B,IAAI,CAAC,MACD,OAAO,IAAI,SAAS,IAAI;IAC5B,OAAO,KAAK,QAAQ,CAAC;AACzB;AAEA,SAAS,QAAQ,GAAG;AAEpB,gBAAgB;AAChB,SAAS,OAAO;AAChB,SAAS,OAAO;AAChB,SAAS,QAAQ;AACjB,SAAS,SAAS;AAElB,aAAa;AACb,SAAS,gBAAgB;AACzB,SAAS,SAAS;AAClB,SAAS,IAAI;AACb,SAAS,IAAI;AACb,SAAS,IAAI;AACb,SAAS,KAAK;AACd,SAAS,KAAK;AACd,SAAS,QAAQ;AACjB,SAAS,OAAO;AAChB,SAAS,MAAM;AAEf,UAAU;AACV,SAAS,OAAO;AAChB,SAAS,QAAQ;AAEjB,UAAU;AACV,SAAS,KAAK;AACd,SAAS,IAAI;AAEb,iDAAiD;AACjD,SAAS,gBAAgB,CAAC,UAAU,CAAC,SAAS,IAAI;AAClD,SAAS,SAAS,CAAC,UAAU,CAAC,SAAS,IAAI,EAAE,SAAS,OAAO,EAAE,SAAS,IAAI;AAC5E,SAAS,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI;AACtC,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5090, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/tokenize.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = tokenize;\n\nvar delimRe        = /[\\s{}=;:[\\],'\"()<>]/g,\n    stringDoubleRe = /(?:\"([^\"\\\\]*(?:\\\\.[^\"\\\\]*)*)\")/g,\n    stringSingleRe = /(?:'([^'\\\\]*(?:\\\\.[^'\\\\]*)*)')/g;\n\nvar setCommentRe = /^ *[*/]+ */,\n    setCommentAltRe = /^\\s*\\*?\\/*/,\n    setCommentSplitRe = /\\n/g,\n    whitespaceRe = /\\s/,\n    unescapeRe = /\\\\(.?)/g;\n\nvar unescapeMap = {\n    \"0\": \"\\0\",\n    \"r\": \"\\r\",\n    \"n\": \"\\n\",\n    \"t\": \"\\t\"\n};\n\n/**\n * Unescapes a string.\n * @param {string} str String to unescape\n * @returns {string} Unescaped string\n * @property {Object.<string,string>} map Special characters map\n * @memberof tokenize\n */\nfunction unescape(str) {\n    return str.replace(unescapeRe, function($0, $1) {\n        switch ($1) {\n            case \"\\\\\":\n            case \"\":\n                return $1;\n            default:\n                return unescapeMap[$1] || \"\";\n        }\n    });\n}\n\ntokenize.unescape = unescape;\n\n/**\n * Gets the next token and advances.\n * @typedef TokenizerHandleNext\n * @type {function}\n * @returns {string|null} Next token or `null` on eof\n */\n\n/**\n * Peeks for the next token.\n * @typedef TokenizerHandlePeek\n * @type {function}\n * @returns {string|null} Next token or `null` on eof\n */\n\n/**\n * Pushes a token back to the stack.\n * @typedef TokenizerHandlePush\n * @type {function}\n * @param {string} token Token\n * @returns {undefined}\n */\n\n/**\n * Skips the next token.\n * @typedef TokenizerHandleSkip\n * @type {function}\n * @param {string} expected Expected token\n * @param {boolean} [optional=false] If optional\n * @returns {boolean} Whether the token matched\n * @throws {Error} If the token didn't match and is not optional\n */\n\n/**\n * Gets the comment on the previous line or, alternatively, the line comment on the specified line.\n * @typedef TokenizerHandleCmnt\n * @type {function}\n * @param {number} [line] Line number\n * @returns {string|null} Comment text or `null` if none\n */\n\n/**\n * Handle object returned from {@link tokenize}.\n * @interface ITokenizerHandle\n * @property {TokenizerHandleNext} next Gets the next token and advances (`null` on eof)\n * @property {TokenizerHandlePeek} peek Peeks for the next token (`null` on eof)\n * @property {TokenizerHandlePush} push Pushes a token back to the stack\n * @property {TokenizerHandleSkip} skip Skips a token, returns its presence and advances or, if non-optional and not present, throws\n * @property {TokenizerHandleCmnt} cmnt Gets the comment on the previous line or the line comment on the specified line, if any\n * @property {number} line Current line number\n */\n\n/**\n * Tokenizes the given .proto source and returns an object with useful utility functions.\n * @param {string} source Source contents\n * @param {boolean} alternateCommentMode Whether we should activate alternate comment parsing mode.\n * @returns {ITokenizerHandle} Tokenizer handle\n */\nfunction tokenize(source, alternateCommentMode) {\n    /* eslint-disable callback-return */\n    source = source.toString();\n\n    var offset = 0,\n        length = source.length,\n        line = 1,\n        lastCommentLine = 0,\n        comments = {};\n\n    var stack = [];\n\n    var stringDelim = null;\n\n    /* istanbul ignore next */\n    /**\n     * Creates an error for illegal syntax.\n     * @param {string} subject Subject\n     * @returns {Error} Error created\n     * @inner\n     */\n    function illegal(subject) {\n        return Error(\"illegal \" + subject + \" (line \" + line + \")\");\n    }\n\n    /**\n     * Reads a string till its end.\n     * @returns {string} String read\n     * @inner\n     */\n    function readString() {\n        var re = stringDelim === \"'\" ? stringSingleRe : stringDoubleRe;\n        re.lastIndex = offset - 1;\n        var match = re.exec(source);\n        if (!match)\n            throw illegal(\"string\");\n        offset = re.lastIndex;\n        push(stringDelim);\n        stringDelim = null;\n        return unescape(match[1]);\n    }\n\n    /**\n     * Gets the character at `pos` within the source.\n     * @param {number} pos Position\n     * @returns {string} Character\n     * @inner\n     */\n    function charAt(pos) {\n        return source.charAt(pos);\n    }\n\n    /**\n     * Sets the current comment text.\n     * @param {number} start Start offset\n     * @param {number} end End offset\n     * @param {boolean} isLeading set if a leading comment\n     * @returns {undefined}\n     * @inner\n     */\n    function setComment(start, end, isLeading) {\n        var comment = {\n            type: source.charAt(start++),\n            lineEmpty: false,\n            leading: isLeading,\n        };\n        var lookback;\n        if (alternateCommentMode) {\n            lookback = 2;  // alternate comment parsing: \"//\" or \"/*\"\n        } else {\n            lookback = 3;  // \"///\" or \"/**\"\n        }\n        var commentOffset = start - lookback,\n            c;\n        do {\n            if (--commentOffset < 0 ||\n                    (c = source.charAt(commentOffset)) === \"\\n\") {\n                comment.lineEmpty = true;\n                break;\n            }\n        } while (c === \" \" || c === \"\\t\");\n        var lines = source\n            .substring(start, end)\n            .split(setCommentSplitRe);\n        for (var i = 0; i < lines.length; ++i)\n            lines[i] = lines[i]\n                .replace(alternateCommentMode ? setCommentAltRe : setCommentRe, \"\")\n                .trim();\n        comment.text = lines\n            .join(\"\\n\")\n            .trim();\n\n        comments[line] = comment;\n        lastCommentLine = line;\n    }\n\n    function isDoubleSlashCommentLine(startOffset) {\n        var endOffset = findEndOfLine(startOffset);\n\n        // see if remaining line matches comment pattern\n        var lineText = source.substring(startOffset, endOffset);\n        var isComment = /^\\s*\\/\\//.test(lineText);\n        return isComment;\n    }\n\n    function findEndOfLine(cursor) {\n        // find end of cursor's line\n        var endOffset = cursor;\n        while (endOffset < length && charAt(endOffset) !== \"\\n\") {\n            endOffset++;\n        }\n        return endOffset;\n    }\n\n    /**\n     * Obtains the next token.\n     * @returns {string|null} Next token or `null` on eof\n     * @inner\n     */\n    function next() {\n        if (stack.length > 0)\n            return stack.shift();\n        if (stringDelim)\n            return readString();\n        var repeat,\n            prev,\n            curr,\n            start,\n            isDoc,\n            isLeadingComment = offset === 0;\n        do {\n            if (offset === length)\n                return null;\n            repeat = false;\n            while (whitespaceRe.test(curr = charAt(offset))) {\n                if (curr === \"\\n\") {\n                    isLeadingComment = true;\n                    ++line;\n                }\n                if (++offset === length)\n                    return null;\n            }\n\n            if (charAt(offset) === \"/\") {\n                if (++offset === length) {\n                    throw illegal(\"comment\");\n                }\n                if (charAt(offset) === \"/\") { // Line\n                    if (!alternateCommentMode) {\n                        // check for triple-slash comment\n                        isDoc = charAt(start = offset + 1) === \"/\";\n\n                        while (charAt(++offset) !== \"\\n\") {\n                            if (offset === length) {\n                                return null;\n                            }\n                        }\n                        ++offset;\n                        if (isDoc) {\n                            setComment(start, offset - 1, isLeadingComment);\n                            // Trailing comment cannot not be multi-line,\n                            // so leading comment state should be reset to handle potential next comments\n                            isLeadingComment = true;\n                        }\n                        ++line;\n                        repeat = true;\n                    } else {\n                        // check for double-slash comments, consolidating consecutive lines\n                        start = offset;\n                        isDoc = false;\n                        if (isDoubleSlashCommentLine(offset - 1)) {\n                            isDoc = true;\n                            do {\n                                offset = findEndOfLine(offset);\n                                if (offset === length) {\n                                    break;\n                                }\n                                offset++;\n                                if (!isLeadingComment) {\n                                    // Trailing comment cannot not be multi-line\n                                    break;\n                                }\n                            } while (isDoubleSlashCommentLine(offset));\n                        } else {\n                            offset = Math.min(length, findEndOfLine(offset) + 1);\n                        }\n                        if (isDoc) {\n                            setComment(start, offset, isLeadingComment);\n                            isLeadingComment = true;\n                        }\n                        line++;\n                        repeat = true;\n                    }\n                } else if ((curr = charAt(offset)) === \"*\") { /* Block */\n                    // check for /** (regular comment mode) or /* (alternate comment mode)\n                    start = offset + 1;\n                    isDoc = alternateCommentMode || charAt(start) === \"*\";\n                    do {\n                        if (curr === \"\\n\") {\n                            ++line;\n                        }\n                        if (++offset === length) {\n                            throw illegal(\"comment\");\n                        }\n                        prev = curr;\n                        curr = charAt(offset);\n                    } while (prev !== \"*\" || curr !== \"/\");\n                    ++offset;\n                    if (isDoc) {\n                        setComment(start, offset - 2, isLeadingComment);\n                        isLeadingComment = true;\n                    }\n                    repeat = true;\n                } else {\n                    return \"/\";\n                }\n            }\n        } while (repeat);\n\n        // offset !== length if we got here\n\n        var end = offset;\n        delimRe.lastIndex = 0;\n        var delim = delimRe.test(charAt(end++));\n        if (!delim)\n            while (end < length && !delimRe.test(charAt(end)))\n                ++end;\n        var token = source.substring(offset, offset = end);\n        if (token === \"\\\"\" || token === \"'\")\n            stringDelim = token;\n        return token;\n    }\n\n    /**\n     * Pushes a token back to the stack.\n     * @param {string} token Token\n     * @returns {undefined}\n     * @inner\n     */\n    function push(token) {\n        stack.push(token);\n    }\n\n    /**\n     * Peeks for the next token.\n     * @returns {string|null} Token or `null` on eof\n     * @inner\n     */\n    function peek() {\n        if (!stack.length) {\n            var token = next();\n            if (token === null)\n                return null;\n            push(token);\n        }\n        return stack[0];\n    }\n\n    /**\n     * Skips a token.\n     * @param {string} expected Expected token\n     * @param {boolean} [optional=false] Whether the token is optional\n     * @returns {boolean} `true` when skipped, `false` if not\n     * @throws {Error} When a required token is not present\n     * @inner\n     */\n    function skip(expected, optional) {\n        var actual = peek(),\n            equals = actual === expected;\n        if (equals) {\n            next();\n            return true;\n        }\n        if (!optional)\n            throw illegal(\"token '\" + actual + \"', '\" + expected + \"' expected\");\n        return false;\n    }\n\n    /**\n     * Gets a comment.\n     * @param {number} [trailingLine] Line number if looking for a trailing comment\n     * @returns {string|null} Comment text\n     * @inner\n     */\n    function cmnt(trailingLine) {\n        var ret = null;\n        var comment;\n        if (trailingLine === undefined) {\n            comment = comments[line - 1];\n            delete comments[line - 1];\n            if (comment && (alternateCommentMode || comment.type === \"*\" || comment.lineEmpty)) {\n                ret = comment.leading ? comment.text : null;\n            }\n        } else {\n            /* istanbul ignore else */\n            if (lastCommentLine < trailingLine) {\n                peek();\n            }\n            comment = comments[trailingLine];\n            delete comments[trailingLine];\n            if (comment && !comment.lineEmpty && (alternateCommentMode || comment.type === \"/\")) {\n                ret = comment.leading ? null : comment.text;\n            }\n        }\n        return ret;\n    }\n\n    return Object.defineProperty({\n        next: next,\n        peek: peek,\n        push: push,\n        skip: skip,\n        cmnt: cmnt\n    }, \"line\", {\n        get: function() { return line; }\n    });\n    /* eslint-enable callback-return */\n}\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,IAAI,UAAiB,wBACjB,iBAAiB,mCACjB,iBAAiB;AAErB,IAAI,eAAe,cACf,kBAAkB,cAClB,oBAAoB,OACpB,eAAe,MACf,aAAa;AAEjB,IAAI,cAAc;IACd,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACT;AAEA;;;;;;CAMC,GACD,SAAS,SAAS,GAAG;IACjB,OAAO,IAAI,OAAO,CAAC,YAAY,SAAS,EAAE,EAAE,EAAE;QAC1C,OAAQ;YACJ,KAAK;YACL,KAAK;gBACD,OAAO;YACX;gBACI,OAAO,WAAW,CAAC,GAAG,IAAI;QAClC;IACJ;AACJ;AAEA,SAAS,QAAQ,GAAG;AAEpB;;;;;CAKC,GAED;;;;;CAKC,GAED;;;;;;CAMC,GAED;;;;;;;;CAQC,GAED;;;;;;CAMC,GAED;;;;;;;;;CASC,GAED;;;;;CAKC,GACD,SAAS,SAAS,MAAM,EAAE,oBAAoB;IAC1C,kCAAkC,GAClC,SAAS,OAAO,QAAQ;IAExB,IAAI,SAAS,GACT,SAAS,OAAO,MAAM,EACtB,OAAO,GACP,kBAAkB,GAClB,WAAW,CAAC;IAEhB,IAAI,QAAQ,EAAE;IAEd,IAAI,cAAc;IAElB,wBAAwB,GACxB;;;;;KAKC,GACD,SAAS,QAAQ,OAAO;QACpB,OAAO,MAAM,aAAa,UAAU,YAAY,OAAO;IAC3D;IAEA;;;;KAIC,GACD,SAAS;QACL,IAAI,KAAK,gBAAgB,MAAM,iBAAiB;QAChD,GAAG,SAAS,GAAG,SAAS;QACxB,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,OACD,MAAM,QAAQ;QAClB,SAAS,GAAG,SAAS;QACrB,KAAK;QACL,cAAc;QACd,OAAO,SAAS,KAAK,CAAC,EAAE;IAC5B;IAEA;;;;;KAKC,GACD,SAAS,OAAO,GAAG;QACf,OAAO,OAAO,MAAM,CAAC;IACzB;IAEA;;;;;;;KAOC,GACD,SAAS,WAAW,KAAK,EAAE,GAAG,EAAE,SAAS;QACrC,IAAI,UAAU;YACV,MAAM,OAAO,MAAM,CAAC;YACpB,WAAW;YACX,SAAS;QACb;QACA,IAAI;QACJ,IAAI,sBAAsB;YACtB,WAAW,GAAI,0CAA0C;QAC7D,OAAO;YACH,WAAW,GAAI,iBAAiB;QACpC;QACA,IAAI,gBAAgB,QAAQ,UACxB;QACJ,GAAG;YACC,IAAI,EAAE,gBAAgB,KACd,CAAC,IAAI,OAAO,MAAM,CAAC,cAAc,MAAM,MAAM;gBACjD,QAAQ,SAAS,GAAG;gBACpB;YACJ;QACJ,QAAS,MAAM,OAAO,MAAM,KAAM;QAClC,IAAI,QAAQ,OACP,SAAS,CAAC,OAAO,KACjB,KAAK,CAAC;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAChC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CACd,OAAO,CAAC,uBAAuB,kBAAkB,cAAc,IAC/D,IAAI;QACb,QAAQ,IAAI,GAAG,MACV,IAAI,CAAC,MACL,IAAI;QAET,QAAQ,CAAC,KAAK,GAAG;QACjB,kBAAkB;IACtB;IAEA,SAAS,yBAAyB,WAAW;QACzC,IAAI,YAAY,cAAc;QAE9B,gDAAgD;QAChD,IAAI,WAAW,OAAO,SAAS,CAAC,aAAa;QAC7C,IAAI,YAAY,WAAW,IAAI,CAAC;QAChC,OAAO;IACX;IAEA,SAAS,cAAc,MAAM;QACzB,4BAA4B;QAC5B,IAAI,YAAY;QAChB,MAAO,YAAY,UAAU,OAAO,eAAe,KAAM;YACrD;QACJ;QACA,OAAO;IACX;IAEA;;;;KAIC,GACD,SAAS;QACL,IAAI,MAAM,MAAM,GAAG,GACf,OAAO,MAAM,KAAK;QACtB,IAAI,aACA,OAAO;QACX,IAAI,QACA,MACA,MACA,OACA,OACA,mBAAmB,WAAW;QAClC,GAAG;YACC,IAAI,WAAW,QACX,OAAO;YACX,SAAS;YACT,MAAO,aAAa,IAAI,CAAC,OAAO,OAAO,SAAU;gBAC7C,IAAI,SAAS,MAAM;oBACf,mBAAmB;oBACnB,EAAE;gBACN;gBACA,IAAI,EAAE,WAAW,QACb,OAAO;YACf;YAEA,IAAI,OAAO,YAAY,KAAK;gBACxB,IAAI,EAAE,WAAW,QAAQ;oBACrB,MAAM,QAAQ;gBAClB;gBACA,IAAI,OAAO,YAAY,KAAK;oBACxB,IAAI,CAAC,sBAAsB;wBACvB,iCAAiC;wBACjC,QAAQ,OAAO,QAAQ,SAAS,OAAO;wBAEvC,MAAO,OAAO,EAAE,YAAY,KAAM;4BAC9B,IAAI,WAAW,QAAQ;gCACnB,OAAO;4BACX;wBACJ;wBACA,EAAE;wBACF,IAAI,OAAO;4BACP,WAAW,OAAO,SAAS,GAAG;4BAC9B,6CAA6C;4BAC7C,6EAA6E;4BAC7E,mBAAmB;wBACvB;wBACA,EAAE;wBACF,SAAS;oBACb,OAAO;wBACH,mEAAmE;wBACnE,QAAQ;wBACR,QAAQ;wBACR,IAAI,yBAAyB,SAAS,IAAI;4BACtC,QAAQ;4BACR,GAAG;gCACC,SAAS,cAAc;gCACvB,IAAI,WAAW,QAAQ;oCACnB;gCACJ;gCACA;gCACA,IAAI,CAAC,kBAAkB;oCAEnB;gCACJ;4BACJ,QAAS,yBAAyB,QAAS;wBAC/C,OAAO;4BACH,SAAS,KAAK,GAAG,CAAC,QAAQ,cAAc,UAAU;wBACtD;wBACA,IAAI,OAAO;4BACP,WAAW,OAAO,QAAQ;4BAC1B,mBAAmB;wBACvB;wBACA;wBACA,SAAS;oBACb;gBACJ,OAAO,IAAI,CAAC,OAAO,OAAO,OAAO,MAAM,KAAK;oBACxC,sEAAsE;oBACtE,QAAQ,SAAS;oBACjB,QAAQ,wBAAwB,OAAO,WAAW;oBAClD,GAAG;wBACC,IAAI,SAAS,MAAM;4BACf,EAAE;wBACN;wBACA,IAAI,EAAE,WAAW,QAAQ;4BACrB,MAAM,QAAQ;wBAClB;wBACA,OAAO;wBACP,OAAO,OAAO;oBAClB,QAAS,SAAS,OAAO,SAAS,IAAK;oBACvC,EAAE;oBACF,IAAI,OAAO;wBACP,WAAW,OAAO,SAAS,GAAG;wBAC9B,mBAAmB;oBACvB;oBACA,SAAS;gBACb,OAAO;oBACH,OAAO;gBACX;YACJ;QACJ,QAAS,OAAQ;QAEjB,mCAAmC;QAEnC,IAAI,MAAM;QACV,QAAQ,SAAS,GAAG;QACpB,IAAI,QAAQ,QAAQ,IAAI,CAAC,OAAO;QAChC,IAAI,CAAC,OACD,MAAO,MAAM,UAAU,CAAC,QAAQ,IAAI,CAAC,OAAO,MACxC,EAAE;QACV,IAAI,QAAQ,OAAO,SAAS,CAAC,QAAQ,SAAS;QAC9C,IAAI,UAAU,QAAQ,UAAU,KAC5B,cAAc;QAClB,OAAO;IACX;IAEA;;;;;KAKC,GACD,SAAS,KAAK,KAAK;QACf,MAAM,IAAI,CAAC;IACf;IAEA;;;;KAIC,GACD,SAAS;QACL,IAAI,CAAC,MAAM,MAAM,EAAE;YACf,IAAI,QAAQ;YACZ,IAAI,UAAU,MACV,OAAO;YACX,KAAK;QACT;QACA,OAAO,KAAK,CAAC,EAAE;IACnB;IAEA;;;;;;;KAOC,GACD,SAAS,KAAK,QAAQ,EAAE,QAAQ;QAC5B,IAAI,SAAS,QACT,SAAS,WAAW;QACxB,IAAI,QAAQ;YACR;YACA,OAAO;QACX;QACA,IAAI,CAAC,UACD,MAAM,QAAQ,YAAY,SAAS,SAAS,WAAW;QAC3D,OAAO;IACX;IAEA;;;;;KAKC,GACD,SAAS,KAAK,YAAY;QACtB,IAAI,MAAM;QACV,IAAI;QACJ,IAAI,iBAAiB,WAAW;YAC5B,UAAU,QAAQ,CAAC,OAAO,EAAE;YAC5B,OAAO,QAAQ,CAAC,OAAO,EAAE;YACzB,IAAI,WAAW,CAAC,wBAAwB,QAAQ,IAAI,KAAK,OAAO,QAAQ,SAAS,GAAG;gBAChF,MAAM,QAAQ,OAAO,GAAG,QAAQ,IAAI,GAAG;YAC3C;QACJ,OAAO;YACH,wBAAwB,GACxB,IAAI,kBAAkB,cAAc;gBAChC;YACJ;YACA,UAAU,QAAQ,CAAC,aAAa;YAChC,OAAO,QAAQ,CAAC,aAAa;YAC7B,IAAI,WAAW,CAAC,QAAQ,SAAS,IAAI,CAAC,wBAAwB,QAAQ,IAAI,KAAK,GAAG,GAAG;gBACjF,MAAM,QAAQ,OAAO,GAAG,OAAO,QAAQ,IAAI;YAC/C;QACJ;QACA,OAAO;IACX;IAEA,OAAO,OAAO,cAAc,CAAC;QACzB,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;IACV,GAAG,QAAQ;QACP,KAAK;YAAa,OAAO;QAAM;IACnC;AACA,iCAAiC,GACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5423, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/parse.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = parse;\n\nparse.filename = null;\nparse.defaults = { keepCase: false };\n\nvar tokenize  = require(\"./tokenize\"),\n    Root      = require(\"./root\"),\n    Type      = require(\"./type\"),\n    Field     = require(\"./field\"),\n    MapField  = require(\"./mapfield\"),\n    OneOf     = require(\"./oneof\"),\n    Enum      = require(\"./enum\"),\n    Service   = require(\"./service\"),\n    Method    = require(\"./method\"),\n    ReflectionObject = require(\"./object\"),\n    types     = require(\"./types\"),\n    util      = require(\"./util\");\n\nvar base10Re    = /^[1-9][0-9]*$/,\n    base10NegRe = /^-?[1-9][0-9]*$/,\n    base16Re    = /^0[x][0-9a-fA-F]+$/,\n    base16NegRe = /^-?0[x][0-9a-fA-F]+$/,\n    base8Re     = /^0[0-7]+$/,\n    base8NegRe  = /^-?0[0-7]+$/,\n    numberRe    = /^(?![eE])[0-9]*(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,\n    nameRe      = /^[a-zA-Z_][a-zA-Z_0-9]*$/,\n    typeRefRe   = /^(?:\\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\\.[a-zA-Z_][a-zA-Z_0-9]*)*$/;\n\n/**\n * Result object returned from {@link parse}.\n * @interface IParserResult\n * @property {string|undefined} package Package name, if declared\n * @property {string[]|undefined} imports Imports, if any\n * @property {string[]|undefined} weakImports Weak imports, if any\n * @property {Root} root Populated root instance\n */\n\n/**\n * Options modifying the behavior of {@link parse}.\n * @interface IParseOptions\n * @property {boolean} [keepCase=false] Keeps field casing instead of converting to camel case\n * @property {boolean} [alternateCommentMode=false] Recognize double-slash comments in addition to doc-block comments.\n * @property {boolean} [preferTrailingComment=false] Use trailing comment when both leading comment and trailing comment exist.\n */\n\n/**\n * Options modifying the behavior of JSON serialization.\n * @interface IToJSONOptions\n * @property {boolean} [keepComments=false] Serializes comments.\n */\n\n/**\n * Parses the given .proto source and returns an object with the parsed contents.\n * @param {string} source Source contents\n * @param {Root} root Root to populate\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {IParserResult} Parser result\n * @property {string} filename=null Currently processing file name for error reporting, if known\n * @property {IParseOptions} defaults Default {@link IParseOptions}\n */\nfunction parse(source, root, options) {\n    /* eslint-disable callback-return */\n    if (!(root instanceof Root)) {\n        options = root;\n        root = new Root();\n    }\n    if (!options)\n        options = parse.defaults;\n\n    var preferTrailingComment = options.preferTrailingComment || false;\n    var tn = tokenize(source, options.alternateCommentMode || false),\n        next = tn.next,\n        push = tn.push,\n        peek = tn.peek,\n        skip = tn.skip,\n        cmnt = tn.cmnt;\n\n    var head = true,\n        pkg,\n        imports,\n        weakImports,\n        edition = \"proto2\";\n\n    var ptr = root;\n\n    var topLevelObjects = [];\n    var topLevelOptions = {};\n\n    var applyCase = options.keepCase ? function(name) { return name; } : util.camelCase;\n\n    function resolveFileFeatures() {\n        topLevelObjects.forEach(obj => {\n            obj._edition = edition;\n            Object.keys(topLevelOptions).forEach(opt => {\n                if (obj.getOption(opt) !== undefined) return;\n                obj.setOption(opt, topLevelOptions[opt], true);\n            });\n        });\n    }\n\n    /* istanbul ignore next */\n    function illegal(token, name, insideTryCatch) {\n        var filename = parse.filename;\n        if (!insideTryCatch)\n            parse.filename = null;\n        return Error(\"illegal \" + (name || \"token\") + \" '\" + token + \"' (\" + (filename ? filename + \", \" : \"\") + \"line \" + tn.line + \")\");\n    }\n\n    function readString() {\n        var values = [],\n            token;\n        do {\n            /* istanbul ignore if */\n            if ((token = next()) !== \"\\\"\" && token !== \"'\")\n                throw illegal(token);\n\n            values.push(next());\n            skip(token);\n            token = peek();\n        } while (token === \"\\\"\" || token === \"'\");\n        return values.join(\"\");\n    }\n\n    function readValue(acceptTypeRef) {\n        var token = next();\n        switch (token) {\n            case \"'\":\n            case \"\\\"\":\n                push(token);\n                return readString();\n            case \"true\": case \"TRUE\":\n                return true;\n            case \"false\": case \"FALSE\":\n                return false;\n        }\n        try {\n            return parseNumber(token, /* insideTryCatch */ true);\n        } catch (e) {\n            /* istanbul ignore else */\n            if (acceptTypeRef && typeRefRe.test(token))\n                return token;\n\n            /* istanbul ignore next */\n            throw illegal(token, \"value\");\n        }\n    }\n\n    function readRanges(target, acceptStrings) {\n        var token, start;\n        do {\n            if (acceptStrings && ((token = peek()) === \"\\\"\" || token === \"'\")) {\n                var str = readString();\n                target.push(str);\n                if (edition >= 2023) {\n                    throw illegal(str, \"id\");\n                }\n            } else {\n                try {\n                    target.push([ start = parseId(next()), skip(\"to\", true) ? parseId(next()) : start ]);\n                } catch (err) {\n                    if (acceptStrings && typeRefRe.test(token) && edition >= 2023) {\n                        target.push(token);\n                    } else {\n                        throw err;\n                    }\n                }\n            }\n        } while (skip(\",\", true));\n        var dummy = {options: undefined};\n        dummy.setOption = function(name, value) {\n          if (this.options === undefined) this.options = {};\n          this.options[name] = value;\n        };\n        ifBlock(\n            dummy,\n            function parseRange_block(token) {\n              /* istanbul ignore else */\n              if (token === \"option\") {\n                parseOption(dummy, token);  // skip\n                skip(\";\");\n              } else\n                throw illegal(token);\n            },\n            function parseRange_line() {\n              parseInlineOptions(dummy);  // skip\n            });\n    }\n\n    function parseNumber(token, insideTryCatch) {\n        var sign = 1;\n        if (token.charAt(0) === \"-\") {\n            sign = -1;\n            token = token.substring(1);\n        }\n        switch (token) {\n            case \"inf\": case \"INF\": case \"Inf\":\n                return sign * Infinity;\n            case \"nan\": case \"NAN\": case \"Nan\": case \"NaN\":\n                return NaN;\n            case \"0\":\n                return 0;\n        }\n        if (base10Re.test(token))\n            return sign * parseInt(token, 10);\n        if (base16Re.test(token))\n            return sign * parseInt(token, 16);\n        if (base8Re.test(token))\n            return sign * parseInt(token, 8);\n\n        /* istanbul ignore else */\n        if (numberRe.test(token))\n            return sign * parseFloat(token);\n\n        /* istanbul ignore next */\n        throw illegal(token, \"number\", insideTryCatch);\n    }\n\n    function parseId(token, acceptNegative) {\n        switch (token) {\n            case \"max\": case \"MAX\": case \"Max\":\n                return 536870911;\n            case \"0\":\n                return 0;\n        }\n\n        /* istanbul ignore if */\n        if (!acceptNegative && token.charAt(0) === \"-\")\n            throw illegal(token, \"id\");\n\n        if (base10NegRe.test(token))\n            return parseInt(token, 10);\n        if (base16NegRe.test(token))\n            return parseInt(token, 16);\n\n        /* istanbul ignore else */\n        if (base8NegRe.test(token))\n            return parseInt(token, 8);\n\n        /* istanbul ignore next */\n        throw illegal(token, \"id\");\n    }\n\n    function parsePackage() {\n        /* istanbul ignore if */\n        if (pkg !== undefined)\n            throw illegal(\"package\");\n\n        pkg = next();\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(pkg))\n            throw illegal(pkg, \"name\");\n\n        ptr = ptr.define(pkg);\n\n        skip(\";\");\n    }\n\n    function parseImport() {\n        var token = peek();\n        var whichImports;\n        switch (token) {\n            case \"weak\":\n                whichImports = weakImports || (weakImports = []);\n                next();\n                break;\n            case \"public\":\n                next();\n                // eslint-disable-next-line no-fallthrough\n            default:\n                whichImports = imports || (imports = []);\n                break;\n        }\n        token = readString();\n        skip(\";\");\n        whichImports.push(token);\n    }\n\n    function parseSyntax() {\n        skip(\"=\");\n        edition = readString();\n\n        /* istanbul ignore if */\n        if (edition < 2023)\n            throw illegal(edition, \"syntax\");\n\n        skip(\";\");\n    }\n\n    function parseEdition() {\n        skip(\"=\");\n        edition = readString();\n        const supportedEditions = [\"2023\"];\n\n        /* istanbul ignore if */\n        if (!supportedEditions.includes(edition))\n            throw illegal(edition, \"edition\");\n\n        skip(\";\");\n    }\n\n\n    function parseCommon(parent, token) {\n        switch (token) {\n\n            case \"option\":\n                parseOption(parent, token);\n                skip(\";\");\n                return true;\n\n            case \"message\":\n                parseType(parent, token);\n                return true;\n\n            case \"enum\":\n                parseEnum(parent, token);\n                return true;\n\n            case \"service\":\n                parseService(parent, token);\n                return true;\n\n            case \"extend\":\n                parseExtension(parent, token);\n                return true;\n        }\n        return false;\n    }\n\n    function ifBlock(obj, fnIf, fnElse) {\n        var trailingLine = tn.line;\n        if (obj) {\n            if(typeof obj.comment !== \"string\") {\n              obj.comment = cmnt(); // try block-type comment\n            }\n            obj.filename = parse.filename;\n        }\n        if (skip(\"{\", true)) {\n            var token;\n            while ((token = next()) !== \"}\")\n                fnIf(token);\n            skip(\";\", true);\n        } else {\n            if (fnElse)\n                fnElse();\n            skip(\";\");\n            if (obj && (typeof obj.comment !== \"string\" || preferTrailingComment))\n                obj.comment = cmnt(trailingLine) || obj.comment; // try line-type comment\n        }\n    }\n\n    function parseType(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"type name\");\n\n        var type = new Type(token);\n        ifBlock(type, function parseType_block(token) {\n            if (parseCommon(type, token))\n                return;\n\n            switch (token) {\n\n                case \"map\":\n                    parseMapField(type, token);\n                    break;\n\n                case \"required\":\n                    if (edition !== \"proto2\")\n                        throw illegal(token);\n                /* eslint-disable no-fallthrough */\n                case \"repeated\":\n                    parseField(type, token);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (edition === \"proto3\") {\n                        parseField(type, \"proto3_optional\");\n                    } else if (edition !== \"proto2\") {\n                        throw illegal(token);\n                    } else {\n                        parseField(type, \"optional\");\n                    }\n                    break;\n\n                case \"oneof\":\n                    parseOneOf(type, token);\n                    break;\n\n                case \"extensions\":\n                    readRanges(type.extensions || (type.extensions = []));\n                    break;\n\n                case \"reserved\":\n                    readRanges(type.reserved || (type.reserved = []), true);\n                    break;\n\n                default:\n                    /* istanbul ignore if */\n                    if (edition === \"proto2\" || !typeRefRe.test(token)) {\n                        throw illegal(token);\n                    }\n\n                    push(token);\n                    parseField(type, \"optional\");\n                    break;\n            }\n        });\n        parent.add(type);\n        if (parent === ptr) {\n            topLevelObjects.push(type);\n        }\n    }\n\n    function parseField(parent, rule, extend) {\n        var type = next();\n        if (type === \"group\") {\n            parseGroup(parent, rule);\n            return;\n        }\n        // Type names can consume multiple tokens, in multiple variants:\n        //    package.subpackage   field       tokens: \"package.subpackage\" [TYPE NAME ENDS HERE] \"field\"\n        //    package . subpackage field       tokens: \"package\" \".\" \"subpackage\" [TYPE NAME ENDS HERE] \"field\"\n        //    package.  subpackage field       tokens: \"package.\" \"subpackage\" [TYPE NAME ENDS HERE] \"field\"\n        //    package  .subpackage field       tokens: \"package\" \".subpackage\" [TYPE NAME ENDS HERE] \"field\"\n        // Keep reading tokens until we get a type name with no period at the end,\n        // and the next token does not start with a period.\n        while (type.endsWith(\".\") || peek().startsWith(\".\")) {\n            type += next();\n        }\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(type))\n            throw illegal(type, \"type\");\n\n        var name = next();\n\n        /* istanbul ignore if */\n\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        name = applyCase(name);\n        skip(\"=\");\n\n        var field = new Field(name, parseId(next()), type, rule, extend);\n\n        ifBlock(field, function parseField_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(field, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseField_line() {\n            parseInlineOptions(field);\n        });\n\n        if (rule === \"proto3_optional\") {\n            // for proto3 optional fields, we create a single-member Oneof to mimic \"optional\" behavior\n            var oneof = new OneOf(\"_\" + name);\n            field.setOption(\"proto3_optional\", true);\n            oneof.add(field);\n            parent.add(oneof);\n        } else {\n            parent.add(field);\n        }\n        if (parent === ptr) {\n            topLevelObjects.push(field);\n        }\n    }\n\n    function parseGroup(parent, rule) {\n        if (edition >= 2023) {\n            throw illegal(\"group\");\n        }\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        var fieldName = util.lcFirst(name);\n        if (name === fieldName)\n            name = util.ucFirst(name);\n        skip(\"=\");\n        var id = parseId(next());\n        var type = new Type(name);\n        type.group = true;\n        var field = new Field(fieldName, id, name, rule);\n        field.filename = parse.filename;\n        ifBlock(type, function parseGroup_block(token) {\n            switch (token) {\n\n                case \"option\":\n                    parseOption(type, token);\n                    skip(\";\");\n                    break;\n                case \"required\":\n                case \"repeated\":\n                    parseField(type, token);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (edition === \"proto3\") {\n                        parseField(type, \"proto3_optional\");\n                    } else {\n                        parseField(type, \"optional\");\n                    }\n                    break;\n\n                case \"message\":\n                    parseType(type, token);\n                    break;\n\n                case \"enum\":\n                    parseEnum(type, token);\n                    break;\n\n                case \"reserved\":\n                    readRanges(type.reserved || (type.reserved = []), true);\n                    break;\n\n                /* istanbul ignore next */\n                default:\n                    throw illegal(token); // there are no groups with proto3 semantics\n            }\n        });\n        parent.add(type)\n              .add(field);\n    }\n\n    function parseMapField(parent) {\n        skip(\"<\");\n        var keyType = next();\n\n        /* istanbul ignore if */\n        if (types.mapKey[keyType] === undefined)\n            throw illegal(keyType, \"type\");\n\n        skip(\",\");\n        var valueType = next();\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(valueType))\n            throw illegal(valueType, \"type\");\n\n        skip(\">\");\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        skip(\"=\");\n        var field = new MapField(applyCase(name), parseId(next()), keyType, valueType);\n        ifBlock(field, function parseMapField_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(field, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseMapField_line() {\n            parseInlineOptions(field);\n        });\n        parent.add(field);\n    }\n\n    function parseOneOf(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var oneof = new OneOf(applyCase(token));\n        ifBlock(oneof, function parseOneOf_block(token) {\n            if (token === \"option\") {\n                parseOption(oneof, token);\n                skip(\";\");\n            } else {\n                push(token);\n                parseField(oneof, \"optional\");\n            }\n        });\n        parent.add(oneof);\n    }\n\n    function parseEnum(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var enm = new Enum(token);\n        ifBlock(enm, function parseEnum_block(token) {\n          switch(token) {\n            case \"option\":\n              parseOption(enm, token);\n              skip(\";\");\n              break;\n\n            case \"reserved\":\n              readRanges(enm.reserved || (enm.reserved = []), true);\n              if(enm.reserved === undefined) enm.reserved = [];\n              break;\n\n            default:\n              parseEnumValue(enm, token);\n          }\n        });\n        parent.add(enm);\n        if (parent === ptr) {\n            topLevelObjects.push(enm);\n        }\n    }\n\n    function parseEnumValue(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token))\n            throw illegal(token, \"name\");\n\n        skip(\"=\");\n        var value = parseId(next(), true),\n            dummy = {\n                options: undefined\n            };\n        dummy.getOption = function(name) {\n            return this.options[name];\n        };\n        dummy.setOption = function(name, value) {\n            ReflectionObject.prototype.setOption.call(dummy, name, value);\n        };\n        dummy.setParsedOption = function() {\n            return undefined;\n        };\n        ifBlock(dummy, function parseEnumValue_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(dummy, token); // skip\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseEnumValue_line() {\n            parseInlineOptions(dummy); // skip\n        });\n        parent.add(token, value, dummy.comment, dummy.parsedOptions || dummy.options);\n    }\n\n    function parseOption(parent, token) {\n            var option;\n            var propName;\n            var isOption = true;\n            if (token === \"option\") {\n                token = next();\n            }\n\n            while (token !== \"=\") {\n                if (token === \"(\") {\n                    var parensValue = next();\n                    skip(\")\");\n                    token = \"(\" + parensValue + \")\";\n                }\n                if (isOption) {\n                    isOption = false;\n                    if (token.includes(\".\") && !token.includes(\"(\")) {\n                        var tokens = token.split(\".\");\n                        option = tokens[0] + \".\";\n                        token = tokens[1];\n                        continue;\n                    }\n                    option = token;\n                } else {\n                    propName = propName ? propName += token : token;\n                }\n                token = next();\n            }\n            var name = propName ? option.concat(propName) : option;\n            var optionValue = parseOptionValue(parent, name);\n            propName = propName && propName[0] === \".\" ? propName.slice(1) : propName;\n            option = option && option[option.length - 1] === \".\" ? option.slice(0, -1) : option;\n            setParsedOption(parent, option, optionValue, propName);\n    }\n\n    function parseOptionValue(parent, name) {\n        // { a: \"foo\" b { c: \"bar\" } }\n        if (skip(\"{\", true)) {\n            var objectResult = {};\n\n            while (!skip(\"}\", true)) {\n                /* istanbul ignore if */\n                if (!nameRe.test(token = next())) {\n                    throw illegal(token, \"name\");\n                }\n                if (token === null) {\n                  throw illegal(token, \"end of input\");\n                }\n\n                var value;\n                var propName = token;\n\n                skip(\":\", true);\n\n                if (peek() === \"{\") {\n                    // option (my_option) = {\n                    //     repeated_value: [ \"foo\", \"bar\" ]\n                    // };\n                    value = parseOptionValue(parent, name + \".\" + token);\n                } else if (peek() === \"[\") {\n                    value = [];\n                    var lastValue;\n                    if (skip(\"[\", true)) {\n                        do {\n                            lastValue = readValue(true);\n                            value.push(lastValue);\n                        } while (skip(\",\", true));\n                        skip(\"]\");\n                        if (typeof lastValue !== \"undefined\") {\n                            setOption(parent, name + \".\" + token, lastValue);\n                        }\n                    }\n                } else {\n                    value = readValue(true);\n                    setOption(parent, name + \".\" + token, value);\n                }\n\n                var prevValue = objectResult[propName];\n\n                if (prevValue)\n                    value = [].concat(prevValue).concat(value);\n\n                objectResult[propName] = value;\n\n                // Semicolons and commas can be optional\n                skip(\",\", true);\n                skip(\";\", true);\n            }\n\n            return objectResult;\n        }\n\n        var simpleValue = readValue(true);\n        setOption(parent, name, simpleValue);\n        return simpleValue;\n        // Does not enforce a delimiter to be universal\n    }\n\n    function setOption(parent, name, value) {\n        if (ptr === parent && /^features\\./.test(name)) {\n            topLevelOptions[name] = value;\n            return;\n        }\n        if (parent.setOption)\n            parent.setOption(name, value);\n    }\n\n    function setParsedOption(parent, name, value, propName) {\n        if (parent.setParsedOption)\n            parent.setParsedOption(name, value, propName);\n    }\n\n    function parseInlineOptions(parent) {\n        if (skip(\"[\", true)) {\n            do {\n                parseOption(parent, \"option\");\n            } while (skip(\",\", true));\n            skip(\"]\");\n        }\n        return parent;\n    }\n\n    function parseService(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"service name\");\n\n        var service = new Service(token);\n        ifBlock(service, function parseService_block(token) {\n            if (parseCommon(service, token)) {\n                return;\n            }\n\n            /* istanbul ignore else */\n            if (token === \"rpc\")\n                parseMethod(service, token);\n            else\n                throw illegal(token);\n        });\n        parent.add(service);\n        if (parent === ptr) {\n            topLevelObjects.push(service);\n        }\n    }\n\n    function parseMethod(parent, token) {\n        // Get the comment of the preceding line now (if one exists) in case the\n        // method is defined across multiple lines.\n        var commentText = cmnt();\n\n        var type = token;\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var name = token,\n            requestType, requestStream,\n            responseType, responseStream;\n\n        skip(\"(\");\n        if (skip(\"stream\", true))\n            requestStream = true;\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token);\n\n        requestType = token;\n        skip(\")\"); skip(\"returns\"); skip(\"(\");\n        if (skip(\"stream\", true))\n            responseStream = true;\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token);\n\n        responseType = token;\n        skip(\")\");\n\n        var method = new Method(name, type, requestType, responseType, requestStream, responseStream);\n        method.comment = commentText;\n        ifBlock(method, function parseMethod_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(method, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        });\n        parent.add(method);\n    }\n\n    function parseExtension(parent, token) {\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token, \"reference\");\n\n        var reference = token;\n        ifBlock(null, function parseExtension_block(token) {\n            switch (token) {\n\n                case \"required\":\n                case \"repeated\":\n                    parseField(parent, token, reference);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (edition === \"proto3\") {\n                        parseField(parent, \"proto3_optional\", reference);\n                    } else {\n                        parseField(parent, \"optional\", reference);\n                    }\n                    break;\n\n                default:\n                    /* istanbul ignore if */\n                    if (edition === \"proto2\" || !typeRefRe.test(token))\n                        throw illegal(token);\n                    push(token);\n                    parseField(parent, \"optional\", reference);\n                    break;\n            }\n        });\n    }\n\n    var token;\n    while ((token = next()) !== null) {\n        switch (token) {\n\n            case \"package\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parsePackage();\n                break;\n\n            case \"import\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parseImport();\n                break;\n\n            case \"syntax\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parseSyntax();\n                break;\n\n            case \"edition\":\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n                parseEdition();\n                break;\n\n            case \"option\":\n                parseOption(ptr, token);\n                skip(\";\", true);\n                break;\n\n            default:\n\n                /* istanbul ignore else */\n                if (parseCommon(ptr, token)) {\n                    head = false;\n                    continue;\n                }\n\n                /* istanbul ignore next */\n                throw illegal(token);\n        }\n    }\n\n    resolveFileFeatures();\n\n    parse.filename = null;\n    return {\n        \"package\"     : pkg,\n        \"imports\"     : imports,\n         weakImports  : weakImports,\n         root         : root\n    };\n}\n\n/**\n * Parses the given .proto source and returns an object with the parsed contents.\n * @name parse\n * @function\n * @param {string} source Source contents\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {IParserResult} Parser result\n * @property {string} filename=null Currently processing file name for error reporting, if known\n * @property {IParseOptions} defaults Default {@link IParseOptions}\n * @variation 2\n */\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,MAAM,QAAQ,GAAG;AACjB,MAAM,QAAQ,GAAG;IAAE,UAAU;AAAM;AAEnC,IAAI,gHACA,wGACA,wGACA,0GACA,gHACA,0GACA,wGACA,8GACA,4GACA,sHACA,0GACA;AAEJ,IAAI,WAAc,iBACd,cAAc,mBACd,WAAc,sBACd,cAAc,wBACd,UAAc,aACd,aAAc,eACd,WAAc,qDACd,SAAc,4BACd,YAAc;AAElB;;;;;;;CAOC,GAED;;;;;;CAMC,GAED;;;;CAIC,GAED;;;;;;;;CAQC,GACD,SAAS,MAAM,MAAM,EAAE,IAAI,EAAE,OAAO;IAChC,kCAAkC,GAClC,IAAI,CAAC,CAAC,gBAAgB,IAAI,GAAG;QACzB,UAAU;QACV,OAAO,IAAI;IACf;IACA,IAAI,CAAC,SACD,UAAU,MAAM,QAAQ;IAE5B,IAAI,wBAAwB,QAAQ,qBAAqB,IAAI;IAC7D,IAAI,KAAK,SAAS,QAAQ,QAAQ,oBAAoB,IAAI,QACtD,OAAO,GAAG,IAAI,EACd,OAAO,GAAG,IAAI,EACd,OAAO,GAAG,IAAI,EACd,OAAO,GAAG,IAAI,EACd,OAAO,GAAG,IAAI;IAElB,IAAI,OAAO,MACP,KACA,SACA,aACA,UAAU;IAEd,IAAI,MAAM;IAEV,IAAI,kBAAkB,EAAE;IACxB,IAAI,kBAAkB,CAAC;IAEvB,IAAI,YAAY,QAAQ,QAAQ,GAAG,SAAS,IAAI;QAAI,OAAO;IAAM,IAAI,KAAK,SAAS;IAEnF,SAAS;QACL,gBAAgB,OAAO,CAAC,CAAA;YACpB,IAAI,QAAQ,GAAG;YACf,OAAO,IAAI,CAAC,iBAAiB,OAAO,CAAC,CAAA;gBACjC,IAAI,IAAI,SAAS,CAAC,SAAS,WAAW;gBACtC,IAAI,SAAS,CAAC,KAAK,eAAe,CAAC,IAAI,EAAE;YAC7C;QACJ;IACJ;IAEA,wBAAwB,GACxB,SAAS,QAAQ,KAAK,EAAE,IAAI,EAAE,cAAc;QACxC,IAAI,WAAW,MAAM,QAAQ;QAC7B,IAAI,CAAC,gBACD,MAAM,QAAQ,GAAG;QACrB,OAAO,MAAM,aAAa,CAAC,QAAQ,OAAO,IAAI,OAAO,QAAQ,QAAQ,CAAC,WAAW,WAAW,OAAO,EAAE,IAAI,UAAU,GAAG,IAAI,GAAG;IACjI;IAEA,SAAS;QACL,IAAI,SAAS,EAAE,EACX;QACJ,GAAG;YACC,sBAAsB,GACtB,IAAI,CAAC,QAAQ,MAAM,MAAM,QAAQ,UAAU,KACvC,MAAM,QAAQ;YAElB,OAAO,IAAI,CAAC;YACZ,KAAK;YACL,QAAQ;QACZ,QAAS,UAAU,QAAQ,UAAU,IAAK;QAC1C,OAAO,OAAO,IAAI,CAAC;IACvB;IAEA,SAAS,UAAU,aAAa;QAC5B,IAAI,QAAQ;QACZ,OAAQ;YACJ,KAAK;YACL,KAAK;gBACD,KAAK;gBACL,OAAO;YACX,KAAK;YAAQ,KAAK;gBACd,OAAO;YACX,KAAK;YAAS,KAAK;gBACf,OAAO;QACf;QACA,IAAI;YACA,OAAO,YAAY,OAAO,kBAAkB,GAAG;QACnD,EAAE,OAAO,GAAG;YACR,wBAAwB,GACxB,IAAI,iBAAiB,UAAU,IAAI,CAAC,QAChC,OAAO;YAEX,wBAAwB,GACxB,MAAM,QAAQ,OAAO;QACzB;IACJ;IAEA,SAAS,WAAW,MAAM,EAAE,aAAa;QACrC,IAAI,OAAO;QACX,GAAG;YACC,IAAI,iBAAiB,CAAC,CAAC,QAAQ,MAAM,MAAM,QAAQ,UAAU,GAAG,GAAG;gBAC/D,IAAI,MAAM;gBACV,OAAO,IAAI,CAAC;gBACZ,IAAI,WAAW,MAAM;oBACjB,MAAM,QAAQ,KAAK;gBACvB;YACJ,OAAO;gBACH,IAAI;oBACA,OAAO,IAAI,CAAC;wBAAE,QAAQ,QAAQ;wBAAS,KAAK,MAAM,QAAQ,QAAQ,UAAU;qBAAO;gBACvF,EAAE,OAAO,KAAK;oBACV,IAAI,iBAAiB,UAAU,IAAI,CAAC,UAAU,WAAW,MAAM;wBAC3D,OAAO,IAAI,CAAC;oBAChB,OAAO;wBACH,MAAM;oBACV;gBACJ;YACJ;QACJ,QAAS,KAAK,KAAK,MAAO;QAC1B,IAAI,QAAQ;YAAC,SAAS;QAAS;QAC/B,MAAM,SAAS,GAAG,SAAS,IAAI,EAAE,KAAK;YACpC,IAAI,IAAI,CAAC,OAAO,KAAK,WAAW,IAAI,CAAC,OAAO,GAAG,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG;QACvB;QACA,QACI,OACA,SAAS,iBAAiB,KAAK;YAC7B,wBAAwB,GACxB,IAAI,UAAU,UAAU;gBACtB,YAAY,OAAO,QAAS,OAAO;gBACnC,KAAK;YACP,OACE,MAAM,QAAQ;QAClB,GACA,SAAS;YACP,mBAAmB,QAAS,OAAO;QACrC;IACR;IAEA,SAAS,YAAY,KAAK,EAAE,cAAc;QACtC,IAAI,OAAO;QACX,IAAI,MAAM,MAAM,CAAC,OAAO,KAAK;YACzB,OAAO,CAAC;YACR,QAAQ,MAAM,SAAS,CAAC;QAC5B;QACA,OAAQ;YACJ,KAAK;YAAO,KAAK;YAAO,KAAK;gBACzB,OAAO,OAAO;YAClB,KAAK;YAAO,KAAK;YAAO,KAAK;YAAO,KAAK;gBACrC,OAAO;YACX,KAAK;gBACD,OAAO;QACf;QACA,IAAI,SAAS,IAAI,CAAC,QACd,OAAO,OAAO,SAAS,OAAO;QAClC,IAAI,SAAS,IAAI,CAAC,QACd,OAAO,OAAO,SAAS,OAAO;QAClC,IAAI,QAAQ,IAAI,CAAC,QACb,OAAO,OAAO,SAAS,OAAO;QAElC,wBAAwB,GACxB,IAAI,SAAS,IAAI,CAAC,QACd,OAAO,OAAO,WAAW;QAE7B,wBAAwB,GACxB,MAAM,QAAQ,OAAO,UAAU;IACnC;IAEA,SAAS,QAAQ,KAAK,EAAE,cAAc;QAClC,OAAQ;YACJ,KAAK;YAAO,KAAK;YAAO,KAAK;gBACzB,OAAO;YACX,KAAK;gBACD,OAAO;QACf;QAEA,sBAAsB,GACtB,IAAI,CAAC,kBAAkB,MAAM,MAAM,CAAC,OAAO,KACvC,MAAM,QAAQ,OAAO;QAEzB,IAAI,YAAY,IAAI,CAAC,QACjB,OAAO,SAAS,OAAO;QAC3B,IAAI,YAAY,IAAI,CAAC,QACjB,OAAO,SAAS,OAAO;QAE3B,wBAAwB,GACxB,IAAI,WAAW,IAAI,CAAC,QAChB,OAAO,SAAS,OAAO;QAE3B,wBAAwB,GACxB,MAAM,QAAQ,OAAO;IACzB;IAEA,SAAS;QACL,sBAAsB,GACtB,IAAI,QAAQ,WACR,MAAM,QAAQ;QAElB,MAAM;QAEN,sBAAsB,GACtB,IAAI,CAAC,UAAU,IAAI,CAAC,MAChB,MAAM,QAAQ,KAAK;QAEvB,MAAM,IAAI,MAAM,CAAC;QAEjB,KAAK;IACT;IAEA,SAAS;QACL,IAAI,QAAQ;QACZ,IAAI;QACJ,OAAQ;YACJ,KAAK;gBACD,eAAe,eAAe,CAAC,cAAc,EAAE;gBAC/C;gBACA;YACJ,KAAK;gBACD;YACA,0CAA0C;YAC9C;gBACI,eAAe,WAAW,CAAC,UAAU,EAAE;gBACvC;QACR;QACA,QAAQ;QACR,KAAK;QACL,aAAa,IAAI,CAAC;IACtB;IAEA,SAAS;QACL,KAAK;QACL,UAAU;QAEV,sBAAsB,GACtB,IAAI,UAAU,MACV,MAAM,QAAQ,SAAS;QAE3B,KAAK;IACT;IAEA,SAAS;QACL,KAAK;QACL,UAAU;QACV,MAAM,oBAAoB;YAAC;SAAO;QAElC,sBAAsB,GACtB,IAAI,CAAC,kBAAkB,QAAQ,CAAC,UAC5B,MAAM,QAAQ,SAAS;QAE3B,KAAK;IACT;IAGA,SAAS,YAAY,MAAM,EAAE,KAAK;QAC9B,OAAQ;YAEJ,KAAK;gBACD,YAAY,QAAQ;gBACpB,KAAK;gBACL,OAAO;YAEX,KAAK;gBACD,UAAU,QAAQ;gBAClB,OAAO;YAEX,KAAK;gBACD,UAAU,QAAQ;gBAClB,OAAO;YAEX,KAAK;gBACD,aAAa,QAAQ;gBACrB,OAAO;YAEX,KAAK;gBACD,eAAe,QAAQ;gBACvB,OAAO;QACf;QACA,OAAO;IACX;IAEA,SAAS,QAAQ,GAAG,EAAE,IAAI,EAAE,MAAM;QAC9B,IAAI,eAAe,GAAG,IAAI;QAC1B,IAAI,KAAK;YACL,IAAG,OAAO,IAAI,OAAO,KAAK,UAAU;gBAClC,IAAI,OAAO,GAAG,QAAQ,yBAAyB;YACjD;YACA,IAAI,QAAQ,GAAG,MAAM,QAAQ;QACjC;QACA,IAAI,KAAK,KAAK,OAAO;YACjB,IAAI;YACJ,MAAO,CAAC,QAAQ,MAAM,MAAM,IACxB,KAAK;YACT,KAAK,KAAK;QACd,OAAO;YACH,IAAI,QACA;YACJ,KAAK;YACL,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,KAAK,YAAY,qBAAqB,GAChE,IAAI,OAAO,GAAG,KAAK,iBAAiB,IAAI,OAAO,EAAE,wBAAwB;QACjF;IACJ;IAEA,SAAS,UAAU,MAAM,EAAE,KAAK;QAE5B,sBAAsB,GACtB,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,SACrB,MAAM,QAAQ,OAAO;QAEzB,IAAI,OAAO,IAAI,KAAK;QACpB,QAAQ,MAAM,SAAS,gBAAgB,KAAK;YACxC,IAAI,YAAY,MAAM,QAClB;YAEJ,OAAQ;gBAEJ,KAAK;oBACD,cAAc,MAAM;oBACpB;gBAEJ,KAAK;oBACD,IAAI,YAAY,UACZ,MAAM,QAAQ;gBACtB,iCAAiC,GACjC,KAAK;oBACD,WAAW,MAAM;oBACjB;gBAEJ,KAAK;oBACD,sBAAsB,GACtB,IAAI,YAAY,UAAU;wBACtB,WAAW,MAAM;oBACrB,OAAO,IAAI,YAAY,UAAU;wBAC7B,MAAM,QAAQ;oBAClB,OAAO;wBACH,WAAW,MAAM;oBACrB;oBACA;gBAEJ,KAAK;oBACD,WAAW,MAAM;oBACjB;gBAEJ,KAAK;oBACD,WAAW,KAAK,UAAU,IAAI,CAAC,KAAK,UAAU,GAAG,EAAE;oBACnD;gBAEJ,KAAK;oBACD,WAAW,KAAK,QAAQ,IAAI,CAAC,KAAK,QAAQ,GAAG,EAAE,GAAG;oBAClD;gBAEJ;oBACI,sBAAsB,GACtB,IAAI,YAAY,YAAY,CAAC,UAAU,IAAI,CAAC,QAAQ;wBAChD,MAAM,QAAQ;oBAClB;oBAEA,KAAK;oBACL,WAAW,MAAM;oBACjB;YACR;QACJ;QACA,OAAO,GAAG,CAAC;QACX,IAAI,WAAW,KAAK;YAChB,gBAAgB,IAAI,CAAC;QACzB;IACJ;IAEA,SAAS,WAAW,MAAM,EAAE,IAAI,EAAE,MAAM;QACpC,IAAI,OAAO;QACX,IAAI,SAAS,SAAS;YAClB,WAAW,QAAQ;YACnB;QACJ;QACA,gEAAgE;QAChE,iGAAiG;QACjG,uGAAuG;QACvG,oGAAoG;QACpG,oGAAoG;QACpG,0EAA0E;QAC1E,mDAAmD;QACnD,MAAO,KAAK,QAAQ,CAAC,QAAQ,OAAO,UAAU,CAAC,KAAM;YACjD,QAAQ;QACZ;QAEA,sBAAsB,GACtB,IAAI,CAAC,UAAU,IAAI,CAAC,OAChB,MAAM,QAAQ,MAAM;QAExB,IAAI,OAAO;QAEX,sBAAsB,GAEtB,IAAI,CAAC,OAAO,IAAI,CAAC,OACb,MAAM,QAAQ,MAAM;QAExB,OAAO,UAAU;QACjB,KAAK;QAEL,IAAI,QAAQ,IAAI,MAAM,MAAM,QAAQ,SAAS,MAAM,MAAM;QAEzD,QAAQ,OAAO,SAAS,iBAAiB,KAAK;YAE1C,wBAAwB,GACxB,IAAI,UAAU,UAAU;gBACpB,YAAY,OAAO;gBACnB,KAAK;YACT,OACI,MAAM,QAAQ;QAEtB,GAAG,SAAS;YACR,mBAAmB;QACvB;QAEA,IAAI,SAAS,mBAAmB;YAC5B,2FAA2F;YAC3F,IAAI,QAAQ,IAAI,MAAM,MAAM;YAC5B,MAAM,SAAS,CAAC,mBAAmB;YACnC,MAAM,GAAG,CAAC;YACV,OAAO,GAAG,CAAC;QACf,OAAO;YACH,OAAO,GAAG,CAAC;QACf;QACA,IAAI,WAAW,KAAK;YAChB,gBAAgB,IAAI,CAAC;QACzB;IACJ;IAEA,SAAS,WAAW,MAAM,EAAE,IAAI;QAC5B,IAAI,WAAW,MAAM;YACjB,MAAM,QAAQ;QAClB;QACA,IAAI,OAAO;QAEX,sBAAsB,GACtB,IAAI,CAAC,OAAO,IAAI,CAAC,OACb,MAAM,QAAQ,MAAM;QAExB,IAAI,YAAY,KAAK,OAAO,CAAC;QAC7B,IAAI,SAAS,WACT,OAAO,KAAK,OAAO,CAAC;QACxB,KAAK;QACL,IAAI,KAAK,QAAQ;QACjB,IAAI,OAAO,IAAI,KAAK;QACpB,KAAK,KAAK,GAAG;QACb,IAAI,QAAQ,IAAI,MAAM,WAAW,IAAI,MAAM;QAC3C,MAAM,QAAQ,GAAG,MAAM,QAAQ;QAC/B,QAAQ,MAAM,SAAS,iBAAiB,KAAK;YACzC,OAAQ;gBAEJ,KAAK;oBACD,YAAY,MAAM;oBAClB,KAAK;oBACL;gBACJ,KAAK;gBACL,KAAK;oBACD,WAAW,MAAM;oBACjB;gBAEJ,KAAK;oBACD,sBAAsB,GACtB,IAAI,YAAY,UAAU;wBACtB,WAAW,MAAM;oBACrB,OAAO;wBACH,WAAW,MAAM;oBACrB;oBACA;gBAEJ,KAAK;oBACD,UAAU,MAAM;oBAChB;gBAEJ,KAAK;oBACD,UAAU,MAAM;oBAChB;gBAEJ,KAAK;oBACD,WAAW,KAAK,QAAQ,IAAI,CAAC,KAAK,QAAQ,GAAG,EAAE,GAAG;oBAClD;gBAEJ,wBAAwB,GACxB;oBACI,MAAM,QAAQ,QAAQ,4CAA4C;YAC1E;QACJ;QACA,OAAO,GAAG,CAAC,MACJ,GAAG,CAAC;IACf;IAEA,SAAS,cAAc,MAAM;QACzB,KAAK;QACL,IAAI,UAAU;QAEd,sBAAsB,GACtB,IAAI,MAAM,MAAM,CAAC,QAAQ,KAAK,WAC1B,MAAM,QAAQ,SAAS;QAE3B,KAAK;QACL,IAAI,YAAY;QAEhB,sBAAsB,GACtB,IAAI,CAAC,UAAU,IAAI,CAAC,YAChB,MAAM,QAAQ,WAAW;QAE7B,KAAK;QACL,IAAI,OAAO;QAEX,sBAAsB,GACtB,IAAI,CAAC,OAAO,IAAI,CAAC,OACb,MAAM,QAAQ,MAAM;QAExB,KAAK;QACL,IAAI,QAAQ,IAAI,SAAS,UAAU,OAAO,QAAQ,SAAS,SAAS;QACpE,QAAQ,OAAO,SAAS,oBAAoB,KAAK;YAE7C,wBAAwB,GACxB,IAAI,UAAU,UAAU;gBACpB,YAAY,OAAO;gBACnB,KAAK;YACT,OACI,MAAM,QAAQ;QAEtB,GAAG,SAAS;YACR,mBAAmB;QACvB;QACA,OAAO,GAAG,CAAC;IACf;IAEA,SAAS,WAAW,MAAM,EAAE,KAAK;QAE7B,sBAAsB,GACtB,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,SACrB,MAAM,QAAQ,OAAO;QAEzB,IAAI,QAAQ,IAAI,MAAM,UAAU;QAChC,QAAQ,OAAO,SAAS,iBAAiB,KAAK;YAC1C,IAAI,UAAU,UAAU;gBACpB,YAAY,OAAO;gBACnB,KAAK;YACT,OAAO;gBACH,KAAK;gBACL,WAAW,OAAO;YACtB;QACJ;QACA,OAAO,GAAG,CAAC;IACf;IAEA,SAAS,UAAU,MAAM,EAAE,KAAK;QAE5B,sBAAsB,GACtB,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,SACrB,MAAM,QAAQ,OAAO;QAEzB,IAAI,MAAM,IAAI,KAAK;QACnB,QAAQ,KAAK,SAAS,gBAAgB,KAAK;YACzC,OAAO;gBACL,KAAK;oBACH,YAAY,KAAK;oBACjB,KAAK;oBACL;gBAEF,KAAK;oBACH,WAAW,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,GAAG,EAAE,GAAG;oBAChD,IAAG,IAAI,QAAQ,KAAK,WAAW,IAAI,QAAQ,GAAG,EAAE;oBAChD;gBAEF;oBACE,eAAe,KAAK;YACxB;QACF;QACA,OAAO,GAAG,CAAC;QACX,IAAI,WAAW,KAAK;YAChB,gBAAgB,IAAI,CAAC;QACzB;IACJ;IAEA,SAAS,eAAe,MAAM,EAAE,KAAK;QAEjC,sBAAsB,GACtB,IAAI,CAAC,OAAO,IAAI,CAAC,QACb,MAAM,QAAQ,OAAO;QAEzB,KAAK;QACL,IAAI,QAAQ,QAAQ,QAAQ,OACxB,QAAQ;YACJ,SAAS;QACb;QACJ,MAAM,SAAS,GAAG,SAAS,IAAI;YAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK;QAC7B;QACA,MAAM,SAAS,GAAG,SAAS,IAAI,EAAE,KAAK;YAClC,iBAAiB,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,MAAM;QAC3D;QACA,MAAM,eAAe,GAAG;YACpB,OAAO;QACX;QACA,QAAQ,OAAO,SAAS,qBAAqB,KAAK;YAE9C,wBAAwB,GACxB,IAAI,UAAU,UAAU;gBACpB,YAAY,OAAO,QAAQ,OAAO;gBAClC,KAAK;YACT,OACI,MAAM,QAAQ;QAEtB,GAAG,SAAS;YACR,mBAAmB,QAAQ,OAAO;QACtC;QACA,OAAO,GAAG,CAAC,OAAO,OAAO,MAAM,OAAO,EAAE,MAAM,aAAa,IAAI,MAAM,OAAO;IAChF;IAEA,SAAS,YAAY,MAAM,EAAE,KAAK;QAC1B,IAAI;QACJ,IAAI;QACJ,IAAI,WAAW;QACf,IAAI,UAAU,UAAU;YACpB,QAAQ;QACZ;QAEA,MAAO,UAAU,IAAK;YAClB,IAAI,UAAU,KAAK;gBACf,IAAI,cAAc;gBAClB,KAAK;gBACL,QAAQ,MAAM,cAAc;YAChC;YACA,IAAI,UAAU;gBACV,WAAW;gBACX,IAAI,MAAM,QAAQ,CAAC,QAAQ,CAAC,MAAM,QAAQ,CAAC,MAAM;oBAC7C,IAAI,SAAS,MAAM,KAAK,CAAC;oBACzB,SAAS,MAAM,CAAC,EAAE,GAAG;oBACrB,QAAQ,MAAM,CAAC,EAAE;oBACjB;gBACJ;gBACA,SAAS;YACb,OAAO;gBACH,WAAW,WAAW,YAAY,QAAQ;YAC9C;YACA,QAAQ;QACZ;QACA,IAAI,OAAO,WAAW,OAAO,MAAM,CAAC,YAAY;QAChD,IAAI,cAAc,iBAAiB,QAAQ;QAC3C,WAAW,YAAY,QAAQ,CAAC,EAAE,KAAK,MAAM,SAAS,KAAK,CAAC,KAAK;QACjE,SAAS,UAAU,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,KAAK,MAAM,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK;QAC7E,gBAAgB,QAAQ,QAAQ,aAAa;IACrD;IAEA,SAAS,iBAAiB,MAAM,EAAE,IAAI;QAClC,8BAA8B;QAC9B,IAAI,KAAK,KAAK,OAAO;YACjB,IAAI,eAAe,CAAC;YAEpB,MAAO,CAAC,KAAK,KAAK,MAAO;gBACrB,sBAAsB,GACtB,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,SAAS;oBAC9B,MAAM,QAAQ,OAAO;gBACzB;gBACA,IAAI,UAAU,MAAM;oBAClB,MAAM,QAAQ,OAAO;gBACvB;gBAEA,IAAI;gBACJ,IAAI,WAAW;gBAEf,KAAK,KAAK;gBAEV,IAAI,WAAW,KAAK;oBAChB,yBAAyB;oBACzB,uCAAuC;oBACvC,KAAK;oBACL,QAAQ,iBAAiB,QAAQ,OAAO,MAAM;gBAClD,OAAO,IAAI,WAAW,KAAK;oBACvB,QAAQ,EAAE;oBACV,IAAI;oBACJ,IAAI,KAAK,KAAK,OAAO;wBACjB,GAAG;4BACC,YAAY,UAAU;4BACtB,MAAM,IAAI,CAAC;wBACf,QAAS,KAAK,KAAK,MAAO;wBAC1B,KAAK;wBACL,IAAI,OAAO,cAAc,aAAa;4BAClC,UAAU,QAAQ,OAAO,MAAM,OAAO;wBAC1C;oBACJ;gBACJ,OAAO;oBACH,QAAQ,UAAU;oBAClB,UAAU,QAAQ,OAAO,MAAM,OAAO;gBAC1C;gBAEA,IAAI,YAAY,YAAY,CAAC,SAAS;gBAEtC,IAAI,WACA,QAAQ,EAAE,CAAC,MAAM,CAAC,WAAW,MAAM,CAAC;gBAExC,YAAY,CAAC,SAAS,GAAG;gBAEzB,wCAAwC;gBACxC,KAAK,KAAK;gBACV,KAAK,KAAK;YACd;YAEA,OAAO;QACX;QAEA,IAAI,cAAc,UAAU;QAC5B,UAAU,QAAQ,MAAM;QACxB,OAAO;IACP,+CAA+C;IACnD;IAEA,SAAS,UAAU,MAAM,EAAE,IAAI,EAAE,KAAK;QAClC,IAAI,QAAQ,UAAU,cAAc,IAAI,CAAC,OAAO;YAC5C,eAAe,CAAC,KAAK,GAAG;YACxB;QACJ;QACA,IAAI,OAAO,SAAS,EAChB,OAAO,SAAS,CAAC,MAAM;IAC/B;IAEA,SAAS,gBAAgB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ;QAClD,IAAI,OAAO,eAAe,EACtB,OAAO,eAAe,CAAC,MAAM,OAAO;IAC5C;IAEA,SAAS,mBAAmB,MAAM;QAC9B,IAAI,KAAK,KAAK,OAAO;YACjB,GAAG;gBACC,YAAY,QAAQ;YACxB,QAAS,KAAK,KAAK,MAAO;YAC1B,KAAK;QACT;QACA,OAAO;IACX;IAEA,SAAS,aAAa,MAAM,EAAE,KAAK;QAE/B,sBAAsB,GACtB,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,SACrB,MAAM,QAAQ,OAAO;QAEzB,IAAI,UAAU,IAAI,QAAQ;QAC1B,QAAQ,SAAS,SAAS,mBAAmB,KAAK;YAC9C,IAAI,YAAY,SAAS,QAAQ;gBAC7B;YACJ;YAEA,wBAAwB,GACxB,IAAI,UAAU,OACV,YAAY,SAAS;iBAErB,MAAM,QAAQ;QACtB;QACA,OAAO,GAAG,CAAC;QACX,IAAI,WAAW,KAAK;YAChB,gBAAgB,IAAI,CAAC;QACzB;IACJ;IAEA,SAAS,YAAY,MAAM,EAAE,KAAK;QAC9B,wEAAwE;QACxE,2CAA2C;QAC3C,IAAI,cAAc;QAElB,IAAI,OAAO;QAEX,sBAAsB,GACtB,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,SACrB,MAAM,QAAQ,OAAO;QAEzB,IAAI,OAAO,OACP,aAAa,eACb,cAAc;QAElB,KAAK;QACL,IAAI,KAAK,UAAU,OACf,gBAAgB;QAEpB,sBAAsB,GACtB,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ,SACxB,MAAM,QAAQ;QAElB,cAAc;QACd,KAAK;QAAM,KAAK;QAAY,KAAK;QACjC,IAAI,KAAK,UAAU,OACf,iBAAiB;QAErB,sBAAsB,GACtB,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ,SACxB,MAAM,QAAQ;QAElB,eAAe;QACf,KAAK;QAEL,IAAI,SAAS,IAAI,OAAO,MAAM,MAAM,aAAa,cAAc,eAAe;QAC9E,OAAO,OAAO,GAAG;QACjB,QAAQ,QAAQ,SAAS,kBAAkB,KAAK;YAE5C,wBAAwB,GACxB,IAAI,UAAU,UAAU;gBACpB,YAAY,QAAQ;gBACpB,KAAK;YACT,OACI,MAAM,QAAQ;QAEtB;QACA,OAAO,GAAG,CAAC;IACf;IAEA,SAAS,eAAe,MAAM,EAAE,KAAK;QAEjC,sBAAsB,GACtB,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ,SACxB,MAAM,QAAQ,OAAO;QAEzB,IAAI,YAAY;QAChB,QAAQ,MAAM,SAAS,qBAAqB,KAAK;YAC7C,OAAQ;gBAEJ,KAAK;gBACL,KAAK;oBACD,WAAW,QAAQ,OAAO;oBAC1B;gBAEJ,KAAK;oBACD,sBAAsB,GACtB,IAAI,YAAY,UAAU;wBACtB,WAAW,QAAQ,mBAAmB;oBAC1C,OAAO;wBACH,WAAW,QAAQ,YAAY;oBACnC;oBACA;gBAEJ;oBACI,sBAAsB,GACtB,IAAI,YAAY,YAAY,CAAC,UAAU,IAAI,CAAC,QACxC,MAAM,QAAQ;oBAClB,KAAK;oBACL,WAAW,QAAQ,YAAY;oBAC/B;YACR;QACJ;IACJ;IAEA,IAAI;IACJ,MAAO,CAAC,QAAQ,MAAM,MAAM,KAAM;QAC9B,OAAQ;YAEJ,KAAK;gBAED,sBAAsB,GACtB,IAAI,CAAC,MACD,MAAM,QAAQ;gBAElB;gBACA;YAEJ,KAAK;gBAED,sBAAsB,GACtB,IAAI,CAAC,MACD,MAAM,QAAQ;gBAElB;gBACA;YAEJ,KAAK;gBAED,sBAAsB,GACtB,IAAI,CAAC,MACD,MAAM,QAAQ;gBAElB;gBACA;YAEJ,KAAK;gBACD,sBAAsB,GACtB,IAAI,CAAC,MACD,MAAM,QAAQ;gBAClB;gBACA;YAEJ,KAAK;gBACD,YAAY,KAAK;gBACjB,KAAK,KAAK;gBACV;YAEJ;gBAEI,wBAAwB,GACxB,IAAI,YAAY,KAAK,QAAQ;oBACzB,OAAO;oBACP;gBACJ;gBAEA,wBAAwB,GACxB,MAAM,QAAQ;QACtB;IACJ;IAEA;IAEA,MAAM,QAAQ,GAAG;IACjB,OAAO;QACH,WAAgB;QAChB,WAAgB;QACf,aAAe;QACf,MAAe;IACpB;AACJ,EAEA;;;;;;;;;;CAUC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6118, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/common.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = common;\n\nvar commonRe = /\\/|\\./;\n\n/**\n * Provides common type definitions.\n * Can also be used to provide additional google types or your own custom types.\n * @param {string} name Short name as in `google/protobuf/[name].proto` or full file name\n * @param {Object.<string,*>} json JSON definition within `google.protobuf` if a short name, otherwise the file's root definition\n * @returns {undefined}\n * @property {INamespace} google/protobuf/any.proto Any\n * @property {INamespace} google/protobuf/duration.proto Duration\n * @property {INamespace} google/protobuf/empty.proto Empty\n * @property {INamespace} google/protobuf/field_mask.proto FieldMask\n * @property {INamespace} google/protobuf/struct.proto Struct, Value, NullValue and ListValue\n * @property {INamespace} google/protobuf/timestamp.proto Timestamp\n * @property {INamespace} google/protobuf/wrappers.proto Wrappers\n * @example\n * // manually provides descriptor.proto (assumes google/protobuf/ namespace and .proto extension)\n * protobuf.common(\"descriptor\", descriptorJson);\n *\n * // manually provides a custom definition (uses my.foo namespace)\n * protobuf.common(\"my/foo/bar.proto\", myFooBarJson);\n */\nfunction common(name, json) {\n    if (!commonRe.test(name)) {\n        name = \"google/protobuf/\" + name + \".proto\";\n        json = { nested: { google: { nested: { protobuf: { nested: json } } } } };\n    }\n    common[name] = json;\n}\n\n// Not provided because of limited use (feel free to discuss or to provide yourself):\n//\n// google/protobuf/descriptor.proto\n// google/protobuf/source_context.proto\n// google/protobuf/type.proto\n//\n// Stripped and pre-parsed versions of these non-bundled files are instead available as part of\n// the repository or package within the google/protobuf directory.\n\ncommon(\"any\", {\n\n    /**\n     * Properties of a google.protobuf.Any message.\n     * @interface IAny\n     * @type {Object}\n     * @property {string} [typeUrl]\n     * @property {Uint8Array} [bytes]\n     * @memberof common\n     */\n    Any: {\n        fields: {\n            type_url: {\n                type: \"string\",\n                id: 1\n            },\n            value: {\n                type: \"bytes\",\n                id: 2\n            }\n        }\n    }\n});\n\nvar timeType;\n\ncommon(\"duration\", {\n\n    /**\n     * Properties of a google.protobuf.Duration message.\n     * @interface IDuration\n     * @type {Object}\n     * @property {number|Long} [seconds]\n     * @property {number} [nanos]\n     * @memberof common\n     */\n    Duration: timeType = {\n        fields: {\n            seconds: {\n                type: \"int64\",\n                id: 1\n            },\n            nanos: {\n                type: \"int32\",\n                id: 2\n            }\n        }\n    }\n});\n\ncommon(\"timestamp\", {\n\n    /**\n     * Properties of a google.protobuf.Timestamp message.\n     * @interface ITimestamp\n     * @type {Object}\n     * @property {number|Long} [seconds]\n     * @property {number} [nanos]\n     * @memberof common\n     */\n    Timestamp: timeType\n});\n\ncommon(\"empty\", {\n\n    /**\n     * Properties of a google.protobuf.Empty message.\n     * @interface IEmpty\n     * @memberof common\n     */\n    Empty: {\n        fields: {}\n    }\n});\n\ncommon(\"struct\", {\n\n    /**\n     * Properties of a google.protobuf.Struct message.\n     * @interface IStruct\n     * @type {Object}\n     * @property {Object.<string,IValue>} [fields]\n     * @memberof common\n     */\n    Struct: {\n        fields: {\n            fields: {\n                keyType: \"string\",\n                type: \"Value\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Value message.\n     * @interface IValue\n     * @type {Object}\n     * @property {string} [kind]\n     * @property {0} [nullValue]\n     * @property {number} [numberValue]\n     * @property {string} [stringValue]\n     * @property {boolean} [boolValue]\n     * @property {IStruct} [structValue]\n     * @property {IListValue} [listValue]\n     * @memberof common\n     */\n    Value: {\n        oneofs: {\n            kind: {\n                oneof: [\n                    \"nullValue\",\n                    \"numberValue\",\n                    \"stringValue\",\n                    \"boolValue\",\n                    \"structValue\",\n                    \"listValue\"\n                ]\n            }\n        },\n        fields: {\n            nullValue: {\n                type: \"NullValue\",\n                id: 1\n            },\n            numberValue: {\n                type: \"double\",\n                id: 2\n            },\n            stringValue: {\n                type: \"string\",\n                id: 3\n            },\n            boolValue: {\n                type: \"bool\",\n                id: 4\n            },\n            structValue: {\n                type: \"Struct\",\n                id: 5\n            },\n            listValue: {\n                type: \"ListValue\",\n                id: 6\n            }\n        }\n    },\n\n    NullValue: {\n        values: {\n            NULL_VALUE: 0\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.ListValue message.\n     * @interface IListValue\n     * @type {Object}\n     * @property {Array.<IValue>} [values]\n     * @memberof common\n     */\n    ListValue: {\n        fields: {\n            values: {\n                rule: \"repeated\",\n                type: \"Value\",\n                id: 1\n            }\n        }\n    }\n});\n\ncommon(\"wrappers\", {\n\n    /**\n     * Properties of a google.protobuf.DoubleValue message.\n     * @interface IDoubleValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    DoubleValue: {\n        fields: {\n            value: {\n                type: \"double\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.FloatValue message.\n     * @interface IFloatValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    FloatValue: {\n        fields: {\n            value: {\n                type: \"float\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Int64Value message.\n     * @interface IInt64Value\n     * @type {Object}\n     * @property {number|Long} [value]\n     * @memberof common\n     */\n    Int64Value: {\n        fields: {\n            value: {\n                type: \"int64\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.UInt64Value message.\n     * @interface IUInt64Value\n     * @type {Object}\n     * @property {number|Long} [value]\n     * @memberof common\n     */\n    UInt64Value: {\n        fields: {\n            value: {\n                type: \"uint64\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Int32Value message.\n     * @interface IInt32Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    Int32Value: {\n        fields: {\n            value: {\n                type: \"int32\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.UInt32Value message.\n     * @interface IUInt32Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    UInt32Value: {\n        fields: {\n            value: {\n                type: \"uint32\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.BoolValue message.\n     * @interface IBoolValue\n     * @type {Object}\n     * @property {boolean} [value]\n     * @memberof common\n     */\n    BoolValue: {\n        fields: {\n            value: {\n                type: \"bool\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.StringValue message.\n     * @interface IStringValue\n     * @type {Object}\n     * @property {string} [value]\n     * @memberof common\n     */\n    StringValue: {\n        fields: {\n            value: {\n                type: \"string\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.BytesValue message.\n     * @interface IBytesValue\n     * @type {Object}\n     * @property {Uint8Array} [value]\n     * @memberof common\n     */\n    BytesValue: {\n        fields: {\n            value: {\n                type: \"bytes\",\n                id: 1\n            }\n        }\n    }\n});\n\ncommon(\"field_mask\", {\n\n    /**\n     * Properties of a google.protobuf.FieldMask message.\n     * @interface IDoubleValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    FieldMask: {\n        fields: {\n            paths: {\n                rule: \"repeated\",\n                type: \"string\",\n                id: 1\n            }\n        }\n    }\n});\n\n/**\n * Gets the root definition of the specified common proto file.\n *\n * Bundled definitions are:\n * - google/protobuf/any.proto\n * - google/protobuf/duration.proto\n * - google/protobuf/empty.proto\n * - google/protobuf/field_mask.proto\n * - google/protobuf/struct.proto\n * - google/protobuf/timestamp.proto\n * - google/protobuf/wrappers.proto\n *\n * @param {string} file Proto file name\n * @returns {INamespace|null} Root definition or `null` if not defined\n */\ncommon.get = function get(file) {\n    return common[file] || null;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG;AAEjB,IAAI,WAAW;AAEf;;;;;;;;;;;;;;;;;;;CAmBC,GACD,SAAS,OAAO,IAAI,EAAE,IAAI;IACtB,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO;QACtB,OAAO,qBAAqB,OAAO;QACnC,OAAO;YAAE,QAAQ;gBAAE,QAAQ;oBAAE,QAAQ;wBAAE,UAAU;4BAAE,QAAQ;wBAAK;oBAAE;gBAAE;YAAE;QAAE;IAC5E;IACA,MAAM,CAAC,KAAK,GAAG;AACnB;AAEA,qFAAqF;AACrF,EAAE;AACF,mCAAmC;AACnC,uCAAuC;AACvC,6BAA6B;AAC7B,EAAE;AACF,+FAA+F;AAC/F,kEAAkE;AAElE,OAAO,OAAO;IAEV;;;;;;;KAOC,GACD,KAAK;QACD,QAAQ;YACJ,UAAU;gBACN,MAAM;gBACN,IAAI;YACR;YACA,OAAO;gBACH,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;AACJ;AAEA,IAAI;AAEJ,OAAO,YAAY;IAEf;;;;;;;KAOC,GACD,UAAU,WAAW;QACjB,QAAQ;YACJ,SAAS;gBACL,MAAM;gBACN,IAAI;YACR;YACA,OAAO;gBACH,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;AACJ;AAEA,OAAO,aAAa;IAEhB;;;;;;;KAOC,GACD,WAAW;AACf;AAEA,OAAO,SAAS;IAEZ;;;;KAIC,GACD,OAAO;QACH,QAAQ,CAAC;IACb;AACJ;AAEA,OAAO,UAAU;IAEb;;;;;;KAMC,GACD,QAAQ;QACJ,QAAQ;YACJ,QAAQ;gBACJ,SAAS;gBACT,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;IAEA;;;;;;;;;;;;KAYC,GACD,OAAO;QACH,QAAQ;YACJ,MAAM;gBACF,OAAO;oBACH;oBACA;oBACA;oBACA;oBACA;oBACA;iBACH;YACL;QACJ;QACA,QAAQ;YACJ,WAAW;gBACP,MAAM;gBACN,IAAI;YACR;YACA,aAAa;gBACT,MAAM;gBACN,IAAI;YACR;YACA,aAAa;gBACT,MAAM;gBACN,IAAI;YACR;YACA,WAAW;gBACP,MAAM;gBACN,IAAI;YACR;YACA,aAAa;gBACT,MAAM;gBACN,IAAI;YACR;YACA,WAAW;gBACP,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;IAEA,WAAW;QACP,QAAQ;YACJ,YAAY;QAChB;IACJ;IAEA;;;;;;KAMC,GACD,WAAW;QACP,QAAQ;YACJ,QAAQ;gBACJ,MAAM;gBACN,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;AACJ;AAEA,OAAO,YAAY;IAEf;;;;;;KAMC,GACD,aAAa;QACT,QAAQ;YACJ,OAAO;gBACH,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;IAEA;;;;;;KAMC,GACD,YAAY;QACR,QAAQ;YACJ,OAAO;gBACH,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;IAEA;;;;;;KAMC,GACD,YAAY;QACR,QAAQ;YACJ,OAAO;gBACH,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;IAEA;;;;;;KAMC,GACD,aAAa;QACT,QAAQ;YACJ,OAAO;gBACH,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;IAEA;;;;;;KAMC,GACD,YAAY;QACR,QAAQ;YACJ,OAAO;gBACH,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;IAEA;;;;;;KAMC,GACD,aAAa;QACT,QAAQ;YACJ,OAAO;gBACH,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;IAEA;;;;;;KAMC,GACD,WAAW;QACP,QAAQ;YACJ,OAAO;gBACH,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;IAEA;;;;;;KAMC,GACD,aAAa;QACT,QAAQ;YACJ,OAAO;gBACH,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;IAEA;;;;;;KAMC,GACD,YAAY;QACR,QAAQ;YACJ,OAAO;gBACH,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;AACJ;AAEA,OAAO,cAAc;IAEjB;;;;;;KAMC,GACD,WAAW;QACP,QAAQ;YACJ,OAAO;gBACH,MAAM;gBACN,MAAM;gBACN,IAAI;YACR;QACJ;IACJ;AACJ;AAEA;;;;;;;;;;;;;;CAcC,GACD,OAAO,GAAG,GAAG,SAAS,IAAI,IAAI;IAC1B,OAAO,MAAM,CAAC,KAAK,IAAI;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6483, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/src/index.js"], "sourcesContent": ["\"use strict\";\nvar protobuf = module.exports = require(\"./index-light\");\n\nprotobuf.build = \"full\";\n\n// Parser\nprotobuf.tokenize         = require(\"./tokenize\");\nprotobuf.parse            = require(\"./parse\");\nprotobuf.common           = require(\"./common\");\n\n// Configure parser\nprotobuf.Root._configure(protobuf.Type, protobuf.parse, protobuf.common);\n"], "names": [], "mappings": "AAAA;AACA,IAAI,WAAW,OAAO,OAAO;AAE7B,SAAS,KAAK,GAAG;AAEjB,SAAS;AACT,SAAS,QAAQ;AACjB,SAAS,KAAK;AACd,SAAS,MAAM;AAEf,mBAAmB;AACnB,SAAS,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6497, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/index.js"], "sourcesContent": ["// full library entry point.\n\n\"use strict\";\nmodule.exports = require(\"./src/index\");\n"], "names": [], "mappings": "AAAA,4BAA4B;AAE5B;AACA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6510, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/protobufjs/ext/descriptor/index.js"], "sourcesContent": ["\"use strict\";\nvar $protobuf = require(\"../..\");\nmodule.exports = exports = $protobuf.descriptor = $protobuf.Root.fromJSON(require(\"../../google/protobuf/descriptor.json\")).lookup(\".google.protobuf\");\n\nvar Namespace = $protobuf.Namespace,\n    Root      = $protobuf.Root,\n    Enum      = $protobuf.Enum,\n    Type      = $protobuf.Type,\n    Field     = $protobuf.Field,\n    MapField  = $protobuf.MapField,\n    OneOf     = $protobuf.OneOf,\n    Service   = $protobuf.Service,\n    Method    = $protobuf.Method;\n\n// --- Root ---\n\n/**\n * Properties of a FileDescriptorSet message.\n * @interface IFileDescriptorSet\n * @property {IFileDescriptorProto[]} file Files\n */\n\n/**\n * Properties of a FileDescriptorProto message.\n * @interface IFileDescriptorProto\n * @property {string} [name] File name\n * @property {string} [package] Package\n * @property {*} [dependency] Not supported\n * @property {*} [publicDependency] Not supported\n * @property {*} [weakDependency] Not supported\n * @property {IDescriptorProto[]} [messageType] Nested message types\n * @property {IEnumDescriptorProto[]} [enumType] Nested enums\n * @property {IServiceDescriptorProto[]} [service] Nested services\n * @property {IFieldDescriptorProto[]} [extension] Nested extension fields\n * @property {IFileOptions} [options] Options\n * @property {*} [sourceCodeInfo] Not supported\n * @property {string} [syntax=\"proto2\"] Syntax\n * @property {IEdition} [edition] Edition\n */\n\n/**\n * Values of the Edition enum.\n * @typedef IEdition\n * @type {number}\n * @property {number} EDITION_UNKNOWN=0\n * @property {number} EDITION_LEGACY=900\n * @property {number} EDITION_PROTO2=998\n * @property {number} EDITION_PROTO3=999\n * @property {number} EDITION_2023=1000\n * @property {number} EDITION_2024=1001\n * @property {number} EDITION_1_TEST_ONLY=1\n * @property {number} EDITION_2_TEST_ONLY=2\n * @property {number} EDITION_99997_TEST_ONLY=99997\n * @property {number} EDITION_99998_TEST_ONLY=99998\n * @property {number} EDITION_99998_TEST_ONLY=99999\n * @property {number} EDITION_MAX=2147483647\n */\n\n/**\n * Properties of a FileOptions message.\n * @interface IFileOptions\n * @property {string} [javaPackage]\n * @property {string} [javaOuterClassname]\n * @property {boolean} [javaMultipleFiles]\n * @property {boolean} [javaGenerateEqualsAndHash]\n * @property {boolean} [javaStringCheckUtf8]\n * @property {IFileOptionsOptimizeMode} [optimizeFor=1]\n * @property {string} [goPackage]\n * @property {boolean} [ccGenericServices]\n * @property {boolean} [javaGenericServices]\n * @property {boolean} [pyGenericServices]\n * @property {boolean} [deprecated]\n * @property {boolean} [ccEnableArenas]\n * @property {string} [objcClassPrefix]\n * @property {string} [csharpNamespace]\n */\n\n/**\n * Values of he FileOptions.OptimizeMode enum.\n * @typedef IFileOptionsOptimizeMode\n * @type {number}\n * @property {number} SPEED=1\n * @property {number} CODE_SIZE=2\n * @property {number} LITE_RUNTIME=3\n */\n\n/**\n * Creates a root from a descriptor set.\n * @param {IFileDescriptorSet|Reader|Uint8Array} descriptor Descriptor\n * @returns {Root} Root instance\n */\nRoot.fromDescriptor = function fromDescriptor(descriptor) {\n\n    // Decode the descriptor message if specified as a buffer:\n    if (typeof descriptor.length === \"number\")\n        descriptor = exports.FileDescriptorSet.decode(descriptor);\n\n    var root = new Root();\n\n    if (descriptor.file) {\n        var fileDescriptor,\n            filePackage;\n        for (var j = 0, i; j < descriptor.file.length; ++j) {\n            filePackage = root;\n            if ((fileDescriptor = descriptor.file[j])[\"package\"] && fileDescriptor[\"package\"].length)\n                filePackage = root.define(fileDescriptor[\"package\"]);\n            var edition = editionFromDescriptor(fileDescriptor);\n            if (fileDescriptor.name && fileDescriptor.name.length)\n                root.files.push(filePackage.filename = fileDescriptor.name);\n            if (fileDescriptor.messageType)\n                for (i = 0; i < fileDescriptor.messageType.length; ++i)\n                    filePackage.add(Type.fromDescriptor(fileDescriptor.messageType[i], edition));\n            if (fileDescriptor.enumType)\n                for (i = 0; i < fileDescriptor.enumType.length; ++i)\n                    filePackage.add(Enum.fromDescriptor(fileDescriptor.enumType[i], edition));\n            if (fileDescriptor.extension)\n                for (i = 0; i < fileDescriptor.extension.length; ++i)\n                    filePackage.add(Field.fromDescriptor(fileDescriptor.extension[i], edition));\n            if (fileDescriptor.service)\n                for (i = 0; i < fileDescriptor.service.length; ++i)\n                    filePackage.add(Service.fromDescriptor(fileDescriptor.service[i], edition));\n            var opts = fromDescriptorOptions(fileDescriptor.options, exports.FileOptions);\n            if (opts) {\n                var ks = Object.keys(opts);\n                for (i = 0; i < ks.length; ++i)\n                    filePackage.setOption(ks[i], opts[ks[i]]);\n            }\n        }\n    }\n\n    return root.resolveAll();\n};\n\n/**\n * Converts a root to a descriptor set.\n * @returns {Message<IFileDescriptorSet>} Descriptor\n * @param {string} [edition=\"proto2\"] The syntax or edition to use\n */\nRoot.prototype.toDescriptor = function toDescriptor(edition) {\n    var set = exports.FileDescriptorSet.create();\n    Root_toDescriptorRecursive(this, set.file, edition);\n    return set;\n};\n\n// Traverses a namespace and assembles the descriptor set\nfunction Root_toDescriptorRecursive(ns, files, edition) {\n\n    // Create a new file\n    var file = exports.FileDescriptorProto.create({ name: ns.filename || (ns.fullName.substring(1).replace(/\\./g, \"_\") || \"root\") + \".proto\" });\n    editionToDescriptor(edition, file);\n    if (!(ns instanceof Root))\n        file[\"package\"] = ns.fullName.substring(1);\n\n    // Add nested types\n    for (var i = 0, nested; i < ns.nestedArray.length; ++i)\n        if ((nested = ns._nestedArray[i]) instanceof Type)\n            file.messageType.push(nested.toDescriptor(edition));\n        else if (nested instanceof Enum)\n            file.enumType.push(nested.toDescriptor());\n        else if (nested instanceof Field)\n            file.extension.push(nested.toDescriptor(edition));\n        else if (nested instanceof Service)\n            file.service.push(nested.toDescriptor());\n        else if (nested instanceof /* plain */ Namespace)\n            Root_toDescriptorRecursive(nested, files, edition); // requires new file\n\n    // Keep package-level options\n    file.options = toDescriptorOptions(ns.options, exports.FileOptions);\n\n    // And keep the file only if there is at least one nested object\n    if (file.messageType.length + file.enumType.length + file.extension.length + file.service.length)\n        files.push(file);\n}\n\n// --- Type ---\n\n/**\n * Properties of a DescriptorProto message.\n * @interface IDescriptorProto\n * @property {string} [name] Message type name\n * @property {IFieldDescriptorProto[]} [field] Fields\n * @property {IFieldDescriptorProto[]} [extension] Extension fields\n * @property {IDescriptorProto[]} [nestedType] Nested message types\n * @property {IEnumDescriptorProto[]} [enumType] Nested enums\n * @property {IDescriptorProtoExtensionRange[]} [extensionRange] Extension ranges\n * @property {IOneofDescriptorProto[]} [oneofDecl] Oneofs\n * @property {IMessageOptions} [options] Not supported\n * @property {IDescriptorProtoReservedRange[]} [reservedRange] Reserved ranges\n * @property {string[]} [reservedName] Reserved names\n */\n\n/**\n * Properties of a MessageOptions message.\n * @interface IMessageOptions\n * @property {boolean} [mapEntry=false] Whether this message is a map entry\n */\n\n/**\n * Properties of an ExtensionRange message.\n * @interface IDescriptorProtoExtensionRange\n * @property {number} [start] Start field id\n * @property {number} [end] End field id\n */\n\n/**\n * Properties of a ReservedRange message.\n * @interface IDescriptorProtoReservedRange\n * @property {number} [start] Start field id\n * @property {number} [end] End field id\n */\n\nvar unnamedMessageIndex = 0;\n\n/**\n * Creates a type from a descriptor.\n *\n * Warning: this is not safe to use with editions protos, since it discards relevant file context.\n *\n * @param {IDescriptorProto|Reader|Uint8Array} descriptor Descriptor\n * @param {string} [edition=\"proto2\"] The syntax or edition to use\n * @param {boolean} [nested=false] Whether or not this is a nested object\n * @returns {Type} Type instance\n */\nType.fromDescriptor = function fromDescriptor(descriptor, edition, nested) {\n    // Decode the descriptor message if specified as a buffer:\n    if (typeof descriptor.length === \"number\")\n        descriptor = exports.DescriptorProto.decode(descriptor);\n\n    // Create the message type\n    var type = new Type(descriptor.name.length ? descriptor.name : \"Type\" + unnamedMessageIndex++, fromDescriptorOptions(descriptor.options, exports.MessageOptions)),\n        i;\n\n    if (!nested)\n        type._edition = edition;\n\n    /* Oneofs */ if (descriptor.oneofDecl)\n        for (i = 0; i < descriptor.oneofDecl.length; ++i)\n            type.add(OneOf.fromDescriptor(descriptor.oneofDecl[i]));\n    /* Fields */ if (descriptor.field)\n        for (i = 0; i < descriptor.field.length; ++i) {\n            var field = Field.fromDescriptor(descriptor.field[i], edition, true);\n            type.add(field);\n            if (descriptor.field[i].hasOwnProperty(\"oneofIndex\")) // eslint-disable-line no-prototype-builtins\n                type.oneofsArray[descriptor.field[i].oneofIndex].add(field);\n        }\n    /* Extension fields */ if (descriptor.extension)\n        for (i = 0; i < descriptor.extension.length; ++i)\n            type.add(Field.fromDescriptor(descriptor.extension[i], edition, true));\n    /* Nested types */ if (descriptor.nestedType)\n        for (i = 0; i < descriptor.nestedType.length; ++i) {\n            type.add(Type.fromDescriptor(descriptor.nestedType[i], edition, true));\n            if (descriptor.nestedType[i].options && descriptor.nestedType[i].options.mapEntry)\n                type.setOption(\"map_entry\", true);\n        }\n    /* Nested enums */ if (descriptor.enumType)\n        for (i = 0; i < descriptor.enumType.length; ++i)\n            type.add(Enum.fromDescriptor(descriptor.enumType[i], edition, true));\n    /* Extension ranges */ if (descriptor.extensionRange && descriptor.extensionRange.length) {\n        type.extensions = [];\n        for (i = 0; i < descriptor.extensionRange.length; ++i)\n            type.extensions.push([ descriptor.extensionRange[i].start, descriptor.extensionRange[i].end ]);\n    }\n    /* Reserved... */ if (descriptor.reservedRange && descriptor.reservedRange.length || descriptor.reservedName && descriptor.reservedName.length) {\n        type.reserved = [];\n        /* Ranges */ if (descriptor.reservedRange)\n            for (i = 0; i < descriptor.reservedRange.length; ++i)\n                type.reserved.push([ descriptor.reservedRange[i].start, descriptor.reservedRange[i].end ]);\n        /* Names */ if (descriptor.reservedName)\n            for (i = 0; i < descriptor.reservedName.length; ++i)\n                type.reserved.push(descriptor.reservedName[i]);\n    }\n\n    return type;\n};\n\n/**\n * Converts a type to a descriptor.\n * @returns {Message<IDescriptorProto>} Descriptor\n * @param {string} [edition=\"proto2\"] The syntax or edition to use\n */\nType.prototype.toDescriptor = function toDescriptor(edition) {\n    var descriptor = exports.DescriptorProto.create({ name: this.name }),\n        i;\n\n    /* Fields */ for (i = 0; i < this.fieldsArray.length; ++i) {\n        var fieldDescriptor;\n        descriptor.field.push(fieldDescriptor = this._fieldsArray[i].toDescriptor(edition));\n        if (this._fieldsArray[i] instanceof MapField) { // map fields are repeated FieldNameEntry\n            var keyType = toDescriptorType(this._fieldsArray[i].keyType, this._fieldsArray[i].resolvedKeyType, false),\n                valueType = toDescriptorType(this._fieldsArray[i].type, this._fieldsArray[i].resolvedType, false),\n                valueTypeName = valueType === /* type */ 11 || valueType === /* enum */ 14\n                    ? this._fieldsArray[i].resolvedType && shortname(this.parent, this._fieldsArray[i].resolvedType) || this._fieldsArray[i].type\n                    : undefined;\n            descriptor.nestedType.push(exports.DescriptorProto.create({\n                name: fieldDescriptor.typeName,\n                field: [\n                    exports.FieldDescriptorProto.create({ name: \"key\", number: 1, label: 1, type: keyType }), // can't reference a type or enum\n                    exports.FieldDescriptorProto.create({ name: \"value\", number: 2, label: 1, type: valueType, typeName: valueTypeName })\n                ],\n                options: exports.MessageOptions.create({ mapEntry: true })\n            }));\n        }\n    }\n    /* Oneofs */ for (i = 0; i < this.oneofsArray.length; ++i)\n        descriptor.oneofDecl.push(this._oneofsArray[i].toDescriptor());\n    /* Nested... */ for (i = 0; i < this.nestedArray.length; ++i) {\n        /* Extension fields */ if (this._nestedArray[i] instanceof Field)\n            descriptor.field.push(this._nestedArray[i].toDescriptor(edition));\n        /* Types */ else if (this._nestedArray[i] instanceof Type)\n            descriptor.nestedType.push(this._nestedArray[i].toDescriptor(edition));\n        /* Enums */ else if (this._nestedArray[i] instanceof Enum)\n            descriptor.enumType.push(this._nestedArray[i].toDescriptor());\n        // plain nested namespaces become packages instead in Root#toDescriptor\n    }\n    /* Extension ranges */ if (this.extensions)\n        for (i = 0; i < this.extensions.length; ++i)\n            descriptor.extensionRange.push(exports.DescriptorProto.ExtensionRange.create({ start: this.extensions[i][0], end: this.extensions[i][1] }));\n    /* Reserved... */ if (this.reserved)\n        for (i = 0; i < this.reserved.length; ++i)\n            /* Names */ if (typeof this.reserved[i] === \"string\")\n                descriptor.reservedName.push(this.reserved[i]);\n            /* Ranges */ else\n                descriptor.reservedRange.push(exports.DescriptorProto.ReservedRange.create({ start: this.reserved[i][0], end: this.reserved[i][1] }));\n\n    descriptor.options = toDescriptorOptions(this.options, exports.MessageOptions);\n\n    return descriptor;\n};\n\n// --- Field ---\n\n/**\n * Properties of a FieldDescriptorProto message.\n * @interface IFieldDescriptorProto\n * @property {string} [name] Field name\n * @property {number} [number] Field id\n * @property {IFieldDescriptorProtoLabel} [label] Field rule\n * @property {IFieldDescriptorProtoType} [type] Field basic type\n * @property {string} [typeName] Field type name\n * @property {string} [extendee] Extended type name\n * @property {string} [defaultValue] Literal default value\n * @property {number} [oneofIndex] Oneof index if part of a oneof\n * @property {*} [jsonName] Not supported\n * @property {IFieldOptions} [options] Field options\n */\n\n/**\n * Values of the FieldDescriptorProto.Label enum.\n * @typedef IFieldDescriptorProtoLabel\n * @type {number}\n * @property {number} LABEL_OPTIONAL=1\n * @property {number} LABEL_REQUIRED=2\n * @property {number} LABEL_REPEATED=3\n */\n\n/**\n * Values of the FieldDescriptorProto.Type enum.\n * @typedef IFieldDescriptorProtoType\n * @type {number}\n * @property {number} TYPE_DOUBLE=1\n * @property {number} TYPE_FLOAT=2\n * @property {number} TYPE_INT64=3\n * @property {number} TYPE_UINT64=4\n * @property {number} TYPE_INT32=5\n * @property {number} TYPE_FIXED64=6\n * @property {number} TYPE_FIXED32=7\n * @property {number} TYPE_BOOL=8\n * @property {number} TYPE_STRING=9\n * @property {number} TYPE_GROUP=10\n * @property {number} TYPE_MESSAGE=11\n * @property {number} TYPE_BYTES=12\n * @property {number} TYPE_UINT32=13\n * @property {number} TYPE_ENUM=14\n * @property {number} TYPE_SFIXED32=15\n * @property {number} TYPE_SFIXED64=16\n * @property {number} TYPE_SINT32=17\n * @property {number} TYPE_SINT64=18\n */\n\n/**\n * Properties of a FieldOptions message.\n * @interface IFieldOptions\n * @property {boolean} [packed] Whether packed or not (defaults to `false` for proto2 and `true` for proto3)\n * @property {IFieldOptionsJSType} [jstype] JavaScript value type (not used by protobuf.js)\n */\n\n/**\n * Values of the FieldOptions.JSType enum.\n * @typedef IFieldOptionsJSType\n * @type {number}\n * @property {number} JS_NORMAL=0\n * @property {number} JS_STRING=1\n * @property {number} JS_NUMBER=2\n */\n\n// copied here from parse.js\nvar numberRe = /^(?![eE])[0-9]*(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/;\n\n/**\n * Creates a field from a descriptor.\n *\n * Warning: this is not safe to use with editions protos, since it discards relevant file context.\n *\n * @param {IFieldDescriptorProto|Reader|Uint8Array} descriptor Descriptor\n * @param {string} [edition=\"proto2\"] The syntax or edition to use\n * @param {boolean} [nested=false] Whether or not this is a top-level object\n * @returns {Field} Field instance\n */\nField.fromDescriptor = function fromDescriptor(descriptor, edition, nested) {\n\n    // Decode the descriptor message if specified as a buffer:\n    if (typeof descriptor.length === \"number\")\n        descriptor = exports.DescriptorProto.decode(descriptor);\n\n    if (typeof descriptor.number !== \"number\")\n        throw Error(\"missing field id\");\n\n    // Rewire field type\n    var fieldType;\n    if (descriptor.typeName && descriptor.typeName.length)\n        fieldType = descriptor.typeName;\n    else\n        fieldType = fromDescriptorType(descriptor.type);\n\n    // Rewire field rule\n    var fieldRule;\n    switch (descriptor.label) {\n        // 0 is reserved for errors\n        case 1: fieldRule = undefined; break;\n        case 2: fieldRule = \"required\"; break;\n        case 3: fieldRule = \"repeated\"; break;\n        default: throw Error(\"illegal label: \" + descriptor.label);\n    }\n\n\tvar extendee = descriptor.extendee;\n\tif (descriptor.extendee !== undefined) {\n\t\textendee = extendee.length ? extendee : undefined;\n\t}\n    var field = new Field(\n        descriptor.name.length ? descriptor.name : \"field\" + descriptor.number,\n        descriptor.number,\n        fieldType,\n        fieldRule,\n        extendee\n    );\n\n    if (!nested)\n        field._edition = edition;\n\n    field.options = fromDescriptorOptions(descriptor.options, exports.FieldOptions);\n    if (descriptor.proto3_optional)\n        field.options.proto3_optional = true;\n\n    if (descriptor.defaultValue && descriptor.defaultValue.length) {\n        var defaultValue = descriptor.defaultValue;\n        switch (defaultValue) {\n            case \"true\": case \"TRUE\":\n                defaultValue = true;\n                break;\n            case \"false\": case \"FALSE\":\n                defaultValue = false;\n                break;\n            default:\n                var match = numberRe.exec(defaultValue);\n                if (match)\n                    defaultValue = parseInt(defaultValue); // eslint-disable-line radix\n                break;\n        }\n        field.setOption(\"default\", defaultValue);\n    }\n\n    if (packableDescriptorType(descriptor.type)) {\n        if (edition === \"proto3\") { // defaults to packed=true (internal preset is packed=true)\n            if (descriptor.options && !descriptor.options.packed)\n                field.setOption(\"packed\", false);\n        } else if ((!edition || edition === \"proto2\") && descriptor.options && descriptor.options.packed) // defaults to packed=false\n            field.setOption(\"packed\", true);\n    }\n\n    return field;\n};\n\n/**\n * Converts a field to a descriptor.\n * @returns {Message<IFieldDescriptorProto>} Descriptor\n * @param {string} [edition=\"proto2\"] The syntax or edition to use\n */\nField.prototype.toDescriptor = function toDescriptor(edition) {\n    var descriptor = exports.FieldDescriptorProto.create({ name: this.name, number: this.id });\n\n    if (this.map) {\n\n        descriptor.type = 11; // message\n        descriptor.typeName = $protobuf.util.ucFirst(this.name); // fieldName -> FieldNameEntry (built in Type#toDescriptor)\n        descriptor.label = 3; // repeated\n\n    } else {\n\n        // Rewire field type\n        switch (descriptor.type = toDescriptorType(this.type, this.resolve().resolvedType, this.delimited)) {\n            case 10: // group\n            case 11: // type\n            case 14: // enum\n                descriptor.typeName = this.resolvedType ? shortname(this.parent, this.resolvedType) : this.type;\n                break;\n        }\n\n        // Rewire field rule\n        if (this.rule === \"repeated\") {\n            descriptor.label = 3;\n        } else if (this.required && edition === \"proto2\") {\n            descriptor.label = 2;\n        } else {\n            descriptor.label = 1;\n        }\n    }\n\n    // Handle extension field\n    descriptor.extendee = this.extensionField ? this.extensionField.parent.fullName : this.extend;\n\n    // Handle part of oneof\n    if (this.partOf)\n        if ((descriptor.oneofIndex = this.parent.oneofsArray.indexOf(this.partOf)) < 0)\n            throw Error(\"missing oneof\");\n\n    if (this.options) {\n        descriptor.options = toDescriptorOptions(this.options, exports.FieldOptions);\n        if (this.options[\"default\"] != null)\n            descriptor.defaultValue = String(this.options[\"default\"]);\n        if (this.options.proto3_optional)\n            descriptor.proto3_optional = true;\n    }\n\n    if (edition === \"proto3\") { // defaults to packed=true\n        if (!this.packed)\n            (descriptor.options || (descriptor.options = exports.FieldOptions.create())).packed = false;\n    } else if ((!edition || edition === \"proto2\") && this.packed) // defaults to packed=false\n        (descriptor.options || (descriptor.options = exports.FieldOptions.create())).packed = true;\n\n    return descriptor;\n};\n\n// --- Enum ---\n\n/**\n * Properties of an EnumDescriptorProto message.\n * @interface IEnumDescriptorProto\n * @property {string} [name] Enum name\n * @property {IEnumValueDescriptorProto[]} [value] Enum values\n * @property {IEnumOptions} [options] Enum options\n */\n\n/**\n * Properties of an EnumValueDescriptorProto message.\n * @interface IEnumValueDescriptorProto\n * @property {string} [name] Name\n * @property {number} [number] Value\n * @property {*} [options] Not supported\n */\n\n/**\n * Properties of an EnumOptions message.\n * @interface IEnumOptions\n * @property {boolean} [allowAlias] Whether aliases are allowed\n * @property {boolean} [deprecated]\n */\n\nvar unnamedEnumIndex = 0;\n\n/**\n * Creates an enum from a descriptor.\n *\n * Warning: this is not safe to use with editions protos, since it discards relevant file context.\n *\n * @param {IEnumDescriptorProto|Reader|Uint8Array} descriptor Descriptor\n * @param {string} [edition=\"proto2\"] The syntax or edition to use\n * @param {boolean} [nested=false] Whether or not this is a top-level object\n * @returns {Enum} Enum instance\n */\nEnum.fromDescriptor = function fromDescriptor(descriptor, edition, nested) {\n\n    // Decode the descriptor message if specified as a buffer:\n    if (typeof descriptor.length === \"number\")\n        descriptor = exports.EnumDescriptorProto.decode(descriptor);\n\n    // Construct values object\n    var values = {};\n    if (descriptor.value)\n        for (var i = 0; i < descriptor.value.length; ++i) {\n            var name  = descriptor.value[i].name,\n                value = descriptor.value[i].number || 0;\n            values[name && name.length ? name : \"NAME\" + value] = value;\n        }\n\n    var enm = new Enum(\n        descriptor.name && descriptor.name.length ? descriptor.name : \"Enum\" + unnamedEnumIndex++,\n        values,\n        fromDescriptorOptions(descriptor.options, exports.EnumOptions)\n    );\n\n    if (!nested)\n        enm._edition = edition;\n\n    return enm;\n};\n\n/**\n * Converts an enum to a descriptor.\n * @returns {Message<IEnumDescriptorProto>} Descriptor\n */\nEnum.prototype.toDescriptor = function toDescriptor() {\n\n    // Values\n    var values = [];\n    for (var i = 0, ks = Object.keys(this.values); i < ks.length; ++i)\n        values.push(exports.EnumValueDescriptorProto.create({ name: ks[i], number: this.values[ks[i]] }));\n\n    return exports.EnumDescriptorProto.create({\n        name: this.name,\n        value: values,\n        options: toDescriptorOptions(this.options, exports.EnumOptions)\n    });\n};\n\n// --- OneOf ---\n\n/**\n * Properties of a OneofDescriptorProto message.\n * @interface IOneofDescriptorProto\n * @property {string} [name] Oneof name\n * @property {*} [options] Not supported\n */\n\nvar unnamedOneofIndex = 0;\n\n/**\n * Creates a oneof from a descriptor.\n *\n * Warning: this is not safe to use with editions protos, since it discards relevant file context.\n *\n * @param {IOneofDescriptorProto|Reader|Uint8Array} descriptor Descriptor\n * @returns {OneOf} OneOf instance\n */\nOneOf.fromDescriptor = function fromDescriptor(descriptor) {\n\n    // Decode the descriptor message if specified as a buffer:\n    if (typeof descriptor.length === \"number\")\n        descriptor = exports.OneofDescriptorProto.decode(descriptor);\n\n    return new OneOf(\n        // unnamedOneOfIndex is global, not per type, because we have no ref to a type here\n        descriptor.name && descriptor.name.length ? descriptor.name : \"oneof\" + unnamedOneofIndex++\n        // fromDescriptorOptions(descriptor.options, exports.OneofOptions) - only uninterpreted_option\n    );\n};\n\n/**\n * Converts a oneof to a descriptor.\n * @returns {Message<IOneofDescriptorProto>} Descriptor\n */\nOneOf.prototype.toDescriptor = function toDescriptor() {\n    return exports.OneofDescriptorProto.create({\n        name: this.name\n        // options: toDescriptorOptions(this.options, exports.OneofOptions) - only uninterpreted_option\n    });\n};\n\n// --- Service ---\n\n/**\n * Properties of a ServiceDescriptorProto message.\n * @interface IServiceDescriptorProto\n * @property {string} [name] Service name\n * @property {IMethodDescriptorProto[]} [method] Methods\n * @property {IServiceOptions} [options] Options\n */\n\n/**\n * Properties of a ServiceOptions message.\n * @interface IServiceOptions\n * @property {boolean} [deprecated]\n */\n\nvar unnamedServiceIndex = 0;\n\n/**\n * Creates a service from a descriptor.\n *\n * Warning: this is not safe to use with editions protos, since it discards relevant file context.\n *\n * @param {IServiceDescriptorProto|Reader|Uint8Array} descriptor Descriptor\n * @param {string} [edition=\"proto2\"] The syntax or edition to use\n * @param {boolean} [nested=false] Whether or not this is a top-level object\n * @returns {Service} Service instance\n */\nService.fromDescriptor = function fromDescriptor(descriptor, edition, nested) {\n\n    // Decode the descriptor message if specified as a buffer:\n    if (typeof descriptor.length === \"number\")\n        descriptor = exports.ServiceDescriptorProto.decode(descriptor);\n\n    var service = new Service(descriptor.name && descriptor.name.length ? descriptor.name : \"Service\" + unnamedServiceIndex++, fromDescriptorOptions(descriptor.options, exports.ServiceOptions));\n    if (!nested)\n        service._edition = edition;\n    if (descriptor.method)\n        for (var i = 0; i < descriptor.method.length; ++i)\n            service.add(Method.fromDescriptor(descriptor.method[i]));\n\n    return service;\n};\n\n/**\n * Converts a service to a descriptor.\n * @returns {Message<IServiceDescriptorProto>} Descriptor\n */\nService.prototype.toDescriptor = function toDescriptor() {\n\n    // Methods\n    var methods = [];\n    for (var i = 0; i < this.methodsArray.length; ++i)\n        methods.push(this._methodsArray[i].toDescriptor());\n\n    return exports.ServiceDescriptorProto.create({\n        name: this.name,\n        method: methods,\n        options: toDescriptorOptions(this.options, exports.ServiceOptions)\n    });\n};\n\n// --- Method ---\n\n/**\n * Properties of a MethodDescriptorProto message.\n * @interface IMethodDescriptorProto\n * @property {string} [name] Method name\n * @property {string} [inputType] Request type name\n * @property {string} [outputType] Response type name\n * @property {IMethodOptions} [options] Not supported\n * @property {boolean} [clientStreaming=false] Whether requests are streamed\n * @property {boolean} [serverStreaming=false] Whether responses are streamed\n */\n\n/**\n * Properties of a MethodOptions message.\n *\n * Warning: this is not safe to use with editions protos, since it discards relevant file context.\n *\n * @interface IMethodOptions\n * @property {boolean} [deprecated]\n */\n\nvar unnamedMethodIndex = 0;\n\n/**\n * Creates a method from a descriptor.\n * @param {IMethodDescriptorProto|Reader|Uint8Array} descriptor Descriptor\n * @returns {Method} Reflected method instance\n */\nMethod.fromDescriptor = function fromDescriptor(descriptor) {\n\n    // Decode the descriptor message if specified as a buffer:\n    if (typeof descriptor.length === \"number\")\n        descriptor = exports.MethodDescriptorProto.decode(descriptor);\n\n    return new Method(\n        // unnamedMethodIndex is global, not per service, because we have no ref to a service here\n        descriptor.name && descriptor.name.length ? descriptor.name : \"Method\" + unnamedMethodIndex++,\n        \"rpc\",\n        descriptor.inputType,\n        descriptor.outputType,\n        Boolean(descriptor.clientStreaming),\n        Boolean(descriptor.serverStreaming),\n        fromDescriptorOptions(descriptor.options, exports.MethodOptions)\n    );\n};\n\n/**\n * Converts a method to a descriptor.\n * @returns {Message<IMethodDescriptorProto>} Descriptor\n */\nMethod.prototype.toDescriptor = function toDescriptor() {\n    return exports.MethodDescriptorProto.create({\n        name: this.name,\n        inputType: this.resolvedRequestType ? this.resolvedRequestType.fullName : this.requestType,\n        outputType: this.resolvedResponseType ? this.resolvedResponseType.fullName : this.responseType,\n        clientStreaming: this.requestStream,\n        serverStreaming: this.responseStream,\n        options: toDescriptorOptions(this.options, exports.MethodOptions)\n    });\n};\n\n// --- utility ---\n\n// Converts a descriptor type to a protobuf.js basic type\nfunction fromDescriptorType(type) {\n    switch (type) {\n        // 0 is reserved for errors\n        case 1: return \"double\";\n        case 2: return \"float\";\n        case 3: return \"int64\";\n        case 4: return \"uint64\";\n        case 5: return \"int32\";\n        case 6: return \"fixed64\";\n        case 7: return \"fixed32\";\n        case 8: return \"bool\";\n        case 9: return \"string\";\n        case 12: return \"bytes\";\n        case 13: return \"uint32\";\n        case 15: return \"sfixed32\";\n        case 16: return \"sfixed64\";\n        case 17: return \"sint32\";\n        case 18: return \"sint64\";\n    }\n    throw Error(\"illegal type: \" + type);\n}\n\n// Tests if a descriptor type is packable\nfunction packableDescriptorType(type) {\n    switch (type) {\n        case 1: // double\n        case 2: // float\n        case 3: // int64\n        case 4: // uint64\n        case 5: // int32\n        case 6: // fixed64\n        case 7: // fixed32\n        case 8: // bool\n        case 13: // uint32\n        case 14: // enum (!)\n        case 15: // sfixed32\n        case 16: // sfixed64\n        case 17: // sint32\n        case 18: // sint64\n            return true;\n    }\n    return false;\n}\n\n// Converts a protobuf.js basic type to a descriptor type\nfunction toDescriptorType(type, resolvedType, delimited) {\n    switch (type) {\n        // 0 is reserved for errors\n        case \"double\": return 1;\n        case \"float\": return 2;\n        case \"int64\": return 3;\n        case \"uint64\": return 4;\n        case \"int32\": return 5;\n        case \"fixed64\": return 6;\n        case \"fixed32\": return 7;\n        case \"bool\": return 8;\n        case \"string\": return 9;\n        case \"bytes\": return 12;\n        case \"uint32\": return 13;\n        case \"sfixed32\": return 15;\n        case \"sfixed64\": return 16;\n        case \"sint32\": return 17;\n        case \"sint64\": return 18;\n    }\n    if (resolvedType instanceof Enum)\n        return 14;\n    if (resolvedType instanceof Type)\n        return delimited ? 10 : 11;\n    throw Error(\"illegal type: \" + type);\n}\n\nfunction fromDescriptorOptionsRecursive(obj, type) {\n    var val = {};\n    for (var i = 0, field, key; i < type.fieldsArray.length; ++i) {\n        if ((key = (field = type._fieldsArray[i]).name) === \"uninterpretedOption\") continue;\n        if (!Object.prototype.hasOwnProperty.call(obj, key)) continue;\n\n        var newKey = underScore(key);\n        if (field.resolvedType instanceof Type) {\n            val[newKey] = fromDescriptorOptionsRecursive(obj[key], field.resolvedType);\n        } else if(field.resolvedType instanceof Enum) {\n            val[newKey] = field.resolvedType.valuesById[obj[key]];\n        } else {\n            val[newKey] = obj[key];\n        }\n    }\n    return val;\n}\n\n// Converts descriptor options to an options object\nfunction fromDescriptorOptions(options, type) {\n    if (!options)\n        return undefined;\n    return fromDescriptorOptionsRecursive(type.toObject(options), type);\n}\n\nfunction toDescriptorOptionsRecursive(obj, type) {\n    var val = {};\n    var keys = Object.keys(obj);\n    for (var i = 0; i < keys.length; ++i) {\n        var key = keys[i];\n        var newKey = $protobuf.util.camelCase(key);\n        if (!Object.prototype.hasOwnProperty.call(type.fields, newKey)) continue;\n        var field = type.fields[newKey];\n        if (field.resolvedType instanceof Type) {\n            val[newKey] = toDescriptorOptionsRecursive(obj[key], field.resolvedType);\n        } else {\n            val[newKey] = obj[key];\n        }\n        if (field.repeated && !Array.isArray(val[newKey])) {\n            val[newKey] = [val[newKey]];\n        }\n    }\n    return val;\n}\n\n// Converts an options object to descriptor options\nfunction toDescriptorOptions(options, type) {\n    if (!options)\n        return undefined;\n    return type.fromObject(toDescriptorOptionsRecursive(options, type));\n}\n\n// Calculates the shortest relative path from `from` to `to`.\nfunction shortname(from, to) {\n    var fromPath = from.fullName.split(\".\"),\n        toPath = to.fullName.split(\".\"),\n        i = 0,\n        j = 0,\n        k = toPath.length - 1;\n    if (!(from instanceof Root) && to instanceof Namespace)\n        while (i < fromPath.length && j < k && fromPath[i] === toPath[j]) {\n            var other = to.lookup(fromPath[i++], true);\n            if (other !== null && other !== to)\n                break;\n            ++j;\n        }\n    else\n        for (; i < fromPath.length && j < k && fromPath[i] === toPath[j]; ++i, ++j);\n    return toPath.slice(j).join(\".\");\n}\n\n// copied here from cli/targets/proto.js\nfunction underScore(str) {\n    return str.substring(0,1)\n         + str.substring(1)\n               .replace(/([A-Z])(?=[a-z]|$)/g, function($0, $1) { return \"_\" + $1.toLowerCase(); });\n}\n\nfunction editionFromDescriptor(fileDescriptor) {\n    if (fileDescriptor.syntax === \"editions\") {\n        switch(fileDescriptor.edition) {\n            case exports.Edition.EDITION_2023:\n                return \"2023\";\n            default:\n                throw new Error(\"Unsupported edition \" + fileDescriptor.edition);\n        }\n    }\n    if (fileDescriptor.syntax === \"proto3\") {\n        return \"proto3\";\n    }\n    return \"proto2\";\n}\n\nfunction editionToDescriptor(edition, fileDescriptor) {\n    if (!edition) return;\n    if (edition === \"proto2\" || edition === \"proto3\") {\n        fileDescriptor.syntax = edition;\n    } else {\n        fileDescriptor.syntax = \"editions\";\n        switch(edition) {\n            case \"2023\":\n                fileDescriptor.edition = exports.Edition.EDITION_2023;\n                break;\n            default:\n                throw new Error(\"Unsupported edition \" + edition);\n        }\n    }\n}\n\n// --- exports ---\n\n/**\n * Reflected file descriptor set.\n * @name FileDescriptorSet\n * @type {Type}\n * @const\n * @tstype $protobuf.Type\n */\n\n/**\n * Reflected file descriptor proto.\n * @name FileDescriptorProto\n * @type {Type}\n * @const\n * @tstype $protobuf.Type\n */\n\n/**\n * Reflected descriptor proto.\n * @name DescriptorProto\n * @type {Type}\n * @property {Type} ExtensionRange\n * @property {Type} ReservedRange\n * @const\n * @tstype $protobuf.Type & {\n *     ExtensionRange: $protobuf.Type,\n *     ReservedRange: $protobuf.Type\n * }\n */\n\n/**\n * Reflected field descriptor proto.\n * @name FieldDescriptorProto\n * @type {Type}\n * @property {Enum} Label\n * @property {Enum} Type\n * @const\n * @tstype $protobuf.Type & {\n *     Label: $protobuf.Enum,\n *     Type: $protobuf.Enum\n * }\n */\n\n/**\n * Reflected oneof descriptor proto.\n * @name OneofDescriptorProto\n * @type {Type}\n * @const\n * @tstype $protobuf.Type\n */\n\n/**\n * Reflected enum descriptor proto.\n * @name EnumDescriptorProto\n * @type {Type}\n * @const\n * @tstype $protobuf.Type\n */\n\n/**\n * Reflected service descriptor proto.\n * @name ServiceDescriptorProto\n * @type {Type}\n * @const\n * @tstype $protobuf.Type\n */\n\n/**\n * Reflected enum value descriptor proto.\n * @name EnumValueDescriptorProto\n * @type {Type}\n * @const\n * @tstype $protobuf.Type\n */\n\n/**\n * Reflected method descriptor proto.\n * @name MethodDescriptorProto\n * @type {Type}\n * @const\n * @tstype $protobuf.Type\n */\n\n/**\n * Reflected file options.\n * @name FileOptions\n * @type {Type}\n * @property {Enum} OptimizeMode\n * @const\n * @tstype $protobuf.Type & {\n *     OptimizeMode: $protobuf.Enum\n * }\n */\n\n/**\n * Reflected message options.\n * @name MessageOptions\n * @type {Type}\n * @const\n * @tstype $protobuf.Type\n */\n\n/**\n * Reflected field options.\n * @name FieldOptions\n * @type {Type}\n * @property {Enum} CType\n * @property {Enum} JSType\n * @const\n * @tstype $protobuf.Type & {\n *     CType: $protobuf.Enum,\n *     JSType: $protobuf.Enum\n * }\n */\n\n/**\n * Reflected oneof options.\n * @name OneofOptions\n * @type {Type}\n * @const\n * @tstype $protobuf.Type\n */\n\n/**\n * Reflected enum options.\n * @name EnumOptions\n * @type {Type}\n * @const\n * @tstype $protobuf.Type\n */\n\n/**\n * Reflected enum value options.\n * @name EnumValueOptions\n * @type {Type}\n * @const\n * @tstype $protobuf.Type\n */\n\n/**\n * Reflected service options.\n * @name ServiceOptions\n * @type {Type}\n * @const\n * @tstype $protobuf.Type\n */\n\n/**\n * Reflected method options.\n * @name MethodOptions\n * @type {Type}\n * @const\n * @tstype $protobuf.Type\n */\n\n/**\n * Reflected uninterpretet option.\n * @name UninterpretedOption\n * @type {Type}\n * @property {Type} NamePart\n * @const\n * @tstype $protobuf.Type & {\n *     NamePart: $protobuf.Type\n * }\n */\n\n/**\n * Reflected source code info.\n * @name SourceCodeInfo\n * @type {Type}\n * @property {Type} Location\n * @const\n * @tstype $protobuf.Type & {\n *     Location: $protobuf.Type\n * }\n */\n\n/**\n * Reflected generated code info.\n * @name GeneratedCodeInfo\n * @type {Type}\n * @property {Type} Annotation\n * @const\n * @tstype $protobuf.Type & {\n *     Annotation: $protobuf.Type\n * }\n */\n"], "names": [], "mappings": "AAAA;AACA,IAAI;AACJ,OAAO,OAAO,GAAG,UAAU,UAAU,UAAU,GAAG,UAAU,IAAI,CAAC,QAAQ,sGAAmD,MAAM,CAAC;AAEnI,IAAI,YAAY,UAAU,SAAS,EAC/B,OAAY,UAAU,IAAI,EAC1B,OAAY,UAAU,IAAI,EAC1B,OAAY,UAAU,IAAI,EAC1B,QAAY,UAAU,KAAK,EAC3B,WAAY,UAAU,QAAQ,EAC9B,QAAY,UAAU,KAAK,EAC3B,UAAY,UAAU,OAAO,EAC7B,SAAY,UAAU,MAAM;AAEhC,eAAe;AAEf;;;;CAIC,GAED;;;;;;;;;;;;;;;;CAgBC,GAED;;;;;;;;;;;;;;;;CAgBC,GAED;;;;;;;;;;;;;;;;;CAiBC,GAED;;;;;;;CAOC,GAED;;;;CAIC,GACD,KAAK,cAAc,GAAG,SAAS,eAAe,UAAU;IAEpD,0DAA0D;IAC1D,IAAI,OAAO,WAAW,MAAM,KAAK,UAC7B,aAAa,QAAQ,iBAAiB,CAAC,MAAM,CAAC;IAElD,IAAI,OAAO,IAAI;IAEf,IAAI,WAAW,IAAI,EAAE;QACjB,IAAI,gBACA;QACJ,IAAK,IAAI,IAAI,GAAG,GAAG,IAAI,WAAW,IAAI,CAAC,MAAM,EAAE,EAAE,EAAG;YAChD,cAAc;YACd,IAAI,CAAC,iBAAiB,WAAW,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,IAAI,cAAc,CAAC,UAAU,CAAC,MAAM,EACpF,cAAc,KAAK,MAAM,CAAC,cAAc,CAAC,UAAU;YACvD,IAAI,UAAU,sBAAsB;YACpC,IAAI,eAAe,IAAI,IAAI,eAAe,IAAI,CAAC,MAAM,EACjD,KAAK,KAAK,CAAC,IAAI,CAAC,YAAY,QAAQ,GAAG,eAAe,IAAI;YAC9D,IAAI,eAAe,WAAW,EAC1B,IAAK,IAAI,GAAG,IAAI,eAAe,WAAW,CAAC,MAAM,EAAE,EAAE,EACjD,YAAY,GAAG,CAAC,KAAK,cAAc,CAAC,eAAe,WAAW,CAAC,EAAE,EAAE;YAC3E,IAAI,eAAe,QAAQ,EACvB,IAAK,IAAI,GAAG,IAAI,eAAe,QAAQ,CAAC,MAAM,EAAE,EAAE,EAC9C,YAAY,GAAG,CAAC,KAAK,cAAc,CAAC,eAAe,QAAQ,CAAC,EAAE,EAAE;YACxE,IAAI,eAAe,SAAS,EACxB,IAAK,IAAI,GAAG,IAAI,eAAe,SAAS,CAAC,MAAM,EAAE,EAAE,EAC/C,YAAY,GAAG,CAAC,MAAM,cAAc,CAAC,eAAe,SAAS,CAAC,EAAE,EAAE;YAC1E,IAAI,eAAe,OAAO,EACtB,IAAK,IAAI,GAAG,IAAI,eAAe,OAAO,CAAC,MAAM,EAAE,EAAE,EAC7C,YAAY,GAAG,CAAC,QAAQ,cAAc,CAAC,eAAe,OAAO,CAAC,EAAE,EAAE;YAC1E,IAAI,OAAO,sBAAsB,eAAe,OAAO,EAAE,QAAQ,WAAW;YAC5E,IAAI,MAAM;gBACN,IAAI,KAAK,OAAO,IAAI,CAAC;gBACrB,IAAK,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,EAAE,EACzB,YAAY,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;YAChD;QACJ;IACJ;IAEA,OAAO,KAAK,UAAU;AAC1B;AAEA;;;;CAIC,GACD,KAAK,SAAS,CAAC,YAAY,GAAG,SAAS,aAAa,OAAO;IACvD,IAAI,MAAM,QAAQ,iBAAiB,CAAC,MAAM;IAC1C,2BAA2B,IAAI,EAAE,IAAI,IAAI,EAAE;IAC3C,OAAO;AACX;AAEA,yDAAyD;AACzD,SAAS,2BAA2B,EAAE,EAAE,KAAK,EAAE,OAAO;IAElD,oBAAoB;IACpB,IAAI,OAAO,QAAQ,mBAAmB,CAAC,MAAM,CAAC;QAAE,MAAM,GAAG,QAAQ,IAAI,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,OAAO,QAAQ,MAAM,IAAI;IAAS;IACzI,oBAAoB,SAAS;IAC7B,IAAI,CAAC,CAAC,cAAc,IAAI,GACpB,IAAI,CAAC,UAAU,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC;IAE5C,mBAAmB;IACnB,IAAK,IAAI,IAAI,GAAG,QAAQ,IAAI,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,EACjD,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,EAAE,aAAa,MACzC,KAAK,WAAW,CAAC,IAAI,CAAC,OAAO,YAAY,CAAC;SACzC,IAAI,kBAAkB,MACvB,KAAK,QAAQ,CAAC,IAAI,CAAC,OAAO,YAAY;SACrC,IAAI,kBAAkB,OACvB,KAAK,SAAS,CAAC,IAAI,CAAC,OAAO,YAAY,CAAC;SACvC,IAAI,kBAAkB,SACvB,KAAK,OAAO,CAAC,IAAI,CAAC,OAAO,YAAY;SACpC,IAAI,kBAAkB,SAAS,GAAG,WACnC,2BAA2B,QAAQ,OAAO,UAAU,oBAAoB;IAEhF,6BAA6B;IAC7B,KAAK,OAAO,GAAG,oBAAoB,GAAG,OAAO,EAAE,QAAQ,WAAW;IAElE,gEAAgE;IAChE,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,KAAK,QAAQ,CAAC,MAAM,GAAG,KAAK,SAAS,CAAC,MAAM,GAAG,KAAK,OAAO,CAAC,MAAM,EAC5F,MAAM,IAAI,CAAC;AACnB;AAEA,eAAe;AAEf;;;;;;;;;;;;;CAaC,GAED;;;;CAIC,GAED;;;;;CAKC,GAED;;;;;CAKC,GAED,IAAI,sBAAsB;AAE1B;;;;;;;;;CASC,GACD,KAAK,cAAc,GAAG,SAAS,eAAe,UAAU,EAAE,OAAO,EAAE,MAAM;IACrE,0DAA0D;IAC1D,IAAI,OAAO,WAAW,MAAM,KAAK,UAC7B,aAAa,QAAQ,eAAe,CAAC,MAAM,CAAC;IAEhD,0BAA0B;IAC1B,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,CAAC,MAAM,GAAG,WAAW,IAAI,GAAG,SAAS,uBAAuB,sBAAsB,WAAW,OAAO,EAAE,QAAQ,cAAc,IAC3J;IAEJ,IAAI,CAAC,QACD,KAAK,QAAQ,GAAG;IAEpB,UAAU,GAAG,IAAI,WAAW,SAAS,EACjC,IAAK,IAAI,GAAG,IAAI,WAAW,SAAS,CAAC,MAAM,EAAE,EAAE,EAC3C,KAAK,GAAG,CAAC,MAAM,cAAc,CAAC,WAAW,SAAS,CAAC,EAAE;IAC7D,UAAU,GAAG,IAAI,WAAW,KAAK,EAC7B,IAAK,IAAI,GAAG,IAAI,WAAW,KAAK,CAAC,MAAM,EAAE,EAAE,EAAG;QAC1C,IAAI,QAAQ,MAAM,cAAc,CAAC,WAAW,KAAK,CAAC,EAAE,EAAE,SAAS;QAC/D,KAAK,GAAG,CAAC;QACT,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,eACnC,KAAK,WAAW,CAAC,WAAW,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC;IAC7D;IACJ,oBAAoB,GAAG,IAAI,WAAW,SAAS,EAC3C,IAAK,IAAI,GAAG,IAAI,WAAW,SAAS,CAAC,MAAM,EAAE,EAAE,EAC3C,KAAK,GAAG,CAAC,MAAM,cAAc,CAAC,WAAW,SAAS,CAAC,EAAE,EAAE,SAAS;IACxE,gBAAgB,GAAG,IAAI,WAAW,UAAU,EACxC,IAAK,IAAI,GAAG,IAAI,WAAW,UAAU,CAAC,MAAM,EAAE,EAAE,EAAG;QAC/C,KAAK,GAAG,CAAC,KAAK,cAAc,CAAC,WAAW,UAAU,CAAC,EAAE,EAAE,SAAS;QAChE,IAAI,WAAW,UAAU,CAAC,EAAE,CAAC,OAAO,IAAI,WAAW,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,EAC7E,KAAK,SAAS,CAAC,aAAa;IACpC;IACJ,gBAAgB,GAAG,IAAI,WAAW,QAAQ,EACtC,IAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,CAAC,MAAM,EAAE,EAAE,EAC1C,KAAK,GAAG,CAAC,KAAK,cAAc,CAAC,WAAW,QAAQ,CAAC,EAAE,EAAE,SAAS;IACtE,oBAAoB,GAAG,IAAI,WAAW,cAAc,IAAI,WAAW,cAAc,CAAC,MAAM,EAAE;QACtF,KAAK,UAAU,GAAG,EAAE;QACpB,IAAK,IAAI,GAAG,IAAI,WAAW,cAAc,CAAC,MAAM,EAAE,EAAE,EAChD,KAAK,UAAU,CAAC,IAAI,CAAC;YAAE,WAAW,cAAc,CAAC,EAAE,CAAC,KAAK;YAAE,WAAW,cAAc,CAAC,EAAE,CAAC,GAAG;SAAE;IACrG;IACA,eAAe,GAAG,IAAI,WAAW,aAAa,IAAI,WAAW,aAAa,CAAC,MAAM,IAAI,WAAW,YAAY,IAAI,WAAW,YAAY,CAAC,MAAM,EAAE;QAC5I,KAAK,QAAQ,GAAG,EAAE;QAClB,UAAU,GAAG,IAAI,WAAW,aAAa,EACrC,IAAK,IAAI,GAAG,IAAI,WAAW,aAAa,CAAC,MAAM,EAAE,EAAE,EAC/C,KAAK,QAAQ,CAAC,IAAI,CAAC;YAAE,WAAW,aAAa,CAAC,EAAE,CAAC,KAAK;YAAE,WAAW,aAAa,CAAC,EAAE,CAAC,GAAG;SAAE;QACjG,SAAS,GAAG,IAAI,WAAW,YAAY,EACnC,IAAK,IAAI,GAAG,IAAI,WAAW,YAAY,CAAC,MAAM,EAAE,EAAE,EAC9C,KAAK,QAAQ,CAAC,IAAI,CAAC,WAAW,YAAY,CAAC,EAAE;IACzD;IAEA,OAAO;AACX;AAEA;;;;CAIC,GACD,KAAK,SAAS,CAAC,YAAY,GAAG,SAAS,aAAa,OAAO;IACvD,IAAI,aAAa,QAAQ,eAAe,CAAC,MAAM,CAAC;QAAE,MAAM,IAAI,CAAC,IAAI;IAAC,IAC9D;IAEJ,UAAU,GAAG,IAAK,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,EAAG;QACvD,IAAI;QACJ,WAAW,KAAK,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC;QAC1E,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,YAAY,UAAU;YAC1C,IAAI,UAAU,iBAAiB,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,eAAe,EAAE,QAC/F,YAAY,iBAAiB,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,EAAE,QAC3F,gBAAgB,cAAc,QAAQ,GAAG,MAAM,cAAc,QAAQ,GAAG,KAClE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,IAAI,UAAU,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,GAC3H;YACV,WAAW,UAAU,CAAC,IAAI,CAAC,QAAQ,eAAe,CAAC,MAAM,CAAC;gBACtD,MAAM,gBAAgB,QAAQ;gBAC9B,OAAO;oBACH,QAAQ,oBAAoB,CAAC,MAAM,CAAC;wBAAE,MAAM;wBAAO,QAAQ;wBAAG,OAAO;wBAAG,MAAM;oBAAQ;oBACtF,QAAQ,oBAAoB,CAAC,MAAM,CAAC;wBAAE,MAAM;wBAAS,QAAQ;wBAAG,OAAO;wBAAG,MAAM;wBAAW,UAAU;oBAAc;iBACtH;gBACD,SAAS,QAAQ,cAAc,CAAC,MAAM,CAAC;oBAAE,UAAU;gBAAK;YAC5D;QACJ;IACJ;IACA,UAAU,GAAG,IAAK,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,EACpD,WAAW,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY;IAC/D,aAAa,GAAG,IAAK,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,EAAG;QAC1D,oBAAoB,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,YAAY,OACvD,WAAW,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC;aAC3C,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,YAAY,MACjD,WAAW,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC;aAChD,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,YAAY,MACjD,WAAW,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY;IAC9D,uEAAuE;IAC3E;IACA,oBAAoB,GAAG,IAAI,IAAI,CAAC,UAAU,EACtC,IAAK,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,EACtC,WAAW,cAAc,CAAC,IAAI,CAAC,QAAQ,eAAe,CAAC,cAAc,CAAC,MAAM,CAAC;QAAE,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;QAAE,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;IAAC;IAChJ,eAAe,GAAG,IAAI,IAAI,CAAC,QAAQ,EAC/B,IAAK,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,EACpC,SAAS,GAAG,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,UACxC,WAAW,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;SAE7C,WAAW,aAAa,CAAC,IAAI,CAAC,QAAQ,eAAe,CAAC,aAAa,CAAC,MAAM,CAAC;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;QAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;IAAC;IAE9I,WAAW,OAAO,GAAG,oBAAoB,IAAI,CAAC,OAAO,EAAE,QAAQ,cAAc;IAE7E,OAAO;AACX;AAEA,gBAAgB;AAEhB;;;;;;;;;;;;;CAaC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;;;;;;CAsBC,GAED;;;;;CAKC,GAED;;;;;;;CAOC,GAED,4BAA4B;AAC5B,IAAI,WAAW;AAEf;;;;;;;;;CASC,GACD,MAAM,cAAc,GAAG,SAAS,eAAe,UAAU,EAAE,OAAO,EAAE,MAAM;IAEtE,0DAA0D;IAC1D,IAAI,OAAO,WAAW,MAAM,KAAK,UAC7B,aAAa,QAAQ,eAAe,CAAC,MAAM,CAAC;IAEhD,IAAI,OAAO,WAAW,MAAM,KAAK,UAC7B,MAAM,MAAM;IAEhB,oBAAoB;IACpB,IAAI;IACJ,IAAI,WAAW,QAAQ,IAAI,WAAW,QAAQ,CAAC,MAAM,EACjD,YAAY,WAAW,QAAQ;SAE/B,YAAY,mBAAmB,WAAW,IAAI;IAElD,oBAAoB;IACpB,IAAI;IACJ,OAAQ,WAAW,KAAK;QACpB,2BAA2B;QAC3B,KAAK;YAAG,YAAY;YAAW;QAC/B,KAAK;YAAG,YAAY;YAAY;QAChC,KAAK;YAAG,YAAY;YAAY;QAChC;YAAS,MAAM,MAAM,oBAAoB,WAAW,KAAK;IAC7D;IAEH,IAAI,WAAW,WAAW,QAAQ;IAClC,IAAI,WAAW,QAAQ,KAAK,WAAW;QACtC,WAAW,SAAS,MAAM,GAAG,WAAW;IACzC;IACG,IAAI,QAAQ,IAAI,MACZ,WAAW,IAAI,CAAC,MAAM,GAAG,WAAW,IAAI,GAAG,UAAU,WAAW,MAAM,EACtE,WAAW,MAAM,EACjB,WACA,WACA;IAGJ,IAAI,CAAC,QACD,MAAM,QAAQ,GAAG;IAErB,MAAM,OAAO,GAAG,sBAAsB,WAAW,OAAO,EAAE,QAAQ,YAAY;IAC9E,IAAI,WAAW,eAAe,EAC1B,MAAM,OAAO,CAAC,eAAe,GAAG;IAEpC,IAAI,WAAW,YAAY,IAAI,WAAW,YAAY,CAAC,MAAM,EAAE;QAC3D,IAAI,eAAe,WAAW,YAAY;QAC1C,OAAQ;YACJ,KAAK;YAAQ,KAAK;gBACd,eAAe;gBACf;YACJ,KAAK;YAAS,KAAK;gBACf,eAAe;gBACf;YACJ;gBACI,IAAI,QAAQ,SAAS,IAAI,CAAC;gBAC1B,IAAI,OACA,eAAe,SAAS,eAAe,4BAA4B;gBACvE;QACR;QACA,MAAM,SAAS,CAAC,WAAW;IAC/B;IAEA,IAAI,uBAAuB,WAAW,IAAI,GAAG;QACzC,IAAI,YAAY,UAAU;YACtB,IAAI,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,MAAM,EAChD,MAAM,SAAS,CAAC,UAAU;QAClC,OAAO,IAAI,CAAC,CAAC,WAAW,YAAY,QAAQ,KAAK,WAAW,OAAO,IAAI,WAAW,OAAO,CAAC,MAAM,EAC5F,MAAM,SAAS,CAAC,UAAU;IAClC;IAEA,OAAO;AACX;AAEA;;;;CAIC,GACD,MAAM,SAAS,CAAC,YAAY,GAAG,SAAS,aAAa,OAAO;IACxD,IAAI,aAAa,QAAQ,oBAAoB,CAAC,MAAM,CAAC;QAAE,MAAM,IAAI,CAAC,IAAI;QAAE,QAAQ,IAAI,CAAC,EAAE;IAAC;IAExF,IAAI,IAAI,CAAC,GAAG,EAAE;QAEV,WAAW,IAAI,GAAG,IAAI,UAAU;QAChC,WAAW,QAAQ,GAAG,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,2DAA2D;QACpH,WAAW,KAAK,GAAG,GAAG,WAAW;IAErC,OAAO;QAEH,oBAAoB;QACpB,OAAQ,WAAW,IAAI,GAAG,iBAAiB,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,GAAG,YAAY,EAAE,IAAI,CAAC,SAAS;YAC7F,KAAK;YACL,KAAK;YACL,KAAK;gBACD,WAAW,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,UAAU,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,IAAI;gBAC/F;QACR;QAEA,oBAAoB;QACpB,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY;YAC1B,WAAW,KAAK,GAAG;QACvB,OAAO,IAAI,IAAI,CAAC,QAAQ,IAAI,YAAY,UAAU;YAC9C,WAAW,KAAK,GAAG;QACvB,OAAO;YACH,WAAW,KAAK,GAAG;QACvB;IACJ;IAEA,yBAAyB;IACzB,WAAW,QAAQ,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM;IAE7F,uBAAuB;IACvB,IAAI,IAAI,CAAC,MAAM,EACX;QAAA,IAAI,CAAC,WAAW,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,GACzE,MAAM,MAAM;IAAgB;IAEpC,IAAI,IAAI,CAAC,OAAO,EAAE;QACd,WAAW,OAAO,GAAG,oBAAoB,IAAI,CAAC,OAAO,EAAE,QAAQ,YAAY;QAC3E,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,MAC3B,WAAW,YAAY,GAAG,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU;QAC5D,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAC5B,WAAW,eAAe,GAAG;IACrC;IAEA,IAAI,YAAY,UAAU;QACtB,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,CAAC,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,GAAG,QAAQ,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG;IAC9F,OAAO,IAAI,CAAC,CAAC,WAAW,YAAY,QAAQ,KAAK,IAAI,CAAC,MAAM,EACxD,CAAC,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,GAAG,QAAQ,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG;IAE1F,OAAO;AACX;AAEA,eAAe;AAEf;;;;;;CAMC,GAED;;;;;;CAMC,GAED;;;;;CAKC,GAED,IAAI,mBAAmB;AAEvB;;;;;;;;;CASC,GACD,KAAK,cAAc,GAAG,SAAS,eAAe,UAAU,EAAE,OAAO,EAAE,MAAM;IAErE,0DAA0D;IAC1D,IAAI,OAAO,WAAW,MAAM,KAAK,UAC7B,aAAa,QAAQ,mBAAmB,CAAC,MAAM,CAAC;IAEpD,0BAA0B;IAC1B,IAAI,SAAS,CAAC;IACd,IAAI,WAAW,KAAK,EAChB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,KAAK,CAAC,MAAM,EAAE,EAAE,EAAG;QAC9C,IAAI,OAAQ,WAAW,KAAK,CAAC,EAAE,CAAC,IAAI,EAChC,QAAQ,WAAW,KAAK,CAAC,EAAE,CAAC,MAAM,IAAI;QAC1C,MAAM,CAAC,QAAQ,KAAK,MAAM,GAAG,OAAO,SAAS,MAAM,GAAG;IAC1D;IAEJ,IAAI,MAAM,IAAI,KACV,WAAW,IAAI,IAAI,WAAW,IAAI,CAAC,MAAM,GAAG,WAAW,IAAI,GAAG,SAAS,oBACvE,QACA,sBAAsB,WAAW,OAAO,EAAE,QAAQ,WAAW;IAGjE,IAAI,CAAC,QACD,IAAI,QAAQ,GAAG;IAEnB,OAAO;AACX;AAEA;;;CAGC,GACD,KAAK,SAAS,CAAC,YAAY,GAAG,SAAS;IAEnC,SAAS;IACT,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,IAAI,GAAG,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,MAAM,EAAE,EAAE,EAC5D,OAAO,IAAI,CAAC,QAAQ,wBAAwB,CAAC,MAAM,CAAC;QAAE,MAAM,EAAE,CAAC,EAAE;QAAE,QAAQ,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;IAAC;IAElG,OAAO,QAAQ,mBAAmB,CAAC,MAAM,CAAC;QACtC,MAAM,IAAI,CAAC,IAAI;QACf,OAAO;QACP,SAAS,oBAAoB,IAAI,CAAC,OAAO,EAAE,QAAQ,WAAW;IAClE;AACJ;AAEA,gBAAgB;AAEhB;;;;;CAKC,GAED,IAAI,oBAAoB;AAExB;;;;;;;CAOC,GACD,MAAM,cAAc,GAAG,SAAS,eAAe,UAAU;IAErD,0DAA0D;IAC1D,IAAI,OAAO,WAAW,MAAM,KAAK,UAC7B,aAAa,QAAQ,oBAAoB,CAAC,MAAM,CAAC;IAErD,OAAO,IAAI,MACP,mFAAmF;IACnF,WAAW,IAAI,IAAI,WAAW,IAAI,CAAC,MAAM,GAAG,WAAW,IAAI,GAAG,UAAU;AAGhF;AAEA;;;CAGC,GACD,MAAM,SAAS,CAAC,YAAY,GAAG,SAAS;IACpC,OAAO,QAAQ,oBAAoB,CAAC,MAAM,CAAC;QACvC,MAAM,IAAI,CAAC,IAAI;IAEnB;AACJ;AAEA,kBAAkB;AAElB;;;;;;CAMC,GAED;;;;CAIC,GAED,IAAI,sBAAsB;AAE1B;;;;;;;;;CASC,GACD,QAAQ,cAAc,GAAG,SAAS,eAAe,UAAU,EAAE,OAAO,EAAE,MAAM;IAExE,0DAA0D;IAC1D,IAAI,OAAO,WAAW,MAAM,KAAK,UAC7B,aAAa,QAAQ,sBAAsB,CAAC,MAAM,CAAC;IAEvD,IAAI,UAAU,IAAI,QAAQ,WAAW,IAAI,IAAI,WAAW,IAAI,CAAC,MAAM,GAAG,WAAW,IAAI,GAAG,YAAY,uBAAuB,sBAAsB,WAAW,OAAO,EAAE,QAAQ,cAAc;IAC3L,IAAI,CAAC,QACD,QAAQ,QAAQ,GAAG;IACvB,IAAI,WAAW,MAAM,EACjB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,CAAC,MAAM,EAAE,EAAE,EAC5C,QAAQ,GAAG,CAAC,OAAO,cAAc,CAAC,WAAW,MAAM,CAAC,EAAE;IAE9D,OAAO;AACX;AAEA;;;CAGC,GACD,QAAQ,SAAS,CAAC,YAAY,GAAG,SAAS;IAEtC,UAAU;IACV,IAAI,UAAU,EAAE;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,EAC5C,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,YAAY;IAEnD,OAAO,QAAQ,sBAAsB,CAAC,MAAM,CAAC;QACzC,MAAM,IAAI,CAAC,IAAI;QACf,QAAQ;QACR,SAAS,oBAAoB,IAAI,CAAC,OAAO,EAAE,QAAQ,cAAc;IACrE;AACJ;AAEA,iBAAiB;AAEjB;;;;;;;;;CASC,GAED;;;;;;;CAOC,GAED,IAAI,qBAAqB;AAEzB;;;;CAIC,GACD,OAAO,cAAc,GAAG,SAAS,eAAe,UAAU;IAEtD,0DAA0D;IAC1D,IAAI,OAAO,WAAW,MAAM,KAAK,UAC7B,aAAa,QAAQ,qBAAqB,CAAC,MAAM,CAAC;IAEtD,OAAO,IAAI,OACP,0FAA0F;IAC1F,WAAW,IAAI,IAAI,WAAW,IAAI,CAAC,MAAM,GAAG,WAAW,IAAI,GAAG,WAAW,sBACzE,OACA,WAAW,SAAS,EACpB,WAAW,UAAU,EACrB,QAAQ,WAAW,eAAe,GAClC,QAAQ,WAAW,eAAe,GAClC,sBAAsB,WAAW,OAAO,EAAE,QAAQ,aAAa;AAEvE;AAEA;;;CAGC,GACD,OAAO,SAAS,CAAC,YAAY,GAAG,SAAS;IACrC,OAAO,QAAQ,qBAAqB,CAAC,MAAM,CAAC;QACxC,MAAM,IAAI,CAAC,IAAI;QACf,WAAW,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW;QAC1F,YAAY,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY;QAC9F,iBAAiB,IAAI,CAAC,aAAa;QACnC,iBAAiB,IAAI,CAAC,cAAc;QACpC,SAAS,oBAAoB,IAAI,CAAC,OAAO,EAAE,QAAQ,aAAa;IACpE;AACJ;AAEA,kBAAkB;AAElB,yDAAyD;AACzD,SAAS,mBAAmB,IAAI;IAC5B,OAAQ;QACJ,2BAA2B;QAC3B,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf,KAAK;YAAI,OAAO;QAChB,KAAK;YAAI,OAAO;QAChB,KAAK;YAAI,OAAO;QAChB,KAAK;YAAI,OAAO;QAChB,KAAK;YAAI,OAAO;QAChB,KAAK;YAAI,OAAO;IACpB;IACA,MAAM,MAAM,mBAAmB;AACnC;AAEA,yCAAyC;AACzC,SAAS,uBAAuB,IAAI;IAChC,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;IACf;IACA,OAAO;AACX;AAEA,yDAAyD;AACzD,SAAS,iBAAiB,IAAI,EAAE,YAAY,EAAE,SAAS;IACnD,OAAQ;QACJ,2BAA2B;QAC3B,KAAK;YAAU,OAAO;QACtB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAU,OAAO;QACtB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAQ,OAAO;QACpB,KAAK;YAAU,OAAO;QACtB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAU,OAAO;QACtB,KAAK;YAAY,OAAO;QACxB,KAAK;YAAY,OAAO;QACxB,KAAK;YAAU,OAAO;QACtB,KAAK;YAAU,OAAO;IAC1B;IACA,IAAI,wBAAwB,MACxB,OAAO;IACX,IAAI,wBAAwB,MACxB,OAAO,YAAY,KAAK;IAC5B,MAAM,MAAM,mBAAmB;AACnC;AAEA,SAAS,+BAA+B,GAAG,EAAE,IAAI;IAC7C,IAAI,MAAM,CAAC;IACX,IAAK,IAAI,IAAI,GAAG,OAAO,KAAK,IAAI,KAAK,WAAW,CAAC,MAAM,EAAE,EAAE,EAAG;QAC1D,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,YAAY,CAAC,EAAE,EAAE,IAAI,MAAM,uBAAuB;QAC3E,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;QAErD,IAAI,SAAS,WAAW;QACxB,IAAI,MAAM,YAAY,YAAY,MAAM;YACpC,GAAG,CAAC,OAAO,GAAG,+BAA+B,GAAG,CAAC,IAAI,EAAE,MAAM,YAAY;QAC7E,OAAO,IAAG,MAAM,YAAY,YAAY,MAAM;YAC1C,GAAG,CAAC,OAAO,GAAG,MAAM,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC;QACzD,OAAO;YACH,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,IAAI;QAC1B;IACJ;IACA,OAAO;AACX;AAEA,mDAAmD;AACnD,SAAS,sBAAsB,OAAO,EAAE,IAAI;IACxC,IAAI,CAAC,SACD,OAAO;IACX,OAAO,+BAA+B,KAAK,QAAQ,CAAC,UAAU;AAClE;AAEA,SAAS,6BAA6B,GAAG,EAAE,IAAI;IAC3C,IAAI,MAAM,CAAC;IACX,IAAI,OAAO,OAAO,IAAI,CAAC;IACvB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;QAClC,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,IAAI,SAAS,UAAU,IAAI,CAAC,SAAS,CAAC;QACtC,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM,EAAE,SAAS;QAChE,IAAI,QAAQ,KAAK,MAAM,CAAC,OAAO;QAC/B,IAAI,MAAM,YAAY,YAAY,MAAM;YACpC,GAAG,CAAC,OAAO,GAAG,6BAA6B,GAAG,CAAC,IAAI,EAAE,MAAM,YAAY;QAC3E,OAAO;YACH,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,IAAI;QAC1B;QACA,IAAI,MAAM,QAAQ,IAAI,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,GAAG;YAC/C,GAAG,CAAC,OAAO,GAAG;gBAAC,GAAG,CAAC,OAAO;aAAC;QAC/B;IACJ;IACA,OAAO;AACX;AAEA,mDAAmD;AACnD,SAAS,oBAAoB,OAAO,EAAE,IAAI;IACtC,IAAI,CAAC,SACD,OAAO;IACX,OAAO,KAAK,UAAU,CAAC,6BAA6B,SAAS;AACjE;AAEA,6DAA6D;AAC7D,SAAS,UAAU,IAAI,EAAE,EAAE;IACvB,IAAI,WAAW,KAAK,QAAQ,CAAC,KAAK,CAAC,MAC/B,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,MAC3B,IAAI,GACJ,IAAI,GACJ,IAAI,OAAO,MAAM,GAAG;IACxB,IAAI,CAAC,CAAC,gBAAgB,IAAI,KAAK,cAAc,WACzC,MAAO,IAAI,SAAS,MAAM,IAAI,IAAI,KAAK,QAAQ,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,CAAE;QAC9D,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;QACrC,IAAI,UAAU,QAAQ,UAAU,IAC5B;QACJ,EAAE;IACN;SAEA,MAAO,IAAI,SAAS,MAAM,IAAI,IAAI,KAAK,QAAQ,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE;IAC7E,OAAO,OAAO,KAAK,CAAC,GAAG,IAAI,CAAC;AAChC;AAEA,wCAAwC;AACxC,SAAS,WAAW,GAAG;IACnB,OAAO,IAAI,SAAS,CAAC,GAAE,KAChB,IAAI,SAAS,CAAC,GACT,OAAO,CAAC,uBAAuB,SAAS,EAAE,EAAE,EAAE;QAAI,OAAO,MAAM,GAAG,WAAW;IAAI;AACjG;AAEA,SAAS,sBAAsB,cAAc;IACzC,IAAI,eAAe,MAAM,KAAK,YAAY;QACtC,OAAO,eAAe,OAAO;YACzB,KAAK,QAAQ,OAAO,CAAC,YAAY;gBAC7B,OAAO;YACX;gBACI,MAAM,IAAI,MAAM,yBAAyB,eAAe,OAAO;QACvE;IACJ;IACA,IAAI,eAAe,MAAM,KAAK,UAAU;QACpC,OAAO;IACX;IACA,OAAO;AACX;AAEA,SAAS,oBAAoB,OAAO,EAAE,cAAc;IAChD,IAAI,CAAC,SAAS;IACd,IAAI,YAAY,YAAY,YAAY,UAAU;QAC9C,eAAe,MAAM,GAAG;IAC5B,OAAO;QACH,eAAe,MAAM,GAAG;QACxB,OAAO;YACH,KAAK;gBACD,eAAe,OAAO,GAAG,QAAQ,OAAO,CAAC,YAAY;gBACrD;YACJ;gBACI,MAAM,IAAI,MAAM,yBAAyB;QACjD;IACJ;AACJ,EAEA,kBAAkB;CAElB;;;;;;CAMC,IAED;;;;;;CAMC,IAED;;;;;;;;;;;CAWC,IAED;;;;;;;;;;;CAWC,IAED;;;;;;CAMC,IAED;;;;;;CAMC,IAED;;;;;;CAMC,IAED;;;;;;CAMC,IAED;;;;;;CAMC,IAED;;;;;;;;;CASC,IAED;;;;;;CAMC,IAED;;;;;;;;;;;CAWC,IAED;;;;;;CAMC,IAED;;;;;;CAMC,IAED;;;;;;CAMC,IAED;;;;;;CAMC,IAED;;;;;;CAMC,IAED;;;;;;;;;CASC,IAED;;;;;;;;;CASC,IAED;;;;;;;;;CASC", "ignoreList": [0], "debugId": null}}]}
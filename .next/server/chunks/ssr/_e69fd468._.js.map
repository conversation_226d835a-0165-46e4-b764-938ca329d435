{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/lib/sampleData.ts"], "sourcesContent": ["import { collection, addDoc, doc, setDoc } from 'firebase/firestore';\nimport { db } from './firebase';\nimport { CustomRequest, User, ContactMessage } from '@/types';\n\n// Sample templates data\nexport const sampleTemplates = [\n  {\n    title: 'Modern Dashboard',\n    description: 'Clean and modern dashboard template with analytics and data visualization',\n    category: 'Dashboard',\n    price: 49,\n    imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop',\n    previewUrl: '#',\n    downloadUrl: '#',\n    tags: ['React', 'TypeScript', 'Charts', 'Analytics'],\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n    createdBy: 'admin'\n  },\n  {\n    title: 'E-commerce Store',\n    description: 'Complete e-commerce solution with shopping cart and payment integration',\n    category: 'E-commerce',\n    price: 79,\n    imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop',\n    previewUrl: '#',\n    downloadUrl: '#',\n    tags: ['Next.js', 'Stripe', 'Shopping Cart', 'Responsive'],\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n    createdBy: 'admin'\n  },\n  {\n    title: 'Landing Page Pro',\n    description: 'High-converting landing page template for SaaS and startups',\n    category: 'Landing Page',\n    price: 39,\n    imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',\n    previewUrl: '#',\n    downloadUrl: '#',\n    tags: ['HTML', 'CSS', 'JavaScript', 'Conversion'],\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n    createdBy: 'admin'\n  },\n  {\n    title: 'Portfolio Showcase',\n    description: 'Creative portfolio template for designers and developers',\n    category: 'Portfolio',\n    price: 29,\n    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',\n    previewUrl: '#',\n    downloadUrl: '#',\n    tags: ['Vue.js', 'GSAP', 'Animation', 'Creative'],\n    featured: false,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n    createdBy: 'admin'\n  },\n  {\n    title: 'Corporate Website',\n    description: 'Professional corporate website template with multiple pages',\n    category: 'Corporate',\n    price: 59,\n    imageUrl: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop',\n    previewUrl: '#',\n    downloadUrl: '#',\n    tags: ['WordPress', 'PHP', 'Corporate', 'Professional'],\n    featured: false,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n    createdBy: 'admin'\n  },\n  {\n    title: 'Mobile App UI',\n    description: 'Complete mobile app UI kit with 50+ screens',\n    category: 'Mobile App',\n    price: 69,\n    imageUrl: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop',\n    previewUrl: '#',\n    downloadUrl: '#',\n    tags: ['React Native', 'Flutter', 'Mobile', 'UI Kit'],\n    featured: false,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n    createdBy: 'admin'\n  }\n];\n\n// Sample categories data\nexport const sampleCategories = [\n  {\n    name: 'Dashboard',\n    description: 'Admin panels and data visualization templates',\n    imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop',\n    templateCount: 0\n  },\n  {\n    name: 'E-commerce',\n    description: 'Online stores and shopping cart templates',\n    imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop',\n    templateCount: 0\n  },\n  {\n    name: 'Landing Page',\n    description: 'High-converting marketing pages',\n    imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',\n    templateCount: 0\n  },\n  {\n    name: 'Portfolio',\n    description: 'Creative showcases for professionals',\n    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',\n    templateCount: 0\n  },\n  {\n    name: 'Corporate',\n    description: 'Business and company websites',\n    imageUrl: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop',\n    templateCount: 0\n  },\n  {\n    name: 'Mobile App',\n    description: 'Mobile application UI templates',\n    imageUrl: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop',\n    templateCount: 0\n  }\n];\n\n// Function to add sample data to Firestore\nexport const addSampleData = async () => {\n  try {\n    console.log('Adding sample templates...');\n\n    // Add templates\n    for (const template of sampleTemplates) {\n      await addDoc(collection(db, 'templates'), template);\n    }\n\n    console.log('Adding sample categories...');\n\n    // Add categories\n    for (const category of sampleCategories) {\n      await addDoc(collection(db, 'categories'), category);\n    }\n\n    console.log('Adding sample custom requests...');\n\n    // Add custom requests\n    for (const request of sampleCustomRequests) {\n      await addDoc(collection(db, 'customRequests'), request);\n    }\n\n    console.log('Adding sample users...');\n\n    // Add sample users (with generated IDs)\n    for (const userData of sampleUsers) {\n      const userRef = doc(collection(db, 'users'));\n      await setDoc(userRef, { ...userData, uid: userRef.id });\n    }\n\n    console.log('Adding sample contact messages...');\n\n    // Add contact messages\n    for (const message of sampleContactMessages) {\n      await addDoc(collection(db, 'contactMessages'), message);\n    }\n\n    console.log('All sample data added successfully!');\n  } catch (error) {\n    console.error('Error adding sample data:', error);\n    throw error;\n  }\n};\n\n// Sample custom requests data\nexport const sampleCustomRequests: Omit<CustomRequest, 'id'>[] = [\n  {\n    userId: 'user1',\n    userEmail: '<EMAIL>',\n    title: 'Modern SaaS Dashboard',\n    description: 'I need a modern dashboard for my SaaS application with analytics, user management, and billing features.',\n    category: 'Dashboard',\n    budget: 800,\n    deadline: new Date('2024-02-15'),\n    status: 'pending',\n    createdAt: new Date('2024-01-10'),\n    updatedAt: new Date('2024-01-10')\n  },\n  {\n    userId: 'user2',\n    userEmail: '<EMAIL>',\n    title: 'E-commerce Mobile App',\n    description: 'Looking for a mobile-first e-commerce template with product catalog, shopping cart, and payment integration.',\n    category: 'E-commerce',\n    budget: 1200,\n    deadline: new Date('2024-02-20'),\n    status: 'in-progress',\n    createdAt: new Date('2024-01-08'),\n    updatedAt: new Date('2024-01-12'),\n    adminNotes: 'Started working on wireframes and design mockups.'\n  },\n  {\n    userId: 'user3',\n    userEmail: '<EMAIL>',\n    title: 'Portfolio Website',\n    description: 'Creative portfolio website for a photographer with gallery, blog, and contact features.',\n    category: 'Portfolio',\n    budget: 500,\n    status: 'completed',\n    createdAt: new Date('2024-01-05'),\n    updatedAt: new Date('2024-01-15'),\n    adminNotes: 'Completed and delivered. Client very satisfied.'\n  },\n  {\n    userId: 'user4',\n    userEmail: '<EMAIL>',\n    title: 'Corporate Landing Page',\n    description: 'Professional landing page for a consulting firm with services showcase and lead generation forms.',\n    category: 'Landing Page',\n    budget: 600,\n    deadline: new Date('2024-02-10'),\n    status: 'pending',\n    createdAt: new Date('2024-01-12'),\n    updatedAt: new Date('2024-01-12')\n  },\n  {\n    userId: 'user5',\n    userEmail: '<EMAIL>',\n    title: 'Restaurant Website',\n    description: 'Website for a restaurant with menu display, online ordering, and reservation system.',\n    category: 'Other',\n    budget: 900,\n    status: 'cancelled',\n    createdAt: new Date('2024-01-03'),\n    updatedAt: new Date('2024-01-07'),\n    adminNotes: 'Client cancelled due to budget constraints.'\n  }\n];\n\n// Sample users data\nexport const sampleUsers: Omit<User, 'uid'>[] = [\n  {\n    email: '<EMAIL>',\n    role: 'user',\n    fullName: 'John Doe',\n    displayName: 'John',\n    phoneNumber: '1234567890',\n    countryCode: '+1',\n    createdAt: new Date('2024-01-01'),\n    updatedAt: new Date('2024-01-10')\n  },\n  {\n    email: '<EMAIL>',\n    role: 'user',\n    fullName: 'Sarah Smith',\n    displayName: 'Sarah',\n    phoneNumber: '9876543210',\n    countryCode: '+1',\n    createdAt: new Date('2024-01-02'),\n    updatedAt: new Date('2024-01-08')\n  },\n  {\n    email: '<EMAIL>',\n    role: 'user',\n    fullName: 'Mike Johnson',\n    displayName: 'Mike',\n    phoneNumber: '5555555555',\n    countryCode: '+44',\n    createdAt: new Date('2024-01-03'),\n    updatedAt: new Date('2024-01-05')\n  },\n  {\n    email: '<EMAIL>',\n    role: 'user',\n    fullName: 'Lisa Brown',\n    displayName: 'Lisa',\n    phoneNumber: '7777777777',\n    countryCode: '+91',\n    createdAt: new Date('2024-01-04'),\n    updatedAt: new Date('2024-01-12')\n  },\n  {\n    email: '<EMAIL>',\n    role: 'user',\n    fullName: 'David Wilson',\n    displayName: 'David',\n    phoneNumber: '3333333333',\n    countryCode: '+61',\n    createdAt: new Date('2024-01-05'),\n    updatedAt: new Date('2024-01-07')\n  }\n];\n\n// Sample contact messages\nexport const sampleContactMessages: Omit<ContactMessage, 'id'>[] = [\n  {\n    name: 'Alex Thompson',\n    email: '<EMAIL>',\n    subject: 'Question about custom development',\n    message: 'Hi, I\\'m interested in getting a custom template developed. Can you provide more information about your process and pricing?',\n    status: 'unread',\n    createdAt: new Date('2024-01-14')\n  },\n  {\n    name: 'Emma Davis',\n    email: '<EMAIL>',\n    subject: 'Template customization request',\n    message: 'I purchased the Modern Dashboard template and would like to customize the color scheme. Is this service available?',\n    status: 'read',\n    createdAt: new Date('2024-01-13')\n  },\n  {\n    name: 'Ryan Miller',\n    email: '<EMAIL>',\n    subject: 'Technical support needed',\n    message: 'I\\'m having trouble setting up the e-commerce template. The payment integration is not working as expected.',\n    status: 'replied',\n    createdAt: new Date('2024-01-12')\n  }\n];\n\n// Function to create an admin user (call this after signing up)\nexport const makeUserAdmin = async (userId: string) => {\n  try {\n    await setDoc(doc(db, 'users', userId), {\n      role: 'admin'\n    }, { merge: true });\n    console.log('User made admin successfully!');\n  } catch (error) {\n    console.error('Error making user admin:', error);\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AACA;;;AAIO,MAAM,kBAAkB;IAC7B;QACE,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,YAAY;QACZ,aAAa;QACb,MAAM;YAAC;YAAS;YAAc;YAAU;SAAY;QACpD,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;QACf,WAAW;IACb;IACA;QACE,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,YAAY;QACZ,aAAa;QACb,MAAM;YAAC;YAAW;YAAU;YAAiB;SAAa;QAC1D,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;QACf,WAAW;IACb;IACA;QACE,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,YAAY;QACZ,aAAa;QACb,MAAM;YAAC;YAAQ;YAAO;YAAc;SAAa;QACjD,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;QACf,WAAW;IACb;IACA;QACE,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,YAAY;QACZ,aAAa;QACb,MAAM;YAAC;YAAU;YAAQ;YAAa;SAAW;QACjD,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;QACf,WAAW;IACb;IACA;QACE,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,YAAY;QACZ,aAAa;QACb,MAAM;YAAC;YAAa;YAAO;YAAa;SAAe;QACvD,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;QACf,WAAW;IACb;IACA;QACE,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,YAAY;QACZ,aAAa;QACb,MAAM;YAAC;YAAgB;YAAW;YAAU;SAAS;QACrD,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;QACf,WAAW;IACb;CACD;AAGM,MAAM,mBAAmB;IAC9B;QACE,MAAM;QACN,aAAa;QACb,UAAU;QACV,eAAe;IACjB;IACA;QACE,MAAM;QACN,aAAa;QACb,UAAU;QACV,eAAe;IACjB;IACA;QACE,MAAM;QACN,aAAa;QACb,UAAU;QACV,eAAe;IACjB;IACA;QACE,MAAM;QACN,aAAa;QACb,UAAU;QACV,eAAe;IACjB;IACA;QACE,MAAM;QACN,aAAa;QACb,UAAU;QACV,eAAe;IACjB;IACA;QACE,MAAM;QACN,aAAa;QACb,UAAU;QACV,eAAe;IACjB;CACD;AAGM,MAAM,gBAAgB;IAC3B,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,gBAAgB;QAChB,KAAK,MAAM,YAAY,gBAAiB;YACtC,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,cAAc;QAC5C;QAEA,QAAQ,GAAG,CAAC;QAEZ,iBAAiB;QACjB,KAAK,MAAM,YAAY,iBAAkB;YACvC,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,eAAe;QAC7C;QAEA,QAAQ,GAAG,CAAC;QAEZ,sBAAsB;QACtB,KAAK,MAAM,WAAW,qBAAsB;YAC1C,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB;QACjD;QAEA,QAAQ,GAAG,CAAC;QAEZ,wCAAwC;QACxC,KAAK,MAAM,YAAY,YAAa;YAClC,MAAM,UAAU,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACnC,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,SAAS;gBAAE,GAAG,QAAQ;gBAAE,KAAK,QAAQ,EAAE;YAAC;QACvD;QAEA,QAAQ,GAAG,CAAC;QAEZ,uBAAuB;QACvB,KAAK,MAAM,WAAW,sBAAuB;YAC3C,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB;QAClD;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAGO,MAAM,uBAAoD;IAC/D;QACE,QAAQ;QACR,WAAW;QACX,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,QAAQ;QACR,WAAW;QACX,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,YAAY;IACd;IACA;QACE,QAAQ;QACR,WAAW;QACX,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,YAAY;IACd;IACA;QACE,QAAQ;QACR,WAAW;QACX,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,QAAQ;QACR,WAAW;QACX,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,YAAY;IACd;CACD;AAGM,MAAM,cAAmC;IAC9C;QACE,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,wBAAsD;IACjE;QACE,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;IACA;QACE,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;IACA;QACE,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS,SAAS;YACrC,MAAM;QACR,GAAG;YAAE,OAAO;QAAK;QACjB,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/setup/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Loader2, CheckCircle, Database, Users } from 'lucide-react';\nimport { addSampleData } from '@/lib/sampleData';\n\nexport default function AdminSetupPage() {\n  const [loading, setLoading] = useState(false);\n  const [success, setSuccess] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleAddSampleData = async () => {\n    setLoading(true);\n    setError('');\n    setSuccess(false);\n\n    try {\n      await addSampleData();\n      setSuccess(true);\n    } catch (error: any) {\n      setError(error.message || 'Failed to add sample data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"max-w-2xl mx-auto\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Admin Setup\n          </h1>\n          <p className=\"text-gray-600\">\n            Set up your KaleidoneX marketplace with sample data\n          </p>\n        </div>\n\n        <div className=\"space-y-6\">\n          {/* Sample Data Card */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Database className=\"h-5 w-5 mr-2\" />\n                Sample Data\n              </CardTitle>\n              <CardDescription>\n                Add sample templates and categories to get started quickly\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"text-sm text-gray-600\">\n                  This will add:\n                  <ul className=\"list-disc list-inside mt-2 space-y-1\">\n                    <li>6 sample templates across different categories</li>\n                    <li>6 template categories</li>\n                    <li>Professional images from Unsplash</li>\n                  </ul>\n                </div>\n\n                {error && (\n                  <Alert variant=\"destructive\">\n                    <AlertDescription>{error}</AlertDescription>\n                  </Alert>\n                )}\n\n                {success && (\n                  <Alert>\n                    <CheckCircle className=\"h-4 w-4\" />\n                    <AlertDescription>\n                      Sample data added successfully! You can now browse templates on your site.\n                    </AlertDescription>\n                  </Alert>\n                )}\n\n                <Button \n                  onClick={handleAddSampleData} \n                  disabled={loading || success}\n                  className=\"w-full\"\n                >\n                  {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n                  {success ? 'Sample Data Added' : 'Add Sample Data'}\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Instructions Card */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Users className=\"h-5 w-5 mr-2\" />\n                Next Steps\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4 text-sm text-gray-600\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-2\">1. Make yourself an admin</h4>\n                  <p>Go to Firebase Console → Firestore → users collection → find your user → change role to \"admin\"</p>\n                </div>\n                \n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-2\">2. Test the application</h4>\n                  <p>Browse templates, test the search and filtering functionality</p>\n                </div>\n                \n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-2\">3. Customize templates</h4>\n                  <p>Replace sample templates with your own designs and content</p>\n                </div>\n                \n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-2\">4. Set up payments</h4>\n                  <p>Integrate Stripe or your preferred payment processor</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Firebase Setup Card */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Firebase Configuration</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3 text-sm\">\n                <div className=\"flex items-center justify-between\">\n                  <span>Authentication</span>\n                  <span className=\"text-green-600 font-medium\">✓ Configured</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span>Firestore Database</span>\n                  <span className=\"text-green-600 font-medium\">✓ Connected</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span>Security Rules</span>\n                  <span className=\"text-yellow-600 font-medium\">⚠ Check manually</span>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,sBAAsB;QAC1B,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,CAAA,GAAA,wHAAA,CAAA,gBAAa,AAAD;YAClB,WAAW;QACb,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAK/B,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDAAwB;kEAErC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;4CAIP,uBACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DACb,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;8DAAE;;;;;;;;;;;4CAItB,yBACC,8OAAC,iIAAA,CAAA,QAAK;;kEACJ,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC,iIAAA,CAAA,mBAAgB;kEAAC;;;;;;;;;;;;0DAMtB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU,WAAW;gDACrB,WAAU;;oDAET,yBAAW,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAC9B,UAAU,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;sCAOzC,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAItC,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,8OAAC;kEAAE;;;;;;;;;;;;0DAGL,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,8OAAC;kEAAE;;;;;;;;;;;;0DAGL,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,8OAAC;kEAAE;;;;;;;;;;;;0DAGL,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOX,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAK,WAAU;kEAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShE", "debugId": null}}, {"offset": {"line": 1024, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }]];\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('loader-circle', __iconNode);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,6BAA+B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa5F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "file": "circle-check-big.js", "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('circle-check-big', __iconNode);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1109, "column": 0}, "map": {"version": 3, "file": "database.js", "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/lucide-react/src/icons/database.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['ellipse', { cx: '12', cy: '5', rx: '9', ry: '3', key: 'msslwz' }],\n  ['path', { d: 'M3 5V19A9 3 0 0 0 21 19V5', key: '1wlel7' }],\n  ['path', { d: 'M3 12A9 3 0 0 0 21 12', key: 'mv7ke4' }],\n];\n\n/**\n * @component @name Database\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8ZWxsaXBzZSBjeD0iMTIiIGN5PSI1IiByeD0iOSIgcnk9IjMiIC8+CiAgPHBhdGggZD0iTTMgNVYxOUE5IDMgMCAwIDAgMjEgMTlWNSIgLz4KICA8cGF0aCBkPSJNMyAxMkE5IDMgMCAwIDAgMjEgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/database\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Database = createLucideIcon('database', __iconNode);\n\nexport default Database;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAW,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1D;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1165, "column": 0}, "map": {"version": 3, "file": "users.js", "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/node_modules/lucide-react/src/icons/users.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['path', { d: 'M16 3.128a4 4 0 0 1 0 7.744', key: '16gr8j' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n];\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8cGF0aCBkPSJNMTYgMy4xMjhhNCA0IDAgMCAxIDAgNy43NDQiIC8+CiAgPHBhdGggZD0iTTIyIDIxdi0yYTQgNCAwIDAgMC0zLTMuODciIC8+CiAgPGNpcmNsZSBjeD0iOSIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('users', __iconNode);\n\nexport default Users;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3D;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,OAAA;QAAS,CAAA;KAAA;CACvD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/button';\n\nexport const HeroSection = () => {\n  return (\n    <section className=\"bg-white py-12 sm:py-16 lg:py-24\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          {/* Hero Content */}\n          <div className=\"mb-8 sm:mb-12\">\n            <h1 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight\">\n              Premium Templates for\n              <span className=\"text-blue-600\">\n                {\" \"}Your Business\n              </span>\n            </h1>\n            <p className=\"text-base sm:text-lg text-gray-600 mb-6 sm:mb-8 max-w-2xl mx-auto\">\n              Professional, ready-to-use templates that help you build beautiful websites\n              and applications quickly and efficiently.\n            </p>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center mb-12 sm:mb-16\">\n            <Button asChild size=\"lg\" className=\"px-6 sm:px-8 py-3\">\n              <Link href=\"/templates\">\n                Browse Templates\n              </Link>\n            </Button>\n            <Button asChild variant=\"outline\" size=\"lg\" className=\"px-6 sm:px-8 py-3\">\n              <Link href=\"/contact\">\n                Get Started\n              </Link>\n            </Button>\n          </div>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 max-w-3xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\">500+</div>\n              <div className=\"text-sm sm:text-base text-gray-600\">Premium Templates</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\">10,000+</div>\n              <div className=\"text-sm sm:text-base text-gray-600\">Happy Customers</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\">24/7</div>\n              <div className=\"text-sm sm:text-base text-gray-600\">Support</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMO,MAAM,cAAc;IACzB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAsF;kDAElG,8OAAC;wCAAK,WAAU;;4CACb;4CAAI;;;;;;;;;;;;;0CAGT,8OAAC;gCAAE,WAAU;0CAAoE;;;;;;;;;;;;kCAOnF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,MAAK;gCAAK,WAAU;0CAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAa;;;;;;;;;;;0CAI1B,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;0CACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAW;;;;;;;;;;;;;;;;;kCAO1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoD;;;;;;kDACnE,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoD;;;;;;kDACnE,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoD;;;;;;kDACnE,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlE", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/FeaturedTemplates.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardFooter } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Star, Eye, Download, ArrowRight } from 'lucide-react';\n\n// Mock data for featured templates\nconst featuredTemplates = [\n  {\n    id: '1',\n    title: 'Modern Dashboard',\n    description: 'Clean and modern dashboard template with dark mode support',\n    category: 'Dashboard',\n    price: 49,\n    imageUrl: '/api/placeholder/400/300',\n    rating: 4.9,\n    downloads: 1234,\n    featured: true,\n    tags: ['React', 'TypeScript', 'Tailwind']\n  },\n  {\n    id: '2',\n    title: 'E-commerce Store',\n    description: 'Complete e-commerce solution with shopping cart and checkout',\n    category: 'E-commerce',\n    price: 79,\n    imageUrl: '/api/placeholder/400/300',\n    rating: 4.8,\n    downloads: 856,\n    featured: true,\n    tags: ['Next.js', 'Stripe', 'Responsive']\n  },\n  {\n    id: '3',\n    title: 'Landing Page Pro',\n    description: 'High-converting landing page template for SaaS products',\n    category: 'Landing Page',\n    price: 39,\n    imageUrl: '/api/placeholder/400/300',\n    rating: 4.9,\n    downloads: 2341,\n    featured: true,\n    tags: ['HTML', 'CSS', 'JavaScript']\n  },\n  {\n    id: '4',\n    title: 'Portfolio Showcase',\n    description: 'Creative portfolio template for designers and developers',\n    category: 'Portfolio',\n    price: 29,\n    imageUrl: '/api/placeholder/400/300',\n    rating: 4.7,\n    downloads: 1567,\n    featured: true,\n    tags: ['Vue.js', 'GSAP', 'Responsive']\n  }\n];\n\nexport const FeaturedTemplates = () => {\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Popular Templates\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Professional templates trusted by thousands of businesses\n          </p>\n        </div>\n\n        {/* Templates Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\">\n          {featuredTemplates.map((template) => (\n            <Card key={template.id} className=\"group hover:shadow-lg transition-shadow duration-200\">\n              <div className=\"relative overflow-hidden rounded-t-lg\">\n                <Image\n                  src={template.imageUrl}\n                  alt={template.title}\n                  width={400}\n                  height={300}\n                  className=\"w-full h-48 object-cover\"\n                />\n                <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 flex items-center justify-center\">\n                  <div className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\n                    <Button size=\"sm\" variant=\"secondary\">\n                      <Eye className=\"h-4 w-4 mr-1\" />\n                      Preview\n                    </Button>\n                  </div>\n                </div>\n              </div>\n              \n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <Badge variant=\"outline\">{template.category}</Badge>\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <Star className=\"h-4 w-4 fill-yellow-400 text-yellow-400 mr-1\" />\n                    {template.rating}\n                  </div>\n                </div>\n                \n                <h3 className=\"font-semibold text-lg text-gray-900 mb-2\">\n                  {template.title}\n                </h3>\n                \n                <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n                  {template.description}\n                </p>\n                \n                <div className=\"flex flex-wrap gap-1 mb-4\">\n                  {template.tags.slice(0, 2).map((tag) => (\n                    <Badge key={tag} variant=\"secondary\" className=\"text-xs\">\n                      {tag}\n                    </Badge>\n                  ))}\n                  {template.tags.length > 2 && (\n                    <Badge variant=\"secondary\" className=\"text-xs\">\n                      +{template.tags.length - 2}\n                    </Badge>\n                  )}\n                </div>\n                \n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <Download className=\"h-4 w-4 mr-1\" />\n                    {template.downloads.toLocaleString()}\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900\">\n                    ${template.price}\n                  </div>\n                </div>\n              </CardContent>\n              \n              <CardFooter className=\"p-6 pt-0\">\n                <Button asChild className=\"w-full\">\n                  <Link href={`/templates/${template.id}`}>\n                    View Details\n                    <ArrowRight className=\"ml-2 h-4 w-4\" />\n                  </Link>\n                </Button>\n              </CardFooter>\n            </Card>\n          ))}\n        </div>\n\n        {/* View All Button */}\n        <div className=\"text-center\">\n          <Button asChild size=\"lg\" variant=\"outline\">\n            <Link href=\"/templates\">\n              View All Templates\n              <ArrowRight className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AARA;;;;;;;;AAUA,mCAAmC;AACnC,MAAM,oBAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAS;YAAc;SAAW;IAC3C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAW;YAAU;SAAa;IAC3C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAQ;YAAO;SAAa;IACrC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,OAAO;QACP,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAU;YAAQ;SAAa;IACxC;CACD;AAEM,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC,gIAAA,CAAA,OAAI;4BAAmB,WAAU;;8CAChC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,SAAS,QAAQ;4CACtB,KAAK,SAAS,KAAK;4CACnB,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAQ;;sEACxB,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;8CAOxC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW,SAAS,QAAQ;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,SAAS,MAAM;;;;;;;;;;;;;sDAIpB,8OAAC;4CAAG,WAAU;sDACX,SAAS,KAAK;;;;;;sDAGjB,8OAAC;4CAAE,WAAU;sDACV,SAAS,WAAW;;;;;;sDAGvB,8OAAC;4CAAI,WAAU;;gDACZ,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC9B,8OAAC,iIAAA,CAAA,QAAK;wDAAW,SAAQ;wDAAY,WAAU;kEAC5C;uDADS;;;;;gDAIb,SAAS,IAAI,CAAC,MAAM,GAAG,mBACtB,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;;wDAAU;wDAC3C,SAAS,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;sDAK/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,SAAS,SAAS,CAAC,cAAc;;;;;;;8DAEpC,8OAAC;oDAAI,WAAU;;wDAAmC;wDAC9C,SAAS,KAAK;;;;;;;;;;;;;;;;;;;8CAKtB,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,WAAU;kDACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAM,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE;;gDAAE;8DAEvC,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2BAhEnB,SAAS,EAAE;;;;;;;;;;8BAyE1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,MAAK;wBAAK,SAAQ;kCAChC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;gCAAa;8CAEtB,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/CategoriesSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { \n  Monitor, \n  ShoppingCart, \n  Briefcase, \n  Users, \n  FileText, \n  Smartphone,\n  ArrowRight \n} from 'lucide-react';\n\n// Mock data for categories\nconst categories = [\n  {\n    id: '1',\n    name: 'Dashboard',\n    description: 'Admin panels and data visualization templates',\n    icon: Monitor,\n    templateCount: 45,\n    color: 'bg-blue-500'\n  },\n  {\n    id: '2',\n    name: 'E-commerce',\n    description: 'Online store and shopping cart templates',\n    icon: ShoppingCart,\n    templateCount: 32,\n    color: 'bg-green-500'\n  },\n  {\n    id: '3',\n    name: 'Portfolio',\n    description: 'Creative showcases for professionals',\n    icon: Briefcase,\n    templateCount: 28,\n    color: 'bg-purple-500'\n  },\n  {\n    id: '4',\n    name: 'Landing Page',\n    description: 'High-converting marketing pages',\n    icon: FileText,\n    templateCount: 56,\n    color: 'bg-orange-500'\n  },\n  {\n    id: '5',\n    name: 'Corporate',\n    description: 'Business and company websites',\n    icon: Users,\n    templateCount: 23,\n    color: 'bg-indigo-500'\n  },\n  {\n    id: '6',\n    name: 'Mobile App',\n    description: 'Mobile application UI templates',\n    icon: Smartphone,\n    templateCount: 19,\n    color: 'bg-pink-500'\n  }\n];\n\nexport const CategoriesSection = () => {\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Template Categories\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Choose from our organized collection of professional templates\n          </p>\n        </div>\n\n        {/* Categories Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12\">\n          {categories.map((category) => {\n            const IconComponent = category.icon;\n            return (\n              <Link key={category.id} href={`/templates?category=${category.name}`}>\n                <Card className=\"group hover:shadow-lg transition-shadow duration-200 h-full cursor-pointer\">\n                  <CardContent className=\"p-6 text-center\">\n                    <div className={`inline-flex items-center justify-center w-12 h-12 ${category.color} rounded-lg mb-4`}>\n                      <IconComponent className=\"h-6 w-6 text-white\" />\n                    </div>\n\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                      {category.name}\n                    </h3>\n\n                    <p className=\"text-gray-600 text-sm mb-3\">\n                      {category.description}\n                    </p>\n\n                    <div className=\"text-sm text-gray-500\">\n                      <span>{category.templateCount} templates</span>\n                    </div>\n                  </CardContent>\n                </Card>\n              </Link>\n            );\n          })}\n        </div>\n\n        {/* View All Categories Button */}\n        <div className=\"text-center\">\n          <Button asChild size=\"lg\" variant=\"outline\">\n            <Link href=\"/categories\">\n              View All Categories\n              <ArrowRight className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AAgBA,2BAA2B;AAC3B,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,wMAAA,CAAA,UAAO;QACb,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,sNAAA,CAAA,eAAY;QAClB,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,4MAAA,CAAA,YAAS;QACf,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,8MAAA,CAAA,WAAQ;QACd,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,eAAe;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,8MAAA,CAAA,aAAU;QAChB,eAAe;QACf,OAAO;IACT;CACD;AAEM,MAAM,oBAAoB;IAC/B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC;wBACf,MAAM,gBAAgB,SAAS,IAAI;wBACnC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAAmB,MAAM,CAAC,oBAAoB,EAAE,SAAS,IAAI,EAAE;sCAClE,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAW,CAAC,kDAAkD,EAAE,SAAS,KAAK,CAAC,gBAAgB,CAAC;sDACnG,cAAA,8OAAC;gDAAc,WAAU;;;;;;;;;;;sDAG3B,8OAAC;4CAAG,WAAU;sDACX,SAAS,IAAI;;;;;;sDAGhB,8OAAC;4CAAE,WAAU;sDACV,SAAS,WAAW;;;;;;sDAGvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;oDAAM,SAAS,aAAa;oDAAC;;;;;;;;;;;;;;;;;;;;;;;2BAhB3B,SAAS,EAAE;;;;;oBAsB1B;;;;;;8BAIF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,MAAK;wBAAK,SAAQ;kCAChC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;gCAAc;8CAEvB,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 986, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/StatsSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { \n  Download, \n  Star, \n  Users, \n  Globe,\n  ArrowRight,\n  CheckCircle \n} from 'lucide-react';\n\nconst stats = [\n  {\n    icon: Download,\n    value: '50,000+',\n    label: 'Total Downloads',\n    description: 'Templates downloaded by developers worldwide'\n  },\n  {\n    icon: Star,\n    value: '4.9/5',\n    label: 'Average Rating',\n    description: 'Based on 10,000+ customer reviews'\n  },\n  {\n    icon: Users,\n    value: '15,000+',\n    label: 'Happy Customers',\n    description: 'Satisfied developers and designers'\n  },\n  {\n    icon: Globe,\n    value: '120+',\n    label: 'Countries',\n    description: 'Global reach across all continents'\n  }\n];\n\nconst features = [\n  'Premium quality designs',\n  'Regular updates and support',\n  'Mobile-responsive layouts',\n  'Clean, modern code',\n  'Comprehensive documentation',\n  'Lifetime access'\n];\n\nexport const StatsSection = () => {\n  return (\n    <section className=\"py-12 sm:py-16 bg-blue-600 text-white\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl sm:text-3xl font-bold mb-4\">\n            Ready to Get Started?\n          </h2>\n          <p className=\"text-base sm:text-lg lg:text-xl text-blue-100 mb-6 sm:mb-8 max-w-2xl mx-auto\">\n            Join thousands of businesses using our professional templates to build amazing websites.\n          </p>\n\n          <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center\">\n            <Button asChild size=\"lg\" className=\"bg-white text-blue-600 hover:bg-blue-50 px-6 sm:px-8\">\n              <Link href=\"/templates\">\n                Browse Templates\n              </Link>\n            </Button>\n            <Button asChild variant=\"outline\" size=\"lg\" className=\"border-white text-white hover:bg-white/10 px-6 sm:px-8\">\n              <Link href=\"/contact\">\n                Contact Us\n              </Link>\n            </Button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AAAA;AAAA;AAAA;AANA;;;;;AAeA,MAAM,QAAQ;IACZ;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,eAAe;IAC1B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,8OAAC;wBAAE,WAAU;kCAA+E;;;;;;kCAI5F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,MAAK;gCAAK,WAAU;0CAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAa;;;;;;;;;;;0CAI1B,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;0CACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC", "debugId": null}}]}
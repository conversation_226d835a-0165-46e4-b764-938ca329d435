{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebaseServices.ts"], "sourcesContent": ["import { \n  collection, \n  doc, \n  getDocs, \n  getDoc, \n  addDoc, \n  updateDoc, \n  deleteDoc, \n  query, \n  orderBy, \n  limit, \n  where,\n  onSnapshot,\n  Timestamp\n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport { CustomRequest, User, ContactMessage } from '@/types';\n\n// Custom Requests Services\nexport const createCustomRequest = async (requestData: Omit<CustomRequest, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'customRequests'), {\n      ...requestData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating custom request:', error);\n    throw error;\n  }\n};\n\nexport const getCustomRequests = async () => {\n  try {\n    const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n  } catch (error) {\n    console.error('Error fetching custom requests:', error);\n    throw error;\n  }\n};\n\nexport const updateCustomRequestStatus = async (requestId: string, status: CustomRequest['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n    \n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n    \n    await updateDoc(doc(db, 'customRequests', requestId), updateData);\n  } catch (error) {\n    console.error('Error updating custom request:', error);\n    throw error;\n  }\n};\n\n// User Management Services\nexport const getAllUsers = async () => {\n  try {\n    const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n  } catch (error) {\n    console.error('Error fetching users:', error);\n    throw error;\n  }\n};\n\nexport const getUserById = async (userId: string) => {\n  try {\n    const docRef = doc(db, 'users', userId);\n    const docSnap = await getDoc(docRef);\n    \n    if (docSnap.exists()) {\n      return { id: docSnap.id, ...docSnap.data() } as User;\n    } else {\n      throw new Error('User not found');\n    }\n  } catch (error) {\n    console.error('Error fetching user:', error);\n    throw error;\n  }\n};\n\n// Dashboard Statistics Services\nexport const getDashboardStats = async () => {\n  try {\n    const [usersSnapshot, templatesSnapshot, requestsSnapshot] = await Promise.all([\n      getDocs(collection(db, 'users')),\n      getDocs(collection(db, 'templates')),\n      getDocs(collection(db, 'customRequests'))\n    ]);\n\n    const totalUsers = usersSnapshot.size;\n    const totalTemplates = templatesSnapshot.size;\n    const totalRequests = requestsSnapshot.size;\n    \n    // Calculate pending requests\n    const pendingRequests = requestsSnapshot.docs.filter(\n      doc => doc.data().status === 'pending'\n    ).length;\n\n    return {\n      totalUsers,\n      totalTemplates,\n      totalRequests,\n      pendingRequests\n    };\n  } catch (error) {\n    console.error('Error fetching dashboard stats:', error);\n    throw error;\n  }\n};\n\n// Real-time listeners\nexport const subscribeToCustomRequests = (callback: (requests: CustomRequest[]) => void) => {\n  const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const requests = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n    callback(requests);\n  });\n};\n\nexport const subscribeToUsers = (callback: (users: User[]) => void) => {\n  const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const users = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n    callback(users);\n  });\n};\n\n// Contact Messages Services\nexport const getContactMessages = async () => {\n  try {\n    const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n  } catch (error) {\n    console.error('Error fetching contact messages:', error);\n    throw error;\n  }\n};\n\nexport const updateContactMessageStatus = async (messageId: string, status: ContactMessage['status']) => {\n  try {\n    await updateDoc(doc(db, 'contactMessages', messageId), {\n      status,\n      updatedAt: new Date()\n    });\n  } catch (error) {\n    console.error('Error updating contact message:', error);\n    throw error;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAeA;;;AAIO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB;YAC5D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACvE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,4BAA4B,OAAO,WAAmB,QAAiC;IAClG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,kBAAkB,YAAY;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAC9D,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAE7B,IAAI,QAAQ,MAAM,IAAI;YACpB,OAAO;gBAAE,IAAI,QAAQ,EAAE;gBAAE,GAAG,QAAQ,IAAI,EAAE;YAAC;QAC7C,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,CAAC,eAAe,mBAAmB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC7E,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;SACxB;QAED,MAAM,aAAa,cAAc,IAAI;QACrC,MAAM,iBAAiB,kBAAkB,IAAI;QAC7C,MAAM,gBAAgB,iBAAiB,IAAI;QAE3C,6BAA6B;QAC7B,MAAM,kBAAkB,iBAAiB,IAAI,CAAC,MAAM,CAClD,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,KAAK,WAC7B,MAAM;QAER,OAAO;YACL;YACA;YACA;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,4BAA4B,CAAC;IACxC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAEvE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAE9D,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,QAAQ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAGO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACxE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,OAAO,WAAmB;IAClE,IAAI;QACF,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,YAAY;YACrD;YACA,WAAW,IAAI;QACjB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  Users,\n  FileText,\n  ShoppingCart,\n  DollarSign,\n  Plus,\n  Eye,\n  Settings,\n  BarChart3,\n  Database\n} from 'lucide-react';\nimport Link from 'next/link';\nimport {\n  getDashboardStats,\n  getCustomRequests,\n  getAllUsers,\n  updateCustomRequestStatus,\n  subscribeToCustomRequests\n} from '@/lib/firebaseServices';\nimport { CustomRequest, User } from '@/types';\n\ninterface DashboardStats {\n  totalUsers: number;\n  totalTemplates: number;\n  totalRequests: number;\n  pendingRequests: number;\n}\n\nexport default function AdminDashboard() {\n  const { user, userData } = useAuth();\n  const [stats, setStats] = useState<DashboardStats>({\n    totalUsers: 0,\n    totalTemplates: 0,\n    totalRequests: 0,\n    pendingRequests: 0\n  });\n  const [customRequests, setCustomRequests] = useState<CustomRequest[]>([]);\n  const [recentUsers, setRecentUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Fetch dashboard stats\n        const dashboardStats = await getDashboardStats();\n        setStats(dashboardStats);\n\n        // Fetch custom requests\n        const requests = await getCustomRequests();\n        setCustomRequests(requests.slice(0, 5)); // Show only recent 5\n\n        // Fetch recent users\n        const users = await getAllUsers();\n        setRecentUsers(users.slice(0, 5)); // Show only recent 5\n\n      } catch (error: any) {\n        console.error('Error fetching admin data:', error);\n        setError('Failed to load dashboard data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (user && userData?.role === 'admin') {\n      fetchData();\n\n      // Set up real-time listener for custom requests\n      const unsubscribe = subscribeToCustomRequests((requests) => {\n        setCustomRequests(requests.slice(0, 5));\n      });\n\n      return () => unsubscribe();\n    }\n  }, [user, userData]);\n\n  const handleUpdateRequestStatus = async (requestId: string, status: CustomRequest['status']) => {\n    try {\n      await updateCustomRequestStatus(requestId, status);\n      // The real-time listener will update the UI automatically\n    } catch (error: any) {\n      console.error('Error updating request status:', error);\n      setError('Failed to update request status');\n    }\n  };\n\n  if (!user || userData?.role !== 'admin') {\n    return (\n      <div className=\"container mx-auto px-4 py-20 text-center\">\n        <h1 className=\"text-2xl font-bold mb-4\">Access Denied</h1>\n        <p className=\"text-gray-600 mb-4\">You need admin privileges to access this page.</p>\n        <Button asChild>\n          <Link href=\"/dashboard\">Go to Dashboard</Link>\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\">\n      {/* Header */}\n      <div className=\"mb-6 sm:mb-8\">\n        <h1 className=\"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\">\n          Admin Dashboard\n        </h1>\n        <p className=\"text-sm sm:text-base text-gray-600\">\n          Manage templates, orders, users, and monitor your marketplace performance.\n        </p>\n      </div>\n\n      {/* Loading State */}\n      {loading && (\n        <div className=\"flex justify-center items-center py-12\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Loading dashboard data...</p>\n          </div>\n        </div>\n      )}\n\n      {/* Error State */}\n      {error && (\n        <div className=\"mb-8 p-4 bg-red-50 border border-red-200 rounded-lg\">\n          <p className=\"text-red-800\">{error}</p>\n        </div>\n      )}\n\n      {/* Stats Cards */}\n      {!loading && (\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8\">\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Users</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.totalUsers}</p>\n                </div>\n                <div className=\"p-3 bg-blue-100 rounded-lg\">\n                  <Users className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Templates</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.totalTemplates}</p>\n                </div>\n                <div className=\"p-3 bg-green-100 rounded-lg\">\n                  <FileText className=\"h-6 w-6 text-green-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Custom Requests</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.totalRequests}</p>\n                </div>\n                <div className=\"p-3 bg-yellow-100 rounded-lg\">\n                  <ShoppingCart className=\"h-6 w-6 text-yellow-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Pending Requests</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.pendingRequests}</p>\n                </div>\n                <div className=\"p-3 bg-purple-100 rounded-lg\">\n                  <DollarSign className=\"h-6 w-6 text-purple-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {!loading && (\n        <div className=\"grid lg:grid-cols-3 gap-8\">\n          {/* Custom Requests */}\n          <div className=\"lg:col-span-2\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Recent Custom Requests</CardTitle>\n                <CardDescription>\n                  Latest custom design requests requiring your attention\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {customRequests.length > 0 ? (\n                    customRequests.map((request) => (\n                      <div key={request.id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                        <div className=\"flex-1\">\n                          <h4 className=\"font-medium text-gray-900\">{request.title}</h4>\n                          <p className=\"text-sm text-gray-600 mb-1\">\n                            {request.userEmail} • {new Date(request.createdAt).toLocaleDateString()}\n                          </p>\n                          <p className=\"text-sm text-gray-500 line-clamp-2\">\n                            {request.description}\n                          </p>\n                          <div className=\"flex items-center mt-2 space-x-2\">\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              {request.category}\n                            </Badge>\n                            {request.budget && (\n                              <Badge variant=\"outline\" className=\"text-xs\">\n                                ${request.budget}\n                              </Badge>\n                            )}\n                          </div>\n                        </div>\n                        <div className=\"flex items-center space-x-4\">\n                          <Badge variant={\n                            request.status === 'completed' ? 'default' :\n                            request.status === 'in-progress' ? 'secondary' :\n                            request.status === 'cancelled' ? 'destructive' : 'outline'\n                          }>\n                            {request.status}\n                          </Badge>\n                          <div className=\"flex space-x-2\">\n                            <Button size=\"sm\" variant=\"outline\">\n                              View\n                            </Button>\n                            {request.status === 'pending' && (\n                              <>\n                                <Button\n                                  size=\"sm\"\n                                  onClick={() => handleUpdateRequestStatus(request.id, 'in-progress')}\n                                >\n                                  Accept\n                                </Button>\n                                <Button\n                                  size=\"sm\"\n                                  variant=\"destructive\"\n                                  onClick={() => handleUpdateRequestStatus(request.id, 'cancelled')}\n                                >\n                                  Decline\n                                </Button>\n                              </>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    ))\n                  ) : (\n                    <div className=\"text-center py-8\">\n                      <p className=\"text-gray-500\">No custom requests yet</p>\n                    </div>\n                  )}\n                </div>\n                <div className=\"mt-6\">\n                  <Button asChild variant=\"outline\" className=\"w-full\">\n                    <Link href=\"/admin/custom-requests\">View All Requests</Link>\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Recent Users */}\n            <Card className=\"mt-6\">\n              <CardHeader>\n                <CardTitle>Recent Users</CardTitle>\n                <CardDescription>\n                  Latest user registrations\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {recentUsers.length > 0 ? (\n                    recentUsers.map((user) => (\n                      <div key={user.uid} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                        <div className=\"flex-1\">\n                          <h4 className=\"font-medium text-gray-900\">\n                            {user.fullName || user.displayName || 'No name'}\n                          </h4>\n                          <p className=\"text-sm text-gray-600\">{user.email}</p>\n                          <p className=\"text-xs text-gray-500\">\n                            Joined {new Date(user.createdAt).toLocaleDateString()}\n                          </p>\n                          {user.phoneNumber && (\n                            <p className=\"text-xs text-gray-500\">\n                              {user.countryCode} {user.phoneNumber}\n                            </p>\n                          )}\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>\n                            {user.role}\n                          </Badge>\n                        </div>\n                      </div>\n                    ))\n                  ) : (\n                    <div className=\"text-center py-8\">\n                      <p className=\"text-gray-500\">No users yet</p>\n                    </div>\n                  )}\n                </div>\n                <div className=\"mt-6\">\n                  <Button asChild variant=\"outline\" className=\"w-full\">\n                    <Link href=\"/admin/users\">View All Users</Link>\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Quick Actions */}\n          <div>\n            <Card>\n              <CardHeader>\n                <CardTitle>Quick Actions</CardTitle>\n                <CardDescription>\n                  Common admin tasks\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <Button asChild className=\"w-full justify-start\">\n                  <Link href=\"/admin/setup\">\n                    <Plus className=\"mr-2 h-4 w-4\" />\n                    Setup Sample Data\n                  </Link>\n                </Button>\n\n                <Button asChild variant=\"outline\" className=\"w-full justify-start\">\n                  <Link href=\"/admin/custom-requests\">\n                    <FileText className=\"mr-2 h-4 w-4\" />\n                    Manage Custom Requests\n                  </Link>\n                </Button>\n\n                <Button asChild variant=\"outline\" className=\"w-full justify-start\">\n                  <Link href=\"/admin/users\">\n                    <Users className=\"mr-2 h-4 w-4\" />\n                    Manage Users\n                  </Link>\n                </Button>\n\n                <Button asChild variant=\"outline\" className=\"w-full justify-start\">\n                  <Link href=\"/templates\">\n                    <Eye className=\"mr-2 h-4 w-4\" />\n                    View Templates\n                  </Link>\n                </Button>\n\n                <Button asChild variant=\"outline\" className=\"w-full justify-start\">\n                  <Link href=\"/admin/analytics\">\n                    <BarChart3 className=\"mr-2 h-4 w-4\" />\n                    Analytics\n                  </Link>\n                </Button>\n\n                <Button asChild variant=\"outline\" className=\"w-full justify-start\">\n                  <Link href=\"/admin/settings\">\n                    <Settings className=\"mr-2 h-4 w-4\" />\n                    Settings\n                  </Link>\n                </Button>\n              </CardContent>\n            </Card>\n\n            {/* System Status */}\n            <Card className=\"mt-6\">\n              <CardHeader>\n                <CardTitle>System Status</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center\">\n                      <Database className=\"w-4 h-4 mr-2 text-green-600\" />\n                      <span className=\"text-sm text-gray-600\">Firebase</span>\n                    </div>\n                    <Badge variant=\"default\" className=\"bg-green-500\">Connected</Badge>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center\">\n                      <Database className=\"w-4 h-4 mr-2 text-green-600\" />\n                      <span className=\"text-sm text-gray-600\">Firestore</span>\n                    </div>\n                    <Badge variant=\"default\" className=\"bg-green-500\">Active</Badge>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center\">\n                      <Users className=\"w-4 h-4 mr-2 text-green-600\" />\n                      <span className=\"text-sm text-gray-600\">Authentication</span>\n                    </div>\n                    <Badge variant=\"default\" className=\"bg-green-500\">Working</Badge>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center\">\n                      <FileText className=\"w-4 h-4 mr-2 text-gray-600\" />\n                      <span className=\"text-sm text-gray-600\">Total Requests</span>\n                    </div>\n                    <span className=\"text-sm text-gray-900\">{stats.totalRequests}</span>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAnBA;;;;;;;;;;AAmCe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,YAAY;QACZ,gBAAgB;QAChB,eAAe;QACf,iBAAiB;IACnB;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,WAAW;gBAEX,wBAAwB;gBACxB,MAAM,iBAAiB,MAAM,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;gBAC7C,SAAS;gBAET,wBAAwB;gBACxB,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;gBACvC,kBAAkB,SAAS,KAAK,CAAC,GAAG,KAAK,qBAAqB;gBAE9D,qBAAqB;gBACrB,MAAM,QAAQ,MAAM,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;gBAC9B,eAAe,MAAM,KAAK,CAAC,GAAG,KAAK,qBAAqB;YAE1D,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA,IAAI,QAAQ,UAAU,SAAS,SAAS;YACtC;YAEA,gDAAgD;YAChD,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,4BAAyB,AAAD,EAAE,CAAC;gBAC7C,kBAAkB,SAAS,KAAK,CAAC,GAAG;YACtC;YAEA,OAAO,IAAM;QACf;IACF,GAAG;QAAC;QAAM;KAAS;IAEnB,MAAM,4BAA4B,OAAO,WAAmB;QAC1D,IAAI;YACF,MAAM,CAAA,GAAA,8HAAA,CAAA,4BAAyB,AAAD,EAAE,WAAW;QAC3C,0DAA0D;QAC5D,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACX;IACF;IAEA,IAAI,CAAC,QAAQ,UAAU,SAAS,SAAS;QACvC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAClC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,OAAO;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAa;;;;;;;;;;;;;;;;;IAIhC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoD;;;;;;kCAGlE,8OAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;YAMnD,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;YAMlC,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;YAKhC,CAAC,yBACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAoC,MAAM,UAAU;;;;;;;;;;;;kDAEnE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMzB,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAoC,MAAM,cAAc;;;;;;;;;;;;kDAEvE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM5B,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAoC,MAAM,aAAa;;;;;;;;;;;;kDAEtE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMhC,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAoC,MAAM,eAAe;;;;;;;;;;;;kDAExE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQjC,CAAC,yBACA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DACZ,eAAe,MAAM,GAAG,IACvB,eAAe,GAAG,CAAC,CAAC,wBAClB,8OAAC;wDAAqB,WAAU;;0EAC9B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAA6B,QAAQ,KAAK;;;;;;kFACxD,8OAAC;wEAAE,WAAU;;4EACV,QAAQ,SAAS;4EAAC;4EAAI,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;kFAEvE,8OAAC;wEAAE,WAAU;kFACV,QAAQ,WAAW;;;;;;kFAEtB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;0FAChC,QAAQ,QAAQ;;;;;;4EAElB,QAAQ,MAAM,kBACb,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;;oFAAU;oFACzC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;0EAKxB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SACL,QAAQ,MAAM,KAAK,cAAc,YACjC,QAAQ,MAAM,KAAK,gBAAgB,cACnC,QAAQ,MAAM,KAAK,cAAc,gBAAgB;kFAEhD,QAAQ,MAAM;;;;;;kFAEjB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kIAAA,CAAA,SAAM;gFAAC,MAAK;gFAAK,SAAQ;0FAAU;;;;;;4EAGnC,QAAQ,MAAM,KAAK,2BAClB;;kGACE,8OAAC,kIAAA,CAAA,SAAM;wFACL,MAAK;wFACL,SAAS,IAAM,0BAA0B,QAAQ,EAAE,EAAE;kGACtD;;;;;;kGAGD,8OAAC,kIAAA,CAAA,SAAM;wFACL,MAAK;wFACL,SAAQ;wFACR,SAAS,IAAM,0BAA0B,QAAQ,EAAE,EAAE;kGACtD;;;;;;;;;;;;;;;;;;;;;uDA5CD,QAAQ,EAAE;;;;8EAsDtB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;0DAInC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAC,SAAQ;oDAAU,WAAU;8DAC1C,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO5C,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DACZ,YAAY,MAAM,GAAG,IACpB,YAAY,GAAG,CAAC,CAAC,qBACf,8OAAC;wDAAmB,WAAU;;0EAC5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFACX,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAI;;;;;;kFAExC,8OAAC;wEAAE,WAAU;kFAAyB,KAAK,KAAK;;;;;;kFAChD,8OAAC;wEAAE,WAAU;;4EAAwB;4EAC3B,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;oEAEpD,KAAK,WAAW,kBACf,8OAAC;wEAAE,WAAU;;4EACV,KAAK,WAAW;4EAAC;4EAAE,KAAK,WAAW;;;;;;;;;;;;;0EAI1C,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAS,KAAK,IAAI,KAAK,UAAU,YAAY;8EACjD,KAAK,IAAI;;;;;;;;;;;;uDAjBN,KAAK,GAAG;;;;8EAuBpB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;0DAInC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAC,SAAQ;oDAAU,WAAU;8DAC1C,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpC,8OAAC;;0CACC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,WAAU;0DACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAKrC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,SAAQ;gDAAU,WAAU;0DAC1C,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAKzC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,SAAQ;gDAAU,WAAU;0DAC1C,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAKtC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,SAAQ;gDAAU,WAAU;0DAC1C,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAKpC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,SAAQ;gDAAU,WAAU;0DAC1C,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,kNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAK1C,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,SAAQ;gDAAU,WAAU;0DAC1C,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7C,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;sEAE1C,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAe;;;;;;;;;;;;8DAEpD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;sEAE1C,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAe;;;;;;;;;;;;8DAEpD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;sEAE1C,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAe;;;;;;;;;;;;8DAEpD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;sEAE1C,8OAAC;4DAAK,WAAU;sEAAyB,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhF", "debugId": null}}]}